# Pre-commit 配置文件
# 在每次 git commit 前自动运行核心测试，防止引入回归错误

repos:
  # Python 代码格式化
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.11
        files: '^src/.*\.py$'
  
  # Python 代码检查
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.1.0
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
        files: '^src/.*\.py$'
  
  # 核心路径测试（最重要）
  - repo: local
    hooks:
      - id: critical-path-test
        name: 核心路径测试 (Docker隔离环境)
        description: 运行核心功能测试，防止 AttributeError 等集成错误
        entry: bash scripts/run_core_test.sh
        language: system
        pass_filenames: false
        # 只在修改了关键文件时运行
        files: '^src/ai_live_streamer/(services|models|api|core)/.*\.py$'
        verbose: true
        stages: [commit]
      
      # 快速语法检查（可选，但有用）
      - id: python-syntax-check
        name: Python 语法检查
        entry: python -m py_compile
        language: system
        files: '\.py$'
        pass_filenames: true
        stages: [commit]

# 配置选项
default_stages: [commit]
fail_fast: false  # 不要在第一个错误时停止，运行所有检查
minimum_pre_commit_version: '3.0.0'

# 使用说明：
# 1. 安装: pip install pre-commit
# 2. 初始化: pre-commit install
# 3. 手动运行: pre-commit run --all-files
# 4. 跳过检查（紧急情况）: git commit --no-verify
#    注意：作为 AI 执行者，永远不应使用 --no-verify