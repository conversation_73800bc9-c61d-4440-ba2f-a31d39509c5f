# AI Live Streamer 用户使用指南

> 🎯 **10分钟快速上手** - 从零开始创建你的AI驱动直播解说系统

## 📋 快速导航

- [系统介绍](#系统介绍) - 了解AI直播系统的核心价值
- [使用前准备](#使用前准备) - 5分钟环境配置
- [完整操作流程](#完整操作流程) - 分步骤详细指南
- [功能使用手册](#功能使用手册) - 各功能模块详解
- [常见问题解答](#常见问题解答) - 快速解决使用疑问

---

## 🎯 系统介绍

### 什么是 AI Live Streamer？

AI Live Streamer 是一个智能化的直播解说系统，能够：

- **🤖 AI自动解说**: 根据产品信息自动生成专业直播内容
- **🎵 实时语音合成**: 将文本内容转换为自然流畅的语音
- **💬 智能互动回复**: 自动识别并回答观众问题
- **🎭 个性化人设**: 多种主播风格供您选择
- **⚡ 零技术门槛**: 通过简单的Web界面操作

### 适用场景

| 使用场景 | 应用说明 | 典型时长 |
|---------|---------|---------|
| **电商直播** | 产品展示、卖点介绍、促销活动 | 60-120分钟 |
| **新品发布** | 新产品介绍、功能演示、用户答疑 | 45-90分钟 |
| **教育培训** | 知识讲解、技能演示、在线答疑 | 30-60分钟 |
| **品牌宣传** | 品牌故事、企业文化、产品推广 | 30-90分钟 |

---

## 🛠️ 使用前准备

### 环境要求

> ⚠️ **重要**: 请确保您的环境满足以下要求

- **操作系统**: Windows 10+ / macOS 10.15+ / Linux
- **浏览器**: Chrome 90+ / Firefox 88+ / Safari 14+
- **网络**: 稳定的互联网连接
- **设备**: 推荐使用电脑端进行配置

### 5分钟快速启动

#### 步骤1: 启动系统

```bash
# 1. 激活环境
conda activate aidev

# 2. 启动服务
python scripts/run_server.py

# 3. 打开浏览器访问
# http://localhost:8000
```

#### 步骤2: 检查系统状态

访问 `http://localhost:8000/health` 确认系统正常运行。

正常返回示例：
```json
{
  "status": "healthy",
  "timestamp": **********.123,
  "version": "0.1.0",
  "environment": "development"
}
```

---

## 📝 完整操作流程

### 总体流程图

```
开始使用 → 填写基础信息 → 配置商品 → 设置卖点 → 选择人设 → 系统生成 → 开始直播
   ↓           ↓          ↓       ↓       ↓        ↓        ↓
 2分钟        3分钟       5分钟    3分钟    2分钟     自动      实时
```

### 第一步: 访问配置界面

1. 打开浏览器，访问 `http://localhost:8000`
2. 您将看到"AI直播运营配置系统"界面
3. 系统包含6个配置步骤，完成度实时显示

### 第二步: 基础信息配置 (2分钟)

> 📍 **目标**: 设置直播的基本参数

**必填信息**:
- **直播标题**: 吸引观众的标题，建议包含产品关键词
  ```
  ✅ 好例子: "新款智能手表首发！超长续航+健康监测"
  ❌ 避免: "直播测试"
  ```

- **直播类型**: 选择最符合您需求的类型
  - `新品发布`: 适合新产品首次推广
  - `日常直播`: 适合常规销售直播
  - `限时特卖`: 适合促销活动
  - `特殊活动`: 适合节日或特别活动
  - `问答互动`: 适合以互动为主的直播

- **计划时长**: 建议60-120分钟
  - 60分钟: 适合新手或简单产品
  - 90分钟: 适合复杂产品或多互动
  - 120分钟: 适合深度介绍或多产品

**推荐设置**:
- ✅ 启用自动互动（推荐）
- 🌍 选择目标时区（默认北京时间）

### 第三步: 商品信息配置 (3分钟)

> 📍 **目标**: 详细描述要推广的商品

**核心信息**:
```
主推商品SKU*: ABC-123-XL
商品名称*: 智能运动手表 Pro
品牌*: TechWatch
商品类别*: 电子产品/智能穿戴
当前售价*: 599.00
原价: 799.00 (如有优惠)
```

**关键规格** (每行一个):
```
颜色：午夜黑/银色/玫瑰金
表盘：1.4英寸AMOLED彩屏
续航：正常使用7天，省电模式30天
防水：5ATM专业防水
材质：航空级铝合金表体
```

> 💡 **小贴士**: 规格描述要具体且容易理解，避免过于技术性的术语

### 第四步: 卖点结构配置 (5分钟)

> 📍 **目标**: 构建有说服力的销售论点体系

#### 4.1 主价值主张
用1-2句话概括产品的核心价值：
```
✅ 好例子: 
"专业级健康监测，超长续航设计，为您的健康生活提供24小时贴心守护，性价比超越同级产品。"

❌ 避免: 
"这是一款不错的手表。"
```

#### 4.2 核心卖点配置
点击"添加卖点"按钮，建议配置3-5个卖点：

**卖点 #1: 超长续航 (高优先级)**
- 标题: 超长续航，告别频繁充电
- 描述: 正常使用7天续航，省电模式可达30天，比同类产品续航提升2倍
- 支撑事实:
  ```
  实验室测试数据：连续心率监测7天
  用户真实反馈：平均使用8天才需充电
  对比测试：比Apple Watch续航提升150%
  ```
- ✅ 事实已验证

**卖点 #2: 专业健康监测 (高优先级)**
- 标题: 专业健康监测，守护您的健康
- 描述: 24小时心率监测、血氧饱和度检测、睡眠质量分析，如私人健康助手
- 支撑事实:
  ```
  医疗级传感器，精度达±1%
  获得FDA认证的健康算法
  支持50+种运动模式检测
  ```

**卖点 #3: 时尚外观设计 (中优先级)**
- 标题: 时尚设计，彰显个人品味
- 描述: 航空级铝合金材质，1.4英寸高清彩屏，多种表带搭配，适合各种场合
- 支撑事实:
  ```
  荣获红点设计大奖
  与知名设计师合作设计
  提供20+种表带选择
  ```

#### 4.3 竞争优势
```
比竞品A便宜30%，功能更全面
独有的双芯片技术，续航业界领先
提供2年质保，比标准质保延长1年
```

#### 4.4 行动号召语
```
限时特价，立即下单享受早鸟优惠
库存有限，前100名购买送专属表带
点击下方链接，30天无理由退换
```

### 第五步: 人设配置 (2分钟)

> 📍 **目标**: 选择合适的AI主播风格

#### 5.1 选择人设模板

系统提供多种人设选择，点击卡片选择：

| 人设类型 | 适用场景 | 语言特点 |
|---------|---------|---------|
| **活力主播** | 时尚数码、运动产品 | 热情洋溢、语速适中、善用感叹词 |
| **专业顾问** | 高端产品、B2B销售 | 理性专业、逻辑清晰、术语准确 |
| **邻家姐姐** | 生活用品、母婴产品 | 亲切温和、贴心细致、生活化表达 |
| **潮流达人** | 美妆时尚、潮流单品 | 时尚前卫、活力四射、流行词汇 |

#### 5.2 语音调节
- **语速调节**: 0.5-2.0倍速（推荐1.0-1.2倍）
- **能量等级**: 
  - 低能量：适合深度介绍、理性分析
  - 中等能量：适合大多数场景（推荐）
  - 高能量：适合促销活动、热门产品

#### 5.3 自定义开场白（可选）
```
大家好，欢迎来到我的直播间！
今天要给大家推荐一款超棒的智能手表
相信大家都在寻找既时尚又实用的智能穿戴设备
```

### 第六步: 高级设置 (可选)

> 📍 **目标**: 微调系统表现，优化直播效果

**推荐配置**:
- ✅ 启用实时事实核查（确保信息准确）
- ✅ 启用实时价格更新（价格信息始终最新）
- ✅ 启用内容多样性检查（避免重复内容）
- **问答超时时间**: 300秒（5分钟）

### 第七步: 审核与提交

> 📍 **目标**: 最终检查配置，提交系统处理

1. **自动验证**: 系统会自动检查配置完整性
2. **人工确认**: 
   - ✅ 内容审核已完成
   - ✅ 合规性审核已完成
3. **提交配置**: 点击"提交配置"按钮

**提交成功后您将看到**:
```
✅ 配置提交成功！
📊 处理状态：已完成
⏱️ 处理时间：3.2秒
🎯 内容生成：已完成
🎭 人设配置：已完成
📝 内容预览：[系统生成的直播脚本预览]
```

---

## 🚀 功能使用手册

### 直播控制功能

提交配置后，您可以通过控制面板管理直播：

#### 访问控制面板
访问 `http://localhost:8000/control` 进入直播控制面板

#### 基本控制操作

**开始直播**:
```http
POST /api/control/start
```
- 启动AI解说系统
- 开始TTS语音合成
- 激活观众互动功能

**暂停/恢复**:
```http
POST /api/control/pause   # 暂停直播
POST /api/control/resume  # 恢复直播
```

**停止直播**:
```http
POST /api/control/stop
```

#### 实时互动管理

**添加观众问题**:
```http
POST /api/control/question
{
  "question": "这款手表防水吗？",
  "priority": "high"  // high/normal/low
}
```

**优先级说明**:
- `high`: 立即打断当前解说进行回答
- `normal`: 在适当时机插入回答
- `low`: 排队等待回答

#### 实时状态监控

访问 `http://localhost:8000/status` 查看直播状态：

```json
{
  "status": "narrating",           // 当前状态
  "current_segment": 3,            // 当前段落
  "total_segments": 8,             // 总段落数
  "progress_percentage": 37.5,     // 完成进度
  "question_queue_length": 2,      // 问题队列长度
  "tts_queue_stats": {             // TTS状态
    "queue_size": 5,
    "active_requests": 2
  },
  "audio_player_stats": {          // 音频播放状态
    "queue_length": 3,
    "playing": true
  }
}
```

### 脚本预览功能

提交配置后，系统会生成直播脚本预览，访问 `http://localhost:8000/api/script-preview` 查看：

```json
{
  "script_sections": [
    {
      "section_type": "opening",
      "duration_seconds": 180,
      "content": "大家好，欢迎来到我的直播间！今天要为大家介绍一款..."
    },
    {
      "section_type": "product_intro",
      "duration_seconds": 600,
      "content": "这款智能手表采用了最新的..."
    }
  ],
  "total_duration_minutes": 75,
  "interaction_points": [
    "产品使用场景咨询",
    "价格优惠政策说明",
    "售后服务保障介绍"
  ]
}
```

### 语音合成功能独立使用

如果您只想使用TTS功能：

```python
# Python 代码示例
from ai_live_streamer.services.tts_manager import get_tts_manager

# 获取TTS管理器
tts = get_tts_manager()
await tts.start()

# 合成单句
chunk_id = await tts.synthesize_text(
    text="欢迎大家来到我们的直播间",
    priority="high"
)

# 等待合成完成
audio_chunk = await tts.wait_for_chunk(chunk_id, timeout_sec=5.0)

# 获取音频数据
audio_data = audio_chunk.audio_data
```

### 自动回复功能独立使用

```python
# Python 代码示例  
from ai_live_streamer.agents.qa_handler import create_qa_handler

# 创建问答处理器
qa_handler = create_qa_handler(search_engine)

# 处理问题
response = await qa_handler.handle_question(
    question="这款手表续航怎么样？",
    context=current_context
)

print(f"AI回复: {response.answer}")
```

---

## ❓ 常见问题解答

### 系统配置相关

**Q: 系统启动失败怎么办？**

A: 请按以下步骤排查：
1. 确认已激活conda环境：`conda activate aidev`
2. 检查环境变量配置是否完整
3. 确认Elasticsearch和Redis服务是否启动
4. 查看启动日志中的具体错误信息

**Q: 无法访问Web界面**

A: 请检查：
1. 服务是否成功启动（看到"Application startup complete"）
2. 端口8000是否被占用
3. 防火墙是否阻止了访问
4. 尝试访问 `http://127.0.0.1:8000` 而不是 `localhost`

### 配置使用相关

**Q: 需要哪些信息才能配置直播？**

A: 最少需要：
- 产品名称和基本信息
- 3个以上产品卖点
- 产品价格
- 目标直播时长

其他信息可以使用系统默认值。

**Q: 如何选择合适的人设？**

A: 根据您的产品类型和目标用户：
- **数码产品** → 活力主播或专业顾问
- **美妆时尚** → 潮流达人
- **生活用品** → 邻家姐姐
- **高端商品** → 专业顾问

**Q: 卖点不够3个怎么办？**

A: 可以从这些角度挖掘卖点：
- 产品功能特色
- 性价比优势
- 品牌信誉保障
- 售后服务
- 用户口碑
- 设计外观

### 直播运行相关

**Q: 直播过程中可以修改配置吗？**

A: 可以进行以下调整：
- ✅ 暂停/恢复直播
- ✅ 添加观众问题
- ✅ 调整语速（需重启）
- ❌ 不能修改基础产品信息
- ❌ 不能更换人设（需重新配置）

**Q: AI回复不准确怎么办？**

A: 请检查：
1. 产品信息是否详细完整
2. 卖点描述是否准确
3. 支撑事实是否真实可信
4. 可以手动添加补充信息到知识库

**Q: 语音合成速度慢怎么办？**

A: 优化建议：
1. 检查网络连接状态
2. 确认TTS服务配置正确
3. 适当调整并发请求数量
4. 使用本地TTS引擎（如Edge TTS）

### 技术问题

**Q: 支持哪些文件格式的产品资料？**

A: 系统支持：
- 📄 PDF文件
- 📝 Word文档 (.docx)
- 📋 文本文件 (.txt)
- 🌐 Markdown文件 (.md)

**Q: 可以同时运行多个直播吗？**

A: 当前版本支持单个直播实例。如需多路直播，请：
1. 使用不同端口启动多个服务实例
2. 为每个实例配置独立的数据库连接
3. 确保服务器资源充足

**Q: 如何备份配置？**

A: 系统会自动保存配置到数据库。手动备份方法：
1. 导出表单数据：`GET /api/operational/forms/{form_id}`
2. 保存人设配置
3. 备份知识库内容

### 性能优化

**Q: 如何提升直播效果？**

A: 优化建议：
1. **内容质量**：详细填写产品信息，确保卖点真实有效
2. **人设匹配**：选择与产品和目标用户匹配的人设
3. **互动设计**：预设常见问题，快速响应观众
4. **节奏控制**：合理设置直播时长，避免内容冗余

**Q: 系统性能监控**

A: 关键指标监控：
- TTS合成延迟：< 800ms
- 问答响应时间：< 3秒  
- 内容生成延迟：< 2秒
- 音频播放流畅度：无卡顿

访问 `http://localhost:8000/api/status` 实时查看性能指标。

---

## 📞 技术支持

### 获取帮助

如果您遇到问题或需要技术支持：

1. **查看日志**: 系统运行日志包含详细的错误信息
2. **API文档**: 访问 `http://localhost:8000/docs` 查看完整API文档
3. **示例配置**: 参考 `tests/` 目录下的测试用例

### 反馈渠道

- 🐛 **Bug反馈**: 在GitHub Issues中报告
- 💡 **功能建议**: 通过Issues提交功能需求
- 📖 **文档改进**: 直接提交PR改进文档

---

## 🎉 快速上手清单

完成以下清单，确保您已掌握系统使用：

- [ ] ✅ 成功启动系统并访问Web界面
- [ ] ✅ 完成一个完整的产品配置流程
- [ ] ✅ 选择并测试合适的AI人设
- [ ] ✅ 提交配置并查看生成的脚本预览
- [ ] ✅ 使用控制面板进行基本的直播操作
- [ ] ✅ 测试观众问题添加和AI自动回复
- [ ] ✅ 查看系统状态和性能监控页面

完成以上清单后，您就可以熟练使用AI Live Streamer系统了！

---

<div align="center">

**🎯 开始您的AI直播之旅，让技术为您的业务增长赋能！**

[🚀 立即开始](http://localhost:8000) | [📖 查看API文档](http://localhost:8000/docs) | [💡 提交建议](https://github.com/your-repo/issues)

</div>