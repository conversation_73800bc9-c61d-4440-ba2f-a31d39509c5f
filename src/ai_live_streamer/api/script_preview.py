"""Script Preview API Endpoints

REST API for generating, viewing, and managing streaming script previews
based on operational form configurations. Restored from archived functionality.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, status, Query, Path
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
from loguru import logger

from ..services.script_previewer import (
    ScriptPreviewer, ScriptSegmentType, PreviewResult, ScriptTimeline, ScriptSegment
)
from ..models.forms import OperationalForm, PriorityLevel
from ..core.exceptions import ServiceError
from ..core.config import cfg


# Request/Response models
class PreviewRequest(BaseModel):
    """Request to generate script preview"""
    form_id: str
    regenerate: bool = False
    modification_notes: Optional[str] = None


class SegmentRegenerateRequest(BaseModel):
    """Request to regenerate specific segment"""
    segment_id: str
    modification_notes: Optional[str] = None


class ExportRequest(BaseModel):
    """Request to export script"""
    form_id: str
    export_format: str = "json"  # json, text, html
    include_metadata: bool = True


class LaunchStreamRequest(BaseModel):
    """Request to launch live stream from script"""
    form_id: str
    persona: str = "default"
    auto_start: bool = True
    stream_title: Optional[str] = None


# Router setup
script_preview_router = APIRouter(prefix="/api/script-preview", tags=["script_preview"])

# Service initialization
script_previewer = ScriptPreviewer()

# In-memory storage for previews (with database persistence)
preview_cache: Dict[str, PreviewResult] = {}


class PreviewResultSerializer:
    """Utility class to serialize/deserialize PreviewResult objects for database storage
    
    Restored from archived functionality to provide persistent script storage.
    """
    
    @staticmethod
    def serialize_preview_result(preview_result: PreviewResult) -> Dict[str, Any]:
        """Convert PreviewResult to dictionary for database storage"""
        if not preview_result.script_timeline:
            return {
                "success": preview_result.success,
                "form_id": preview_result.form_id,
                "script_timeline": None,
                "estimated_metrics": preview_result.estimated_metrics,
                "generation_time_seconds": preview_result.generation_time_seconds,
                "error_messages": preview_result.error_messages,
                "warnings": preview_result.warnings
            }
        
        # Serialize script timeline
        timeline_data = {
            "total_duration_minutes": preview_result.script_timeline.total_duration_minutes,
            "segments": [],
            "interaction_points": preview_result.script_timeline.interaction_points,
            "break_points": preview_result.script_timeline.break_points,
            "adaptation_rules": preview_result.script_timeline.adaptation_rules
        }
        
        # Serialize segments
        for segment in preview_result.script_timeline.segments:
            segment_data = {
                "segment_id": segment.segment_id,
                "segment_type": segment.segment_type.value,
                "title": segment.title,
                "content": segment.content,
                "estimated_duration_seconds": segment.estimated_duration_seconds,
                "priority": segment.priority.value,
                "triggers": segment.triggers,
                "variables": segment.variables,
                "persona_notes": segment.persona_notes
            }
            timeline_data["segments"].append(segment_data)
        
        return {
            "success": preview_result.success,
            "form_id": preview_result.form_id,
            "script_timeline": timeline_data,
            "estimated_metrics": preview_result.estimated_metrics,
            "generation_time_seconds": preview_result.generation_time_seconds,
            "error_messages": preview_result.error_messages,
            "warnings": preview_result.warnings
        }
    
    @staticmethod
    def deserialize_preview_result(form_id: str, data: Dict[str, Any], preview_html: str) -> PreviewResult:
        """Convert dictionary from database back to PreviewResult object"""
        script_timeline = None
        
        if data.get("script_timeline"):
            timeline_data = data["script_timeline"]
            
            # Deserialize segments
            segments = []
            for segment_data in timeline_data.get("segments", []):
                segment = ScriptSegment(
                    segment_id=segment_data["segment_id"],
                    segment_type=ScriptSegmentType(segment_data["segment_type"]),
                    title=segment_data["title"],
                    content=segment_data["content"],
                    estimated_duration_seconds=segment_data["estimated_duration_seconds"],
                    priority=PriorityLevel(segment_data["priority"]),
                    triggers=segment_data["triggers"],
                    variables=segment_data["variables"],
                    persona_notes=segment_data.get("persona_notes")
                )
                segments.append(segment)
            
            # Create script timeline
            script_timeline = ScriptTimeline(
                total_duration_minutes=timeline_data["total_duration_minutes"],
                segments=segments,
                interaction_points=timeline_data.get("interaction_points", []),
                break_points=timeline_data.get("break_points", []),
                adaptation_rules=timeline_data.get("adaptation_rules", {})
            )
        
        return PreviewResult(
            success=data["success"],
            form_id=form_id,
            script_timeline=script_timeline,
            preview_html=preview_html,
            estimated_metrics=data.get("estimated_metrics", {}),
            generation_time_seconds=data.get("generation_time_seconds", 0.0),
            error_messages=data.get("error_messages", []),
            warnings=data.get("warnings", [])
        )


# Database persistence functions
def save_preview_to_database(preview_result: PreviewResult) -> None:
    """Save script preview result to database"""
    try:
        # Import form_storage
        from .operational_forms import form_storage
        
        # Serialize preview result
        serialized_data = PreviewResultSerializer.serialize_preview_result(preview_result)
        
        # Save to database
        form_storage.create_script_preview(
            form_id=preview_result.form_id,
            preview_data=serialized_data,
            preview_html=preview_result.preview_html or "",
            estimated_metrics=preview_result.estimated_metrics,
            warnings=preview_result.warnings,
            generation_time_seconds=preview_result.generation_time_seconds
        )
        
        logger.info(f"✅ Saved script preview for form {preview_result.form_id} to database")
        
    except Exception as e:
        logger.error(f"❌ Failed to save preview to database for form {preview_result.form_id}: {e}")
        raise ServiceError(f"Failed to save script preview: {e}")


def load_preview_from_database(form_id: str) -> Optional[PreviewResult]:
    """Load script preview from database into memory cache"""
    try:
        from .operational_forms import form_storage
        
        preview_data = form_storage.get_script_preview(form_id)
        if not preview_data:
            return None
        
        # Deserialize preview result
        preview_result = PreviewResultSerializer.deserialize_preview_result(
            form_id=form_id,
            data=preview_data["preview_data"],
            preview_html=preview_data["preview_html"]
        )
        
        # Cache in memory for performance
        preview_cache[form_id] = preview_result
        
        logger.debug(f"📥 Loaded script preview for form {form_id} from database")
        return preview_result
        
    except Exception as e:
        logger.error(f"❌ Failed to load preview from database for form {form_id}: {e}")
        return None


def get_preview_from_cache_or_db(form_id: str) -> Optional[PreviewResult]:
    """Get preview from memory cache first, then database if not found"""
    # Check memory cache first
    if form_id in preview_cache:
        logger.info(f"📦 Cache HIT (memory): Found preview for form {form_id} in memory cache")
        return preview_cache[form_id]
    
    # Load from database if not in cache
    preview_result = load_preview_from_database(form_id)
    if preview_result:
        logger.info(f"💾 Cache HIT (database): Loaded preview for form {form_id} from database to memory cache")
        return preview_result
    
    logger.info(f"❓ Cache MISS: No preview found for form {form_id} in cache or database")
    return None


def get_form_by_id(form_id: str) -> Optional[OperationalForm]:
    """Get form by ID from operational forms storage"""
    try:
        # Import from operational forms module to get shared storage
        from .operational_forms import form_storage
        return form_storage.get_form(form_id)
    except (ImportError, AttributeError) as e:
        logger.warning(f"Could not access form_storage: {e}")
        return None


@script_preview_router.post("/generate", response_model=Dict[str, Any])
async def generate_script_preview(request: PreviewRequest) -> Dict[str, Any]:
    """Generate complete script preview from operational form"""
    try:
        logger.info(f"Generating script preview for form {request.form_id}")
        
        # Get form data
        form = get_form_by_id(request.form_id)
        if not form:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Form not found: {request.form_id}"
            )
        
        # Check if we should regenerate or use cached/persisted version
        if not request.regenerate:
            cached_preview = get_preview_from_cache_or_db(request.form_id)
            if cached_preview:
                cache_source = "memory" if request.form_id in preview_cache else "database"
                logger.info(f"🔄 Using {cache_source} cached preview for form {request.form_id}")
                
                return {
                    "success": cached_preview.success,
                    "form_id": cached_preview.form_id,
                    "cached": True,
                    "cache_source": cache_source,
                    "generation_time_seconds": cached_preview.generation_time_seconds,
                    "estimated_metrics": cached_preview.estimated_metrics,
                    "segments_count": len(cached_preview.script_timeline.segments) if cached_preview.script_timeline else 0,
                    "error_messages": cached_preview.error_messages,
                    "warnings": cached_preview.warnings
                }
        
        # Generate new preview
        preview_result = await script_previewer.generate_script_preview(form)
        
        # Save to database and cache in memory
        if preview_result.success:
            save_preview_to_database(preview_result)
            preview_cache[request.form_id] = preview_result
        
        if preview_result.success:
            logger.info(f"✅ Script preview generated successfully for form {request.form_id}")
            
            return {
                "success": True,
                "form_id": preview_result.form_id,
                "cached": False,
                "generation_time_seconds": preview_result.generation_time_seconds,
                "estimated_metrics": preview_result.estimated_metrics,
                "segments_count": len(preview_result.script_timeline.segments),
                "total_duration_minutes": preview_result.script_timeline.total_duration_minutes,
                "interaction_points": len(preview_result.script_timeline.interaction_points),
                "warnings": preview_result.warnings,
                "preview_url": f"/api/script-preview/preview/{request.form_id}"
            }
        else:
            logger.error(f"❌ Script preview generation failed for form {request.form_id}")
            return {
                "success": False,
                "form_id": preview_result.form_id,
                "error_messages": preview_result.error_messages,
                "generation_time_seconds": preview_result.generation_time_seconds
            }
    
    except Exception as e:
        logger.error(f"Script preview generation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate script preview: {str(e)}"
        )


@script_preview_router.get("/preview/{form_id}", response_class=HTMLResponse)
async def get_script_preview_html(form_id: str) -> HTMLResponse:
    """Get HTML preview of generated script"""
    try:
        # Check cache or database first
        preview_result = get_preview_from_cache_or_db(form_id)
        if preview_result and preview_result.success and preview_result.preview_html:
            return HTMLResponse(content=preview_result.preview_html)
        
        # If not found, try to generate new preview
        form = get_form_by_id(form_id)
        if not form:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Form not found: {form_id}"
            )
        
        logger.info(f"🎬 Generating preview HTML for form {form_id}")
        preview_result = await script_previewer.generate_script_preview(form)
        
        if preview_result.success and preview_result.preview_html:
            # Save to database and cache in memory
            save_preview_to_database(preview_result)
            preview_cache[form_id] = preview_result
            return HTMLResponse(content=preview_result.preview_html)
        else:
            error_html = f"""
            <html>
            <body>
                <h1>Preview Generation Failed</h1>
                <p>Could not generate preview for form {form_id}</p>
                <ul>
                    {"".join(f"<li>{error}</li>" for error in preview_result.error_messages)}
                </ul>
            </body>
            </html>
            """
            return HTMLResponse(content=error_html, status_code=500)
    
    except Exception as e:
        logger.error(f"Error getting preview HTML: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get preview HTML: {str(e)}"
        )


@script_preview_router.get("/view/{form_id}", response_class=HTMLResponse)
async def get_script_view_html(form_id: str) -> HTMLResponse:
    """Get HTML view of generated script (alias for /preview/{form_id} for backward compatibility)"""
    return await get_script_preview_html(form_id)


@script_preview_router.get("/segments/{form_id}", response_model=Dict[str, Any])
async def get_script_segments(form_id: str) -> Dict[str, Any]:
    """Get detailed script segments for a form"""
    try:
        # Check cache or database first
        preview_result = get_preview_from_cache_or_db(form_id)
        if preview_result and preview_result.success and preview_result.script_timeline:
                segments_data = []
                for segment in preview_result.script_timeline.segments:
                    segments_data.append({
                        "segment_id": segment.segment_id,
                        "segment_type": segment.segment_type.value,
                        "title": segment.title,
                        "content": segment.content,
                        "estimated_duration_seconds": segment.estimated_duration_seconds,
                        "priority": segment.priority.value,
                        "triggers": segment.triggers,
                        "variables": segment.variables,
                        "persona_notes": segment.persona_notes
                    })
                
                return {
                    "success": True,
                    "form_id": form_id,
                    "segments": segments_data,
                    "total_segments": len(segments_data),
                    "total_duration_minutes": preview_result.script_timeline.total_duration_minutes,
                    "interaction_points": preview_result.script_timeline.interaction_points,
                    "break_points": preview_result.script_timeline.break_points
                }
        
        # If not cached, return error
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No preview found for form {form_id}. Generate preview first."
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting segments: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve segments: {str(e)}"
        )


@script_preview_router.post("/regenerate-segment", response_model=Dict[str, Any])
async def regenerate_script_segment(request: SegmentRegenerateRequest) -> Dict[str, Any]:
    """Regenerate a specific script segment with modifications"""
    try:
        # Find form ID from segment by searching cache and database
        form_id = None
        form = None
        
        # First check memory cache
        for fid, preview_result in preview_cache.items():
            if preview_result.success and preview_result.script_timeline:
                if any(s.segment_id == request.segment_id for s in preview_result.script_timeline.segments):
                    form_id = fid
                    form = get_form_by_id(fid)
                    break
        
        # If not found in cache, search database
        if not form_id:
            try:
                from .operational_forms import form_storage
                all_previews = form_storage.list_script_previews(limit=1000)  
                
                for preview_summary in all_previews:
                    fid = preview_summary["form_id"]
                    preview_result = get_preview_from_cache_or_db(fid)
                    if preview_result and preview_result.script_timeline:
                        if any(s.segment_id == request.segment_id for s in preview_result.script_timeline.segments):
                            form_id = fid
                            form = get_form_by_id(fid)
                            break
            except Exception as e:
                logger.warning(f"Could not search database for segment: {e}")
        
        if not form_id or not form:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Segment not found: {request.segment_id}"
            )
        
        logger.info(f"Regenerating segment {request.segment_id} for form {form_id}")
        
        # Regenerate the segment
        new_segment = await script_previewer.regenerate_segment(
            form, request.segment_id, request.modification_notes
        )
        
        # Update the preview with the new segment
        preview_result = get_preview_from_cache_or_db(form_id)
        if preview_result and preview_result.script_timeline:
            # Find and replace the segment
            for i, segment in enumerate(preview_result.script_timeline.segments):
                if segment.segment_id == request.segment_id:
                    preview_result.script_timeline.segments[i] = new_segment
                    break
            
            # Regenerate HTML preview
            preview_result.preview_html = await script_previewer._generate_html_preview(
                form, preview_result.script_timeline
            )
            
            # Save updated preview to database
            save_preview_to_database(preview_result)
            
            # Update memory cache
            preview_cache[form_id] = preview_result
        
        return {
            "success": True,
            "segment_id": new_segment.segment_id,
            "title": new_segment.title,
            "content": new_segment.content,
            "estimated_duration_seconds": new_segment.estimated_duration_seconds,
            "modification_notes": request.modification_notes,
            "regenerated_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Segment regeneration error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to regenerate segment: {str(e)}"
        )


@script_preview_router.post("/export", response_model=Dict[str, Any])
async def export_script(request: ExportRequest) -> Dict[str, Any]:
    """Export script in various formats"""
    try:
        # Check if preview exists in cache or database
        preview_result = get_preview_from_cache_or_db(request.form_id)
        if not preview_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No preview found for form {request.form_id}. Generate preview first."
            )
        
        if not preview_result.success or not preview_result.script_timeline:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot export failed preview"
            )
        
        form = get_form_by_id(request.form_id)
        if not form:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Form not found: {request.form_id}"
            )
        
        logger.info(f"Exporting script for form {request.form_id} in format {request.export_format}")
        
        # Export using script previewer
        export_result = await script_previewer.export_script(
            form, preview_result.script_timeline, request.export_format
        )
        
        # Add metadata if requested
        if request.include_metadata:
            export_result["metadata"] = {
                "exported_at": datetime.utcnow().isoformat(),
                "generation_time_seconds": preview_result.generation_time_seconds,
                "estimated_metrics": preview_result.estimated_metrics,
                "segments_count": len(preview_result.script_timeline.segments),
                "total_duration_minutes": preview_result.script_timeline.total_duration_minutes
            }
        
        return export_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Export error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Export failed: {str(e)}"
        )


@script_preview_router.post("/prepare-stream", response_model=Dict[str, Any])
async def prepare_live_stream(request: LaunchStreamRequest) -> Dict[str, Any]:
    """Prepare live stream from script (does not auto-start)"""
    try:
        # Check if preview exists in cache or database
        preview_result = get_preview_from_cache_or_db(request.form_id)
        if not preview_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No preview found for form {request.form_id}. Generate preview first."
            )
        
        if not preview_result.success or not preview_result.script_timeline:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot prepare stream from failed preview"
            )
        
        form = get_form_by_id(request.form_id)
        if not form:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Form not found: {request.form_id}"
            )
        
        logger.info(f"Preparing stream for form {request.form_id}")
        
        # Prepare stream data for controller
        stream_segments = []
        for segment in preview_result.script_timeline.segments:
            stream_segments.append({
                "segment_id": segment.segment_id,
                "type": segment.segment_type.value,
                "title": segment.title,
                "content": segment.content,
                "duration_seconds": segment.estimated_duration_seconds,
                "priority": segment.priority.value
            })
        
        stream_title = request.stream_title or form.basic_information.stream_title
        
        prepared_stream = {
            "form_id": request.form_id,
            "stream_title": stream_title,
            "persona": request.persona,
            "total_duration_minutes": preview_result.script_timeline.total_duration_minutes,
            "segments": stream_segments,
            "interaction_points": preview_result.script_timeline.interaction_points,
            "estimated_metrics": preview_result.estimated_metrics,
            "prepared_at": datetime.utcnow().isoformat()
        }
        
        # Store in cache for potential stream starting
        preview_cache[f"{request.form_id}_stream_prepared"] = prepared_stream
        
        return {
            "success": True,
            "stream_prepared": True,
            "form_id": request.form_id,
            "stream_title": stream_title,
            "persona": request.persona,
            "segments_count": len(stream_segments),
            "total_duration_minutes": preview_result.script_timeline.total_duration_minutes,
            "start_stream_endpoint": "/api/control/start-stream",
            "prepared_data_id": f"{request.form_id}_stream_prepared"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Stream preparation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Stream preparation failed: {str(e)}"
        )


# Backward compatibility endpoint - DISABLED
@script_preview_router.post("/launch-stream", response_model=Dict[str, Any])
async def launch_live_stream_deprecated(request: LaunchStreamRequest) -> Dict[str, Any]:
    """[已禁用] V1启动直播流端点 - 强制迁移到V2
    
    此端点已永久禁用以防止V1/V2混合模式运行导致的QA处理失败。
    """
    logger.error("🚫 客户端尝试使用已禁用的V1 API /launch-stream - 请求被拒绝")
    logger.error("❌ V1 API已永久禁用，请立即更新到 /api/script-preview/prepare-stream")
    
    # 直接返回410 Gone错误，强制客户端升级
    from fastapi import HTTPException
    raise HTTPException(
        status_code=410,  # Gone - 资源永久不可用
        detail={
            "error": "V1_API_DISABLED",
            "message": "The /launch-stream endpoint has been permanently disabled. Please use /prepare-stream",
            "new_endpoint": "/api/script-preview/prepare-stream",
            "migration_guide": "https://docs.ai-livestreamer.com/v2-migration",
            "reason": "V1 architecture causes QA insertion timing issues (60s timeout, wrong position)"
        }
    )


@script_preview_router.get("/list", response_model=Dict[str, Any])
async def list_script_previews() -> Dict[str, Any]:
    """List all script previews from database with summary information"""
    try:
        from .operational_forms import form_storage
        
        # Get all previews from database
        db_previews = form_storage.list_script_previews(limit=1000)
        
        previews = []
        for db_preview in db_previews:
            form_id = db_preview["form_id"]
            
            # Try to get form information
            form = get_form_by_id(form_id)
            
            preview_info = {
                "form_id": form_id,
                "created_by": db_preview.get("created_by", ""),
                "created_at": db_preview["created_at"].isoformat() if isinstance(db_preview["created_at"], datetime) else str(db_preview["created_at"]),
                "updated_at": db_preview["updated_at"].isoformat() if isinstance(db_preview["updated_at"], datetime) else str(db_preview["updated_at"]),
                "generation_time_seconds": db_preview.get("generation_time_seconds", 0),
                "cached_in_memory": form_id in preview_cache
            }
            
            # Add form metadata if available
            if form:
                preview_info.update({
                    "title": form.basic_information.stream_title,
                    "product_name": form.product_information.product_name,
                    "brand": form.product_information.brand,
                    "planned_duration": form.basic_information.planned_duration_minutes
                })
            
            # Try to get segment count from cached preview
            cached_preview = get_preview_from_cache_or_db(form_id)
            if cached_preview and cached_preview.script_timeline:
                preview_info["segments_count"] = len(cached_preview.script_timeline.segments)
                preview_info["estimated_duration_minutes"] = cached_preview.script_timeline.total_duration_minutes
            
            previews.append(preview_info)
        
        return {
            "status": "success",
            "total_previews": len(previews),
            "previews": previews,
            "retrieved_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error listing previews: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list previews: {str(e)}"
        )


@script_preview_router.get("/stats", response_model=Dict[str, Any])
async def get_preview_stats() -> Dict[str, Any]:
    """Get script preview service statistics"""
    try:
        # Get service stats
        service_stats = script_previewer.get_generation_stats()
        
        # Get database stats
        from .operational_forms import form_storage
        db_stats = form_storage.get_script_preview_stats()
        
        # Add cache statistics
        cache_stats = {
            "cached_previews": len(preview_cache),
            "cache_size_mb": sum(len(str(preview)) for preview in preview_cache.values()) / (1024 * 1024),
            "cache_keys": list(preview_cache.keys())
        }
        
        return {
            "service_stats": service_stats,
            "database_stats": db_stats,
            "cache_stats": cache_stats,
            "available_segment_types": [segment_type.value for segment_type in ScriptSegmentType],
            "service_status": "active",
            "retrieved_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get statistics: {str(e)}"
        )


@script_preview_router.delete("/cache/{form_id}")
async def clear_preview_cache(form_id: str) -> Dict[str, Any]:
    """Clear cached preview and database entry for a specific form"""
    try:
        # Remove from memory cache
        keys_to_remove = [key for key in preview_cache.keys() if key.startswith(form_id)]
        
        for key in keys_to_remove:
            del preview_cache[key]
        
        # Remove from database
        db_deleted = False
        try:
            from .operational_forms import form_storage
            db_deleted = form_storage.delete_script_preview(form_id)
        except Exception as db_error:
            logger.warning(f"Failed to delete from database for form {form_id}: {db_error}")
        
        logger.info(f"Cleared {len(keys_to_remove)} cache entries and database entry (deleted: {db_deleted}) for form {form_id}")
        
        return {
            "success": True,
            "form_id": form_id,
            "cleared_cache_entries": len(keys_to_remove),
            "deleted_from_database": db_deleted,
            "remaining_cache_size": len(preview_cache)
        }
        
    except Exception as e:
        logger.error(f"Error clearing cache: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear cache: {str(e)}"
        )


@script_preview_router.delete("/cache")
async def clear_all_preview_cache() -> Dict[str, Any]:
    """Clear all cached previews from memory and database"""
    try:
        # Count current cache entries
        cache_count = len(preview_cache)
        
        # Clear memory cache
        preview_cache.clear()
        
        # Clear all database entries
        db_deleted_count = 0
        try:
            from .operational_forms import form_storage
            db_deleted_count = form_storage.delete_all_script_previews()
        except Exception as db_error:
            logger.warning(f"Failed to clear database previews: {db_error}")
        
        logger.info(f"Cleared all cache entries ({cache_count}) and database entries ({db_deleted_count})")
        
        return {
            "success": True,
            "cleared_cache_entries": cache_count,
            "deleted_database_entries": db_deleted_count,
            "remaining_cache_size": 0
        }
        
    except Exception as e:
        logger.error(f"Error clearing all cache: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear all cache: {str(e)}"
        )


# Health check endpoint
@script_preview_router.get("/health")
async def health_check() -> Dict[str, Any]:
    """Check health of script preview service"""
    try:
        stats = script_previewer.get_generation_stats()
        
        return {
            "status": "healthy",
            "service": "script_preview",
            "llm_enabled": script_previewer.use_llm_generation,
            "total_generations": stats.get("scripts_generated", 0),
            "cache_size": len(preview_cache),
            "checked_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "service": "script_preview",
            "error": str(e),
            "checked_at": datetime.utcnow().isoformat()
        }


@script_preview_router.get("/timeline/{form_id}", response_model=Dict[str, Any])
async def get_script_timeline(form_id: str = Path(..., description="Form ID")) -> Dict[str, Any]:
    """Get detailed script timeline data - restored from archive for frontend compatibility"""
    try:
        preview_result = get_preview_from_cache_or_db(form_id)
        if not preview_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No preview found for form: {form_id}"
            )
        timeline = preview_result.script_timeline
        
        response = {
            "form_id": form_id,
            "timeline": {
                "total_duration_minutes": timeline.total_duration_minutes,
                "segments": [
                    {
                        "segment_id": segment.segment_id,
                        "type": segment.segment_type.value,
                        "title": segment.title,
                        "content": segment.content,
                        "estimated_duration_seconds": segment.estimated_duration_seconds,
                        "priority": segment.priority.value,
                        "triggers": segment.triggers,
                        "variables": segment.variables,
                        "persona_notes": segment.persona_notes
                    }
                    for segment in timeline.segments
                ],
                "interaction_points": timeline.interaction_points,
                "break_points": timeline.break_points,
                "adaptation_rules": timeline.adaptation_rules
            },
            "estimated_metrics": preview_result.estimated_metrics,
            "warnings": preview_result.warnings,
            "retrieved_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Timeline data retrieved for form {form_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving timeline: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve timeline: {str(e)}"
        )