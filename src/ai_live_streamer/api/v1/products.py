"""产品管理RESTful API

提供产品的增删改查接口。
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, Path, status
from loguru import logger

from ...models.product import (
    Product, ProductCreate, ProductUpdate, ProductResponse,
    ProductDetailResponse, ProductStats, ProductFilters, Pagination
)
from ...services.product_service import ProductService
from ...services.persistence import DatabaseManager
from ...core.config import cfg
from ...core.exceptions import ServiceError, ValidationError


# 创建路由器
router = APIRouter(prefix="/api/v1/products", tags=["products"])

# 初始化服务
db_manager = DatabaseManager(cfg.database_path)
product_service = ProductService(db_manager)


@router.get("/", response_model=List[ProductResponse])
async def list_products(
    category: Optional[str] = Query(None, description="产品分类"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    min_price: Optional[float] = Query(None, ge=0, description="最低价格"),
    max_price: Optional[float] = Query(None, ge=0, description="最高价格"),
    has_stock: Optional[bool] = Query(None, description="是否有库存"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小")
):
    """获取产品列表"""
    try:
        filters = ProductFilters(
            category=category,
            search=search,
            min_price=min_price,
            max_price=max_price,
            has_stock=has_stock,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        pagination = Pagination(page=page, size=size)
        
        result = await product_service.list_products(filters, pagination)
        
        # 返回带分页信息的响应
        return {
            "items": result.items,
            "total": result.total,
            "page": result.page,
            "size": result.size,
            "pages": result.pages,
            "has_next": result.has_next,
            "has_prev": result.has_prev
        }
        
    except ServiceError as e:
        logger.error(f"获取产品列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=ProductResponse, status_code=status.HTTP_201_CREATED)
async def create_product(product: ProductCreate):
    """创建新产品"""
    try:
        created_product = await product_service.create_product(product)
        
        # 获取标签
        tags = product.tags if product.tags else []
        
        return ProductResponse(
            id=created_product.id,
            sku=created_product.sku,
            name=created_product.name,
            category=created_product.category,
            description=created_product.description,
            price=created_product.price,
            stock=created_product.stock,
            tags=tags,
            created_at=created_product.created_at,
            updated_at=created_product.updated_at
        )
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except ServiceError as e:
        logger.error(f"创建产品失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{product_id}", response_model=ProductDetailResponse)
async def get_product(product_id: int = Path(..., ge=1, description="产品ID")):
    """获取产品详情"""
    try:
        product = await product_service.get_product(product_id)
        stats = await product_service.get_product_stats(product_id)
        
        # 获取产品标签
        # TODO: 从数据库获取标签
        tags = []
        
        return ProductDetailResponse(
            id=product.id,
            sku=product.sku,
            name=product.name,
            category=product.category,
            description=product.description,
            price=product.price,
            stock=product.stock,
            tags=tags,
            created_at=product.created_at,
            updated_at=product.updated_at,
            qa_count=stats.qa_count,
            config_count=stats.config_count,
            total_qa_hits=stats.total_qa_hits,
            last_used=stats.last_used
        )
        
    except ValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        logger.error(f"获取产品详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: int = Path(..., ge=1, description="产品ID"),
    updates: ProductUpdate = ...
):
    """更新产品信息"""
    try:
        updated_product = await product_service.update_product(product_id, updates)
        
        # 获取标签
        tags = updates.tags if updates.tags else []
        
        return ProductResponse(
            id=updated_product.id,
            sku=updated_product.sku,
            name=updated_product.name,
            category=updated_product.category,
            description=updated_product.description,
            price=updated_product.price,
            stock=updated_product.stock,
            tags=tags,
            created_at=updated_product.created_at,
            updated_at=updated_product.updated_at
        )
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except ServiceError as e:
        logger.error(f"更新产品失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{product_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_product(product_id: int = Path(..., ge=1, description="产品ID")):
    """删除产品"""
    try:
        await product_service.delete_product(product_id)
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except ServiceError as e:
        logger.error(f"删除产品失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{product_id}/stats", response_model=ProductStats)
async def get_product_stats(product_id: int = Path(..., ge=1, description="产品ID")):
    """获取产品统计信息"""
    try:
        stats = await product_service.get_product_stats(product_id)
        return stats
        
    except ValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        logger.error(f"获取产品统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sku/{sku}", response_model=ProductResponse)
async def get_product_by_sku(sku: str = Path(..., description="产品SKU")):
    """根据SKU获取产品"""
    try:
        product = await product_service.get_product_by_sku(sku)
        if not product:
            raise HTTPException(status_code=404, detail=f"SKU {sku} 不存在")
        
        # TODO: 获取标签
        tags = []
        
        return ProductResponse(
            id=product.id,
            sku=product.sku,
            name=product.name,
            category=product.category,
            description=product.description,
            price=product.price,
            stock=product.stock,
            tags=tags,
            created_at=product.created_at,
            updated_at=product.updated_at
        )
        
    except HTTPException:
        raise
    except ServiceError as e:
        logger.error(f"根据SKU获取产品失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))