"""产品QA管理API

提供基于产品维度的QA管理接口。
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, Path, status
from pydantic import BaseModel, Field
from datetime import datetime
import sqlite3
import json
from loguru import logger

from ...services.persistence import DatabaseManager
from ...core.config import cfg
from ...core.exceptions import ServiceError, ValidationError


# 请求/响应模型
class QACreate(BaseModel):
    """创建QA请求"""
    question: str = Field(..., min_length=1, max_length=500, description="问题")
    answer: str = Field(..., min_length=1, max_length=2000, description="答案")
    category: Optional[str] = Field(None, max_length=50, description="分类")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签")


class QAUpdate(BaseModel):
    """更新QA请求"""
    question: Optional[str] = Field(None, min_length=1, max_length=500)
    answer: Optional[str] = Field(None, min_length=1, max_length=2000)
    category: Optional[str] = Field(None, max_length=50)
    tags: Optional[List[str]] = None
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)


class QAResponse(BaseModel):
    """QA响应"""
    id: int
    product_id: int
    question: str
    answer: str
    category: Optional[str]
    tags: List[str]
    hit_count: int
    confidence_score: float
    created_at: datetime
    updated_at: datetime


class QAListResponse(BaseModel):
    """QA列表响应"""
    items: List[QAResponse]
    total: int
    page: int
    size: int
    pages: int
    has_next: bool
    has_prev: bool


# 创建路由器
router = APIRouter(tags=["product-qa"])

# 初始化数据库
db_manager = DatabaseManager(cfg.database_path)


def get_db_connection():
    """获取数据库连接"""
    return db_manager.get_connection()


@router.get("/api/v1/products/{product_id}/qa", response_model=QAListResponse)
async def get_product_qa(
    product_id: int = Path(..., ge=1, description="产品ID"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    category: Optional[str] = Query(None, description="分类"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小")
):
    """获取产品的QA列表"""
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        
        # 检查产品是否存在
        cursor.execute("SELECT id FROM products WHERE id = ?", (product_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail=f"产品ID {product_id} 不存在")
        
        # 构建查询条件
        where_clauses = ["product_id = ?"]
        params = [product_id]
        
        if search:
            where_clauses.append("(question LIKE ? OR answer LIKE ?)")
            search_pattern = f"%{search}%"
            params.extend([search_pattern, search_pattern])
        
        if category:
            where_clauses.append("category = ?")
            params.append(category)
        
        where_clause = " AND ".join(where_clauses)
        
        # 获取总数
        count_query = f"SELECT COUNT(*) FROM qa_entries WHERE {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]
        
        # 分页查询
        query = f"""
        SELECT id, product_id, question, answer, category, tags,
               hit_count, confidence_score, created_at, updated_at
        FROM qa_entries
        WHERE {where_clause}
        ORDER BY hit_count DESC, created_at DESC
        LIMIT ? OFFSET ?
        """
        
        offset = (page - 1) * size
        params.extend([size, offset])
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # 构建响应
        items = []
        for row in rows:
            items.append(QAResponse(
                id=row[0],
                product_id=row[1],
                question=row[2],
                answer=row[3],
                category=row[4] or "",
                tags=json.loads(row[5]) if row[5] else [],
                hit_count=row[6],
                confidence_score=row[7],
                created_at=datetime.fromisoformat(row[8]) if row[8] else datetime.now(),
                updated_at=datetime.fromisoformat(row[9]) if row[9] else datetime.now()
            ))
        
        pages = (total + size - 1) // size
        
        return QAListResponse(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages,
            has_next=page < pages,
            has_prev=page > 1
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取产品QA失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()


@router.post("/api/v1/products/{product_id}/qa", response_model=QAResponse, status_code=status.HTTP_201_CREATED)
async def add_qa_to_product(
    product_id: int = Path(..., ge=1, description="产品ID"),
    qa: QACreate = ...
):
    """为产品添加QA"""
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        
        # 检查产品是否存在
        cursor.execute("SELECT id FROM products WHERE id = ?", (product_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail=f"产品ID {product_id} 不存在")
        
        # 插入QA
        cursor.execute("""
        INSERT INTO qa_entries (product_id, question, answer, category, tags, confidence_score)
        VALUES (?, ?, ?, ?, ?, ?)
        """, (
            product_id,
            qa.question,
            qa.answer,
            qa.category,
            json.dumps(qa.tags) if qa.tags else None,
            0.8  # 默认置信度
        ))
        
        qa_id = cursor.lastrowid
        
        # 更新FTS索引
        cursor.execute("""
        INSERT INTO qa_entries_fts (rowid, question, answer)
        VALUES (?, ?, ?)
        """, (qa_id, qa.question, qa.answer))
        
        conn.commit()
        
        # 获取创建的QA
        cursor.execute("""
        SELECT id, product_id, question, answer, category, tags,
               hit_count, confidence_score, created_at, updated_at
        FROM qa_entries WHERE id = ?
        """, (qa_id,))
        
        row = cursor.fetchone()
        
        return QAResponse(
            id=row[0],
            product_id=row[1],
            question=row[2],
            answer=row[3],
            category=row[4] or "",
            tags=json.loads(row[5]) if row[5] else [],
            hit_count=row[6],
            confidence_score=row[7],
            created_at=datetime.fromisoformat(row[8]) if row[8] else datetime.now(),
            updated_at=datetime.fromisoformat(row[9]) if row[9] else datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        conn.rollback()
        logger.error(f"添加QA失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()


@router.put("/api/v1/products/{product_id}/qa/{qa_id}", response_model=QAResponse)
async def update_product_qa(
    product_id: int = Path(..., ge=1, description="产品ID"),
    qa_id: int = Path(..., ge=1, description="QA ID"),
    updates: QAUpdate = ...
):
    """更新产品的QA"""
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        
        # 检查QA是否属于该产品
        cursor.execute("""
        SELECT id FROM qa_entries WHERE id = ? AND product_id = ?
        """, (qa_id, product_id))
        
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="QA不存在或不属于该产品")
        
        # 构建更新语句
        update_fields = []
        params = []
        
        if updates.question is not None:
            update_fields.append("question = ?")
            params.append(updates.question)
        
        if updates.answer is not None:
            update_fields.append("answer = ?")
            params.append(updates.answer)
        
        if updates.category is not None:
            update_fields.append("category = ?")
            params.append(updates.category)
        
        if updates.tags is not None:
            update_fields.append("tags = ?")
            params.append(json.dumps(updates.tags))
        
        if updates.confidence_score is not None:
            update_fields.append("confidence_score = ?")
            params.append(updates.confidence_score)
        
        if not update_fields:
            raise HTTPException(status_code=400, detail="没有要更新的字段")
        
        update_fields.append("updated_at = CURRENT_TIMESTAMP")
        
        query = f"UPDATE qa_entries SET {', '.join(update_fields)} WHERE id = ?"
        params.append(qa_id)
        
        cursor.execute(query, params)
        
        # 更新FTS索引
        if updates.question or updates.answer:
            cursor.execute("""
            SELECT question, answer FROM qa_entries WHERE id = ?
            """, (qa_id,))
            row = cursor.fetchone()
            
            cursor.execute("""
            INSERT OR REPLACE INTO qa_entries_fts (rowid, question, answer)
            VALUES (?, ?, ?)
            """, (qa_id, row[0], row[1]))
        
        conn.commit()
        
        # 获取更新后的QA
        cursor.execute("""
        SELECT id, product_id, question, answer, category, tags,
               hit_count, confidence_score, created_at, updated_at
        FROM qa_entries WHERE id = ?
        """, (qa_id,))
        
        row = cursor.fetchone()
        
        return QAResponse(
            id=row[0],
            product_id=row[1],
            question=row[2],
            answer=row[3],
            category=row[4] or "",
            tags=json.loads(row[5]) if row[5] else [],
            hit_count=row[6],
            confidence_score=row[7],
            created_at=datetime.fromisoformat(row[8]) if row[8] else datetime.now(),
            updated_at=datetime.fromisoformat(row[9]) if row[9] else datetime.now()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        conn.rollback()
        logger.error(f"更新QA失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()


@router.delete("/api/v1/products/{product_id}/qa/{qa_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_product_qa(
    product_id: int = Path(..., ge=1, description="产品ID"),
    qa_id: int = Path(..., ge=1, description="QA ID")
):
    """删除产品的QA"""
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        
        # 删除QA（同时检查产品ID）
        cursor.execute("""
        DELETE FROM qa_entries WHERE id = ? AND product_id = ?
        """, (qa_id, product_id))
        
        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail="QA不存在或不属于该产品")
        
        # 删除FTS索引
        cursor.execute("""
        DELETE FROM qa_entries_fts WHERE rowid = ?
        """, (qa_id,))
        
        conn.commit()
        
    except HTTPException:
        raise
    except Exception as e:
        conn.rollback()
        logger.error(f"删除QA失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()


@router.get("/api/v1/products/{product_id}/qa/search")
async def search_product_qa(
    product_id: int = Path(..., ge=1, description="产品ID"),
    q: str = Query(..., min_length=1, max_length=200, description="搜索关键词"),
    limit: int = Query(10, ge=1, le=50, description="返回数量")
):
    """搜索产品的QA"""
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        
        # 检查产品是否存在
        cursor.execute("SELECT id FROM products WHERE id = ?", (product_id,))
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail=f"产品ID {product_id} 不存在")
        
        # 使用FTS搜索
        cursor.execute("""
        SELECT qa.id, qa.question, qa.answer, qa.hit_count, qa.confidence_score
        FROM qa_entries qa
        JOIN qa_entries_fts fts ON qa.id = fts.rowid
        WHERE qa.product_id = ? AND fts MATCH ?
        ORDER BY rank, qa.hit_count DESC
        LIMIT ?
        """, (product_id, q, limit))
        
        results = []
        for row in cursor.fetchall():
            results.append({
                "id": row[0],
                "question": row[1],
                "answer": row[2],
                "hit_count": row[3],
                "confidence_score": row[4]
            })
        
        return {
            "query": q,
            "product_id": product_id,
            "results": results,
            "count": len(results)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索QA失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        conn.close()