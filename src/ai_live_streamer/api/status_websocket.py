"""WebSocket endpoint for real-time status updates

Provides real-time status broadcasting for all active streaming sessions.
Supports client subscription, filtering, and automatic reconnection.

Author: <PERSON> Code
Date: 2025-08-05
"""

import asyncio
import json
from typing import Dict, Set, Optional, Any, List
from datetime import datetime
from fastapi import API<PERSON>outer, WebSocket, WebSocketDisconnect, Query
from loguru import logger

from ..core.exceptions import ServiceError


router = APIRouter(prefix="/ws", tags=["websocket"])


class ConnectionManager:
    """Manages WebSocket connections and message broadcasting"""
    
    def __init__(self):
        # Active connections by client ID
        self._active_connections: Dict[str, WebSocket] = {}
        # Subscription filters by client ID
        self._client_filters: Dict[str, Dict[str, Any]] = {}
        # Message queue for reliable delivery
        self._message_queues: Dict[str, asyncio.Queue] = {}
        
        logger.info("✅ WebSocket ConnectionManager initialized")
    
    async def connect(self, websocket: WebSocket, client_id: str) -> None:
        """Accept new WebSocket connection"""
        await websocket.accept()
        
        # Store connection
        self._active_connections[client_id] = websocket
        self._message_queues[client_id] = asyncio.Queue(maxsize=100)
        
        logger.info(f"📡 Client {client_id} connected via WebSocket")
        
        # Send welcome message
        await self.send_personal_message(
            {
                "type": "connection",
                "status": "connected",
                "client_id": client_id,
                "timestamp": datetime.utcnow().isoformat()
            },
            client_id
        )
    
    def disconnect(self, client_id: str) -> None:
        """Remove disconnected client"""
        if client_id in self._active_connections:
            del self._active_connections[client_id]
            
        if client_id in self._client_filters:
            del self._client_filters[client_id]
            
        if client_id in self._message_queues:
            del self._message_queues[client_id]
        
        logger.info(f"📡 Client {client_id} disconnected")
    
    async def send_personal_message(self, message: Dict[str, Any], client_id: str) -> None:
        """Send message to specific client"""
        if client_id in self._active_connections:
            websocket = self._active_connections[client_id]
            try:
                await websocket.send_json(message)
            except Exception as e:
                logger.warning(f"Failed to send message to {client_id}: {str(e)}")
                self.disconnect(client_id)
    
    async def broadcast(self, message: Dict[str, Any], session_id: Optional[str] = None) -> None:
        """Broadcast message to all connected clients with matching filters"""
        # Add timestamp if not present
        if "timestamp" not in message:
            message["timestamp"] = datetime.utcnow().isoformat()
        
        # Send to all clients that match the filter
        disconnected_clients = []
        
        for client_id, websocket in self._active_connections.items():
            # Check if client has filters
            if client_id in self._client_filters:
                filters = self._client_filters[client_id]
                
                # Apply session filter if specified
                if session_id and filters.get("session_id") and filters["session_id"] != session_id:
                    continue
                
                # Apply event type filter if specified
                if filters.get("event_types") and message.get("type") not in filters["event_types"]:
                    continue
            
            # Send message
            try:
                await websocket.send_json(message)
            except Exception as e:
                logger.warning(f"Failed to broadcast to {client_id}: {str(e)}")
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    def set_client_filters(self, client_id: str, filters: Dict[str, Any]) -> None:
        """Set subscription filters for a client"""
        self._client_filters[client_id] = filters
        logger.debug(f"Filters set for {client_id}: {filters}")
    
    def get_active_clients(self) -> List[str]:
        """Get list of active client IDs"""
        return list(self._active_connections.keys())


# Global connection manager instance
manager = ConnectionManager()


class StatusBroadcaster:
    """Broadcasts status updates to WebSocket clients"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.manager = connection_manager
        self._broadcast_tasks: Dict[str, asyncio.Task] = {}
        self._is_active = False
        
        logger.info("✅ StatusBroadcaster initialized")
    
    async def start_broadcasting(self, session_id: str, controller: Any) -> None:
        """Start broadcasting status updates for a session"""
        if session_id in self._broadcast_tasks:
            logger.warning(f"Broadcast already active for session {session_id}")
            return
        
        # Create broadcast task
        task = asyncio.create_task(
            self._broadcast_loop(session_id, controller)
        )
        self._broadcast_tasks[session_id] = task
        
        logger.info(f"📢 Started status broadcasting for session {session_id}")
    
    async def stop_broadcasting(self, session_id: str) -> None:
        """Stop broadcasting for a session"""
        if session_id in self._broadcast_tasks:
            task = self._broadcast_tasks[session_id]
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            
            del self._broadcast_tasks[session_id]
            logger.info(f"📢 Stopped status broadcasting for session {session_id}")
    
    async def _broadcast_loop(self, session_id: str, controller: Any) -> None:
        """Broadcast status updates periodically"""
        while True:
            try:
                # Get current status
                status = controller.get_status()
                health = controller.get_health_status()
                
                # Prepare status update message
                message = {
                    "type": "status_update",
                    "session_id": session_id,
                    "status": {
                        "is_running": status["is_running"],
                        "is_qa_active": status["is_qa_active"],
                        "is_player_playing": status["is_player_playing"],
                        "uptime_seconds": status["session_info"]["uptime_sec"],
                        "questions_handled": status["session_info"]["metrics"]["questions_handled"],
                        "health": health["is_healthy"]
                    }
                }
                
                # Broadcast to all clients
                await self.manager.broadcast(message, session_id)
                
                # Wait before next update
                await asyncio.sleep(2.0)  # Update every 2 seconds
                
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logger.error(f"Error in broadcast loop for {session_id}: {str(e)}")
                await asyncio.sleep(5.0)  # Error recovery delay
    
    async def broadcast_event(self, session_id: str, event_type: str, event_data: Dict[str, Any]) -> None:
        """Broadcast a specific event"""
        message = {
            "type": "event",
            "event_type": event_type,
            "session_id": session_id,
            "data": event_data
        }
        
        await self.manager.broadcast(message, session_id)


# Global broadcaster instance
broadcaster = StatusBroadcaster(manager)


@router.websocket("/status")
async def websocket_status_endpoint(
    websocket: WebSocket,
    client_id: Optional[str] = Query(None, description="Client identifier"),
    session_id: Optional[str] = Query(None, description="Filter by session ID"),
    event_types: Optional[str] = Query(None, description="Comma-separated event types to subscribe")
):
    """WebSocket endpoint for real-time status updates
    
    Clients can subscribe to status updates with optional filters:
    - session_id: Only receive updates for specific session
    - event_types: Only receive specific types of events
    
    Message format:
    {
        "type": "status_update" | "event" | "connection" | "error",
        "session_id": "session_123",
        "timestamp": "2024-01-01T00:00:00Z",
        "status": {...} | "data": {...}
    }
    """
    # Generate client ID if not provided
    if not client_id:
        client_id = f"ws_client_{int(datetime.utcnow().timestamp())}"
    
    # Connect client
    await manager.connect(websocket, client_id)
    
    # Set up filters
    filters = {}
    if session_id:
        filters["session_id"] = session_id
    if event_types:
        filters["event_types"] = event_types.split(",")
    
    if filters:
        manager.set_client_filters(client_id, filters)
    
    try:
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for client messages (ping/pong, filter updates, etc.)
                data = await websocket.receive_json()
                
                # Handle different message types
                if data.get("type") == "ping":
                    # Respond to ping
                    await manager.send_personal_message(
                        {"type": "pong", "timestamp": datetime.utcnow().isoformat()},
                        client_id
                    )
                
                elif data.get("type") == "update_filters":
                    # Update client filters
                    new_filters = data.get("filters", {})
                    manager.set_client_filters(client_id, new_filters)
                    
                    await manager.send_personal_message(
                        {
                            "type": "filters_updated",
                            "filters": new_filters,
                            "timestamp": datetime.utcnow().isoformat()
                        },
                        client_id
                    )
                
                elif data.get("type") == "subscribe_session":
                    # Subscribe to specific session
                    session_to_subscribe = data.get("session_id")
                    if session_to_subscribe:
                        filters["session_id"] = session_to_subscribe
                        manager.set_client_filters(client_id, filters)
                        
                        await manager.send_personal_message(
                            {
                                "type": "subscribed",
                                "session_id": session_to_subscribe,
                                "timestamp": datetime.utcnow().isoformat()
                            },
                            client_id
                        )
                
            except WebSocketDisconnect:
                manager.disconnect(client_id)
                break
            except json.JSONDecodeError:
                # Invalid JSON received
                await manager.send_personal_message(
                    {
                        "type": "error",
                        "error": "Invalid JSON format",
                        "timestamp": datetime.utcnow().isoformat()
                    },
                    client_id
                )
            except Exception as e:
                logger.error(f"WebSocket error for {client_id}: {str(e)}")
                await manager.send_personal_message(
                    {
                        "type": "error",
                        "error": str(e),
                        "timestamp": datetime.utcnow().isoformat()
                    },
                    client_id
                )
                
    except Exception as e:
        logger.error(f"WebSocket connection error: {str(e)}")
    finally:
        manager.disconnect(client_id)


# API for internal use to broadcast events
async def broadcast_session_event(session_id: str, event_type: str, event_data: Dict[str, Any]) -> None:
    """Internal API to broadcast session events
    
    This can be called from controllers to notify WebSocket clients of events.
    """
    await broadcaster.broadcast_event(session_id, event_type, event_data)


# Export for use in controllers
__all__ = ["router", "broadcaster", "broadcast_session_event"]