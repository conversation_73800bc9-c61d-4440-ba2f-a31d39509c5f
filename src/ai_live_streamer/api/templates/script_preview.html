<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播脚本预览 - {stream_title}</title>
    
    <!-- Global Design System CSS -->
    <link rel="stylesheet" href="./static/css/global-design-system.css?v=1.0.0">
    
    <!-- UI Components Library -->
    <script src="./static/js/ui-components.js?v=1.0.0"></script>
    
    <style>
        /* Page Specific Styles */
        .script-preview-header {
            background: var(--primary-gradient);
            color: var(--text-inverse);
            padding: var(--spacing-lg) 0;
            margin-bottom: var(--spacing-xl);
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.25);
            position: relative;
            overflow: hidden;
        }
        
        .script-preview-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -10%;
            width: 50%;
            height: 200%;
            background: rgba(255, 255, 255, 0.05);
            transform: rotate(45deg);
        }
        
        .script-preview-header .container {
            position: relative;
            z-index: 1;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            margin-bottom: var(--spacing-3);
            font-size: var(--text-sm);
            opacity: 0.9;
        }
        
        .breadcrumb a {
            color: inherit;
            text-decoration: none;
            transition: opacity var(--transition-fast);
        }
        
        .breadcrumb a:hover {
            opacity: 0.8;
            text-decoration: underline;
        }
        
        .script-title {
            font-size: var(--text-4xl);
            font-weight: var(--font-weight-bold);
            margin: 0 0 var(--spacing-2) 0;
            line-height: var(--line-height-tight);
        }
        
        .script-subtitle {
            font-size: var(--text-lg);
            opacity: 0.9;
            margin: 0;
        }
        
        .stats-grid {
            margin-bottom: var(--spacing-xl);
        }
        
        .timeline-container {
            margin-bottom: var(--spacing-xl);
        }
        
        .script-segment {
            position: relative;
        }
        
        .script-segment::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-color);
            border-radius: var(--radius-sm);
        }
        
        .script-segment.priority-high::before {
            background: var(--error-color);
        }
        
        .script-segment.priority-medium::before {
            background: var(--warning-color);
        }
        
        .script-segment.priority-low::before {
            background: var(--success-color);
        }
        
        .segment-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-md);
        }
        
        .segment-type-badge {
            font-size: var(--text-xs);
            padding: 2px 8px;
            border-radius: var(--radius-full);
            font-weight: var(--font-weight-semibold);
            text-transform: uppercase;
            letter-spacing: var(--letter-spacing-wide);
        }
        
        .segment-type-opening {
            background: var(--primary-bg);
            color: var(--primary-color);
        }
        
        .segment-type-selling_point {
            background: var(--success-bg);
            color: var(--success-color);
        }
        
        .segment-type-price_announcement {
            background: var(--warning-bg);
            color: var(--warning-color);
        }
        
        .segment-type-interaction {
            background: var(--info-bg);
            color: var(--info-color);
        }
        
        .segment-type-closing {
            background: var(--error-bg);
            color: var(--error-color);
        }
        
        .segment-content {
            background: var(--bg-secondary);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin: var(--spacing-md) 0;
            white-space: pre-line;
            line-height: var(--line-height-relaxed);
            color: var(--text-primary);
        }
        
        .segment-duration {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-1);
            background: var(--primary-color);
            color: var(--text-inverse);
            padding: var(--spacing-1) var(--spacing-2);
            border-radius: var(--radius-full);
            font-size: var(--text-xs);
            font-weight: var(--font-weight-medium);
        }
        
        .segment-meta {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-2);
            margin-top: var(--spacing-md);
            font-size: var(--text-sm);
            color: var(--text-secondary);
        }
        
        .interaction-point {
            background: var(--info-bg);
            border: 2px dashed var(--info-color);
            padding: var(--spacing-md);
            margin: var(--spacing-md) 0;
            border-radius: var(--radius-md);
            text-align: center;
            font-weight: var(--font-weight-semibold);
            color: var(--info-color);
        }
        
        .back-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
        }
        
        .export-actions {
            display: flex;
            gap: var(--spacing-2);
        }
        
        /* 脚本段落编号 */
        .segment-number {
            position: absolute;
            left: calc(-1 * var(--spacing-3));
            top: var(--spacing-3);
            width: var(--spacing-5);
            height: var(--spacing-5);
            background: var(--primary-color);
            color: var(--text-inverse);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--text-xs);
            font-weight: var(--font-weight-bold);
            box-shadow: var(--shadow-md);
        }
        
        /* 时间线视觉效果 */
        .script-segments {
            position: relative;
            padding-left: var(--spacing-6);
        }
        
        .script-segments::before {
            content: '';
            position: absolute;
            left: var(--spacing-5);
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--border-color), transparent);
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .segment-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-2);
            }
            
            .back-actions {
                flex-direction: column;
                gap: var(--spacing-3);
                align-items: stretch;
            }
            
            .export-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="script-preview-header">
        <div class="container">
            <div class="breadcrumb">
                <a href="/">首页</a>
                <span>></span>
                <a href="/#scripts">脚本管理</a>
                <span>></span>
                <span>脚本预览</span>
            </div>
            <h1 class="script-title">{stream_title}</h1>
            <div class="script-subtitle">{product_brand} {product_name} 直播脚本</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Action Bar -->
        <div class="back-actions">
            <a href="/#scripts" class="btn btn-secondary">
                ← 返回脚本列表
            </a>
            <div class="export-actions">
                <button class="btn btn-secondary" onclick="exportScript('json')">
                    📄 导出JSON
                </button>
                <button class="btn btn-secondary" onclick="exportScript('text')">
                    📝 导出文本
                </button>
                <button class="btn btn-primary" onclick="window.print()">
                    🖨️ 打印脚本
                </button>
            </div>
        </div>

        <!-- Script Statistics -->
        <div class="info-cards-grid stats-grid">
            <div class="info-card">
                <div class="info-card-stat-item">
                    <span class="info-card-stat-value">{total_segments}</span>
                    <span class="info-card-stat-label">脚本片段</span>
                </div>
            </div>
            <div class="info-card">
                <div class="info-card-stat-item">
                    <span class="info-card-stat-value">{total_duration_minutes}</span>
                    <span class="info-card-stat-label">预计时长 (分钟)</span>
                </div>
            </div>
            <div class="info-card">
                <div class="info-card-stat-item">
                    <span class="info-card-stat-value">{interaction_points_count}</span>
                    <span class="info-card-stat-label">互动节点</span>
                </div>
            </div>
            <div class="info-card">
                <div class="info-card-stat-item">
                    <span class="info-card-stat-value">{selling_points_count}</span>
                    <span class="info-card-stat-label">卖点展示</span>
                </div>
            </div>
        </div>

        <!-- Script Timeline -->
        <div class="timeline-container">
            <div class="info-card">
                <div class="info-card-header">
                    <h2 class="info-card-title">脚本时间线</h2>
                    <div class="info-card-meta">生成时间: {generation_time}</div>
                </div>
                
                <div class="script-segments">
                    {segments_html}
                </div>
                
                <!-- Interaction Points -->
                {interaction_points_html}
            </div>
        </div>
    </div>

    <script>
        // Export functionality
        function exportScript(format) {
            const formId = new URLSearchParams(window.location.search).get('form_id') || 
                          window.location.pathname.split('/').pop();
            
            const exportUrl = `/api/script-preview/export`;
            const payload = {
                form_id: formId,
                export_format: format,
                include_metadata: true
            };
            
            fetch(exportUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Create download
                    const blob = new Blob([data.content], { 
                        type: format === 'json' ? 'application/json' : 'text/plain' 
                    });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `script_${formId}.${format}`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    
                    UI.Toast.success(`脚本已导出为 ${format.toUpperCase()} 格式`);
                } else {
                    UI.Toast.error('导出失败: ' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Export error:', error);
                UI.Toast.error('导出失败: ' + error.message);
            });
        }

        // Navigation enhancement
        document.addEventListener('DOMContentLoaded', function() {
            // Set active tab for navigation
            localStorage.setItem('activeTab', 'scripts');
            
            // Smooth scroll for interaction points
            const interactionPoints = document.querySelectorAll('.interaction-point');
            interactionPoints.forEach((point, index) => {
                point.addEventListener('click', () => {
                    point.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        point.style.transform = 'scale(1)';
                    }, 200);
                });
            });
        });
        
        // Print styles enhancement
        window.addEventListener('beforeprint', function() {
            document.body.classList.add('printing');
        });
        
        window.addEventListener('afterprint', function() {
            document.body.classList.remove('printing');
        });
    </script>
    
    <!-- Print Styles -->
    <style media="print">
        .script-preview-header,
        .back-actions,
        .btn {
            display: none !important;
        }
        
        .container {
            max-width: 100%;
            margin: 0;
            padding: 0;
        }
        
        .info-card {
            box-shadow: none;
            border: 1px solid #ddd;
            page-break-inside: avoid;
        }
        
        .script-segment {
            page-break-inside: avoid;
            margin-bottom: 20px;
        }
        
        .segment-content {
            background: #f9f9f9 !important;
        }
    </style>
</body>
</html>