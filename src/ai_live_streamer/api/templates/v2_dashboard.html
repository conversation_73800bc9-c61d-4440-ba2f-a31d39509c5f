<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI直播系统 V2 - 控制台</title>
    
    <!-- Global Design System CSS -->
    <link rel="stylesheet" href="./static/css/global-design-system.css?v=1.0.0">
    
    <!-- UI Components Library -->
    <script src="./static/js/ui-components.js?v=1.0.0"></script>
    
    <style>
        /* V2 Dashboard Specific Styles - Using Design System */
        
        /* Enhanced Data Visualization Colors */
        :root {
            /* Advanced gradients for data viz */
            --data-gradient-1: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --data-gradient-2: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            --data-gradient-3: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%);
            --data-gradient-4: linear-gradient(135deg, #fc8181 0%, #e53e3e 100%);
            --data-gradient-5: linear-gradient(135deg, #63b3ed 0%, #3182ce 100%);
            
            /* Status indicator colors with better contrast */
            --status-online-bg: linear-gradient(135deg, #d4f4dd 0%, #c6f6d5 100%);
            --status-warning-bg: linear-gradient(135deg, #fff5e6 0%, #fef3c7 100%);
            --status-error-bg: linear-gradient(135deg, #fee 0%, #fed7d7 100%);
            
            /* Professional accent colors */
            --accent-emerald: #10b981;
            --accent-amber: #f59e0b;
            --accent-rose: #f43f5e;
            --accent-cyan: #06b6d4;
            --accent-violet: #8b5cf6;
            
            /* Data visualization patterns */
            --chart-primary: #667eea;
            --chart-secondary: #48bb78;
            --chart-tertiary: #f6ad55;
            --chart-quaternary: #fc8181;
            
            /* Glass morphism effects */
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        
        body {
            background: var(--primary-gradient);
            min-height: 100vh;
            padding: var(--spacing-6); /* 24px */
            font-family: var(--font-sans);
            font-size: var(--text-base);
            line-height: var(--line-height-relaxed);
            letter-spacing: var(--letter-spacing-normal);
        }
        
        .v2-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--spacing-4);
        }
        
        .v2-header {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-8); /* 32px */
            margin-bottom: var(--spacing-6); /* 24px */
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--border-light);
            position: relative;
            overflow: hidden;
        }
        
        .v2-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }
        
        .v2-header::after {
            content: '';
            position: absolute;
            top: -2px;
            right: -2px;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
            border-radius: 50%;
        }
        
        .v2-header h1 {
            font-family: var(--font-heading);
            color: var(--text-primary);
            font-size: var(--text-3xl); /* 30px */
            font-weight: var(--font-weight-bold);
            line-height: var(--line-height-tight);
            letter-spacing: var(--letter-spacing-tight);
            display: flex;
            align-items: center;
            gap: var(--spacing-4); /* 16px */
            margin: 0;
        }
        
        .v2-version-badge {
            background: var(--primary-gradient);
            color: var(--text-inverse);
            padding: var(--spacing-1) var(--spacing-4); /* 4px 16px */
            border-radius: var(--radius-full);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-semibold);
            letter-spacing: var(--letter-spacing-wide);
            box-shadow: var(--shadow-sm);
        }
        
        .v2-main-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-8); /* 32px */
            max-width: 1200px;
            margin: 0 auto;
        }
        
        /* 响应式网格 */
        @media (max-width: 768px) {
            .v2-main-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-6);
            }
        }
        
        /* Use unified info-card system */
        .v2-card {
            /* Extend info-card base styles */
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-6);
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-light);
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
        }
        
        @media (max-width: 480px) {
            .v2-card {
                padding: var(--spacing-4); /* 16px 在小屏幕上 */
            }
        }
        
        .v2-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: transform var(--transition-base);
        }
        
        .v2-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-card-hover);
        }
        
        .v2-card:hover::before {
            transform: scaleX(1);
        }
        
        .v2-card h2 {
            font-family: var(--font-heading);
            color: var(--text-primary);
            font-size: var(--text-xl); /* 20px */
            font-weight: var(--font-weight-semibold);
            line-height: var(--line-height-tight);
            margin-bottom: var(--spacing-5); /* 20px */
            padding-bottom: var(--spacing-3); /* 12px */
            border-bottom: 2px solid var(--border-light);
            display: flex;
            align-items: center;
            gap: var(--spacing-2); /* 8px */
        }
        
        .v2-status-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-3); /* 12px */
            margin-bottom: var(--spacing-6); /* 24px */
        }
        
        @media (max-width: 640px) {
            .v2-status-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-2);
            }
        }
        
        .v2-status-item {
            background: var(--bg-secondary);
            padding: var(--spacing-4); /* 16px */
            border-radius: var(--radius-md);
            border-left: 4px solid var(--primary-color);
            transition: all var(--transition-fast);
            position: relative;
            backdrop-filter: blur(5px);
            border: 1px solid var(--border-light);
        }
        
        .v2-status-item:hover {
            background: var(--primary-bg);
            transform: translateX(4px);
            box-shadow: var(--shadow-md);
            border-left-color: var(--accent-emerald);
        }
        
        .v2-status-item:nth-child(even) {
            border-left-color: var(--accent-cyan);
        }
        
        .v2-status-item:nth-child(even):hover {
            border-left-color: var(--accent-violet);
        }
        
        .v2-status-item .label {
            font-size: var(--text-xs); /* 12px */
            color: var(--text-secondary);
            margin-bottom: var(--spacing-1); /* 4px */
            font-weight: var(--font-weight-medium);
            text-transform: uppercase;
            letter-spacing: var(--letter-spacing-wider);
        }
        
        .v2-status-item .value {
            font-size: var(--text-base); /* 16px */
            font-weight: var(--font-weight-bold);
            line-height: var(--line-height-tight);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-1); /* 4px */
        }
        
        .v2-status-online {
            color: var(--success-color);
        }
        
        .v2-status-offline {
            color: var(--error-color);
        }
        
        .v2-status-warning {
            color: var(--warning-color);
        }
        
        .v2-feature-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .v2-feature-list li {
            padding: var(--spacing-4); /* 16px */
            background: var(--bg-secondary);
            margin-bottom: var(--spacing-3); /* 12px */
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-3); /* 12px */
            transition: all var(--transition-fast);
            border: 1px solid var(--border-light);
            font-size: var(--text-sm);
            line-height: var(--line-height-relaxed);
        }
        
        .v2-feature-list li:hover {
            background: var(--primary-bg);
            transform: translateX(4px);
        }
        
        .v2-feature-list li::before {
            content: "✨";
            font-size: 1.125rem;
        }
        
        .v2-client-list {
            max-height: 300px;
            overflow-y: auto;
            padding-right: var(--spacing-xs);
        }
        
        .v2-client-list::-webkit-scrollbar {
            width: 6px;
        }
        
        .v2-client-list::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: var(--radius-sm);
        }
        
        .v2-client-list::-webkit-scrollbar-thumb {
            background: var(--gray-300);
            border-radius: var(--radius-sm);
        }
        
        .v2-client-item {
            background: var(--bg-secondary);
            padding: var(--spacing-4); /* 16px */
            margin-bottom: var(--spacing-3); /* 12px */
            border-radius: var(--radius-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid var(--border-light);
            transition: all var(--transition-fast);
        }
        
        .v2-client-item:hover {
            background: var(--primary-bg);
            border-color: var(--primary-color);
        }
        
        .v2-client-id {
            font-family: var(--font-mono);
            font-size: var(--text-xs); /* 12px */
            line-height: var(--line-height-tight);
            color: var(--text-secondary);
            background: var(--bg-primary);
            padding: var(--spacing-1) var(--spacing-2); /* 4px 8px */
            border-radius: var(--radius-sm);
        }
        
        .v2-client-status {
            padding: var(--spacing-1) var(--spacing-2); /* 4px 8px */
            border-radius: var(--radius-sm);
            font-size: var(--text-xs); /* 12px */
            font-weight: var(--font-weight-semibold);
            line-height: var(--line-height-tight);
            text-transform: uppercase;
            letter-spacing: var(--letter-spacing-wider);
        }
        
        .v2-status-healthy {
            background: var(--status-online-bg);
            color: var(--accent-emerald);
            border: 1px solid var(--accent-emerald);
            position: relative;
        }
        
        .v2-status-healthy::before {
            content: '●';
            margin-right: var(--spacing-xs);
            animation: pulse 2s infinite;
        }
        
        .v2-status-at-risk {
            background: var(--status-warning-bg);
            color: var(--accent-amber);
            border: 1px solid var(--accent-amber);
        }
        
        .v2-status-at-risk::before {
            content: '⚠';
            margin-right: var(--spacing-xs);
        }
        
        .v2-status-depleted {
            background: var(--status-error-bg);
            color: var(--accent-rose);
            border: 1px solid var(--accent-rose);
        }
        
        .v2-status-depleted::before {
            content: '✖';
            margin-right: var(--spacing-xs);
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
        
        .v2-control-buttons {
            display: flex;
            gap: var(--spacing-2); /* 8px */
            margin-top: var(--spacing-6); /* 24px */
            flex-wrap: wrap;
        }
        
        @media (max-width: 480px) {
            .v2-control-buttons {
                flex-direction: column;
                gap: var(--spacing-3);
            }
        }
        
        .v2-btn {
            padding: var(--spacing-3) var(--spacing-6); /* 12px 24px */
            border: none;
            border-radius: var(--radius-md);
            font-size: var(--text-sm); /* 14px */
            font-weight: var(--font-weight-semibold);
            line-height: var(--line-height-tight);
            letter-spacing: var(--letter-spacing-wide);
            cursor: pointer;
            transition: all var(--transition-base);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-2); /* 8px */
            border: 1px solid transparent;
            min-height: 2.5rem; /* 40px */
            white-space: nowrap;
        }
        
        .v2-btn-primary {
            background: var(--primary-gradient);
            color: var(--text-inverse);
            box-shadow: var(--shadow-sm);
        }
        
        .v2-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            filter: brightness(1.05);
        }
        
        .v2-btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border-color: var(--border-color);
        }
        
        .v2-btn-secondary:hover {
            background: var(--primary-bg);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .v2-metrics-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-4); /* 16px */
            margin-top: var(--spacing-6); /* 24px */
        }
        
        @media (max-width: 768px) {
            .v2-metrics-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-3);
            }
        }
        
        .v2-metric-card {
            background: var(--bg-primary);
            padding: var(--spacing-6); /* 24px */
            border-radius: var(--radius-lg);
            text-align: center;
            border: 1px solid var(--border-light);
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        @media (max-width: 480px) {
            .v2-metric-card {
                padding: var(--spacing-4);
            }
        }
        
        .v2-metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--data-gradient-1);
            transform: scaleX(0);
            transition: transform var(--transition-base);
        }
        
        .v2-metric-card:nth-child(2)::before {
            background: var(--data-gradient-2);
        }
        
        .v2-metric-card:nth-child(3)::before {
            background: var(--data-gradient-3);
        }
        
        .v2-metric-card:hover {
            transform: translateY(-6px);
            box-shadow: var(--shadow-xl);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .v2-metric-card:hover::before {
            transform: scaleX(1);
        }
        
        .v2-metric-value {
            font-family: var(--font-heading);
            font-size: var(--text-4xl); /* 36px */
            font-weight: var(--font-weight-bold);
            line-height: var(--line-height-tight);
            color: var(--primary-color);
            margin-bottom: var(--spacing-2); /* 8px */
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 2;
        }
        
        .v2-metric-label {
            font-size: var(--text-xs); /* 12px */
            color: var(--text-secondary);
            font-weight: var(--font-weight-semibold);
            text-transform: uppercase;
            letter-spacing: var(--letter-spacing-wider);
            line-height: var(--line-height-tight);
            position: relative;
            z-index: 2;
        }
        
        /* Progress Ring for Metrics */
        .v2-metric-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            opacity: 0.1;
            z-index: 1;
        }
        
        .v2-ring-circle {
            fill: none;
            stroke-width: 3;
            stroke-linecap: round;
        }
        
        .v2-ring-bg {
            stroke: var(--gray-200);
        }
        
        .v2-ring-progress {
            stroke: var(--primary-color);
            stroke-dasharray: 251.2;
            stroke-dashoffset: 251.2;
            animation: progressRing 2s ease-out forwards;
            animation-delay: 0.5s;
        }
        
        .v2-metric-card:nth-child(2) .v2-ring-progress {
            stroke: var(--accent-emerald);
        }
        
        .v2-metric-card:nth-child(3) .v2-ring-progress {
            stroke: var(--accent-amber);
        }
        
        @keyframes progressRing {
            to {
                stroke-dashoffset: 125.6; /* 50% progress */
            }
        }
        
        /* Trend indicators */
        .v2-trend-indicator {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            font-size: 0.75rem;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            background: var(--success-bg);
            color: var(--success-color);
            z-index: 3;
        }
        
        .v2-trend-down {
            background: var(--error-bg);
            color: var(--error-color);
        }
        
        .v2-trend-stable {
            background: var(--warning-bg);
            color: var(--warning-color);
        }
        
        /* Advanced Micro-interactions and Animations */
        
        /* Sophisticated page load animations */
        .v2-container {
            animation: fadeInScale 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        @keyframes fadeInScale {
            0% {
                opacity: 0;
                transform: scale(0.95);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        /* Card entrance animations with stagger */
        .v2-card {
            animation: slideInUp 0.6s cubic-bezier(0.23, 1, 0.32, 1) both;
        }
        
        .v2-card:nth-child(1) { animation-delay: 0.1s; }
        .v2-card:nth-child(2) { animation-delay: 0.2s; }
        .v2-card:nth-child(3) { animation-delay: 0.3s; }
        .v2-card:nth-child(4) { animation-delay: 0.4s; }
        
        @keyframes slideInUp {
            0% {
                opacity: 0;
                transform: translateY(30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Enhanced button interactions */
        .v2-btn {
            position: relative;
            overflow: hidden;
        }
        
        .v2-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s ease, height 0.6s ease;
        }
        
        .v2-btn:active::after {
            width: 300px;
            height: 300px;
        }
        
        /* Subtle parallax effect for feature list */
        .v2-feature-list li {
            transform: translateZ(0);
            backface-visibility: hidden;
        }
        
        .v2-feature-list li:hover {
            animation: gentleFloat 2s ease-in-out infinite;
        }
        
        @keyframes gentleFloat {
            0%, 100% {
                transform: translateX(0px) translateY(0px);
            }
            25% {
                transform: translateX(2px) translateY(-2px);
            }
            50% {
                transform: translateX(4px) translateY(0px);
            }
            75% {
                transform: translateX(2px) translateY(2px);
            }
        }
        
        /* Interactive loading states */
        .v2-loading {
            position: relative;
        }
        
        .v2-loading::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, transparent, var(--primary-color), transparent);
            border-radius: inherit;
            z-index: -1;
            animation: rotateGlow 2s linear infinite;
        }
        
        @keyframes rotateGlow {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        /* Sophisticated hover states for status items */
        .v2-status-item {
            transform-origin: left center;
        }
        
        .v2-status-item::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: width 0.4s ease;
        }
        
        .v2-status-item:hover::after {
            width: 100%;
        }
        
        /* Breathing animation for online status */
        .v2-status-online {
            animation: breathe 3s ease-in-out infinite;
        }
        
        @keyframes breathe {
            0%, 100% {
                box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
            }
            50% {
                box-shadow: 0 0 20px rgba(16, 185, 129, 0.6);
            }
        }
        
        /* Smooth metric value updates */
        .v2-metric-value {
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .v2-metric-value.updating {
            transform: scale(1.1);
            filter: brightness(1.2);
        }
        
        /* Enhanced responsive design */
        @media (max-width: 768px) {
            .v2-main-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }
            
            .v2-status-grid {
                grid-template-columns: 1fr;
            }
            
            .v2-metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .v2-control-buttons {
                flex-direction: column;
            }
            
            .v2-btn {
                justify-content: center;
            }
        }
        
        @media (max-width: 480px) {
            body {
                padding: var(--spacing-md);
            }
            
            .v2-header {
                padding: var(--spacing-lg);
            }
            
            .v2-card {
                padding: var(--spacing-md);
            }
        }
        
        /* Additional V2 specific components */
        .v2-status-feedback {
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            border-radius: var(--radius-md);
            background: var(--info-bg);
            color: var(--info-color);
            font-size: 0.875rem;
            border: 1px solid var(--info-color);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .v2-qa-section {
            margin-top: var(--spacing-lg);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--border-light);
        }
        
        .v2-section-title {
            font-family: var(--font-heading);
            font-size: var(--text-lg); /* 18px */
            font-weight: var(--font-weight-semibold);
            line-height: var(--line-height-tight);
            color: var(--text-primary);
            margin-bottom: var(--spacing-4); /* 16px */
        }
        
        .v2-input {
            width: 100%;
            padding: var(--spacing-3) var(--spacing-4); /* 12px 16px */
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-family: var(--font-sans);
            font-size: var(--text-sm); /* 14px */
            font-weight: var(--font-weight-normal);
            line-height: var(--line-height-normal);
            margin-bottom: var(--spacing-4); /* 16px */
            transition: all var(--transition-fast);
            background: var(--bg-primary);
            min-height: 2.5rem; /* 40px */
        }
        
        .v2-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-bg);
        }
        
        .v2-input::placeholder {
            color: var(--text-muted);
        }
    </style>
</head>
<body>
    <!-- Page Header with Navigation -->
    <div style="background: var(--bg-primary); border-bottom: 1px solid var(--border-color); padding: 16px 0; margin-bottom: 24px;">
        <div class="v2-container">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; opacity: 0.8; font-size: 0.875rem;">
                        <a href="./" style="color: var(--primary-color); text-decoration: none;">首页</a>
                        <span>></span>
                        <span>V2控制台</span>
                    </div>
                    <h1 style="margin: 0;">
                        🚀 AI直播系统控制台
                        <span class="v2-version-badge">V2.0</span>
                    </h1>
                </div>
                <div>
                    <a href="./" class="btn btn-secondary">返回主控制台</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="v2-container">
        
        <div class="v2-main-grid">
            <!-- 系统状态 -->
            <div class="info-card v2-card">
                <h2 class="info-card-title">📊 系统状态</h2>
                <div class="v2-status-grid">
                    <div class="v2-status-item">
                        <div class="label">服务状态</div>
                        <div class="value v2-status-online" id="service-status">在线</div>
                    </div>
                    <div class="v2-status-item">
                        <div class="label">WebSocket V2</div>
                        <div class="value" id="ws-status">未连接</div>
                    </div>
                    <div class="v2-status-item">
                        <div class="label">播放列表模式</div>
                        <div class="value" id="playlist-mode">智能模式</div>
                    </div>
                    <div class="v2-status-item">
                        <div class="label">QA插入策略</div>
                        <div class="value" id="qa-strategy">MIN策略</div>
                    </div>
                </div>
                
                <div class="v2-metrics-grid">
                    <div class="v2-metric-card">
                        <svg class="v2-metric-ring">
                            <circle class="v2-ring-circle v2-ring-bg" cx="40" cy="40" r="36"></circle>
                            <circle class="v2-ring-circle v2-ring-progress" cx="40" cy="40" r="36"></circle>
                        </svg>
                        <div class="v2-trend-indicator">↗ +15%</div>
                        <div class="v2-metric-value" id="cache-hits">0</div>
                        <div class="v2-metric-label">缓存命中</div>
                    </div>
                    <div class="v2-metric-card">
                        <svg class="v2-metric-ring">
                            <circle class="v2-ring-circle v2-ring-bg" cx="40" cy="40" r="36"></circle>
                            <circle class="v2-ring-circle v2-ring-progress" cx="40" cy="40" r="36"></circle>
                        </svg>
                        <div class="v2-trend-indicator v2-trend-stable">→ 0%</div>
                        <div class="v2-metric-value" id="synthesis-queue">0</div>
                        <div class="v2-metric-label">合成队列</div>
                    </div>
                    <div class="v2-metric-card">
                        <svg class="v2-metric-ring">
                            <circle class="v2-ring-circle v2-ring-bg" cx="40" cy="40" r="36"></circle>
                            <circle class="v2-ring-circle v2-ring-progress" cx="40" cy="40" r="36"></circle>
                        </svg>
                        <div class="v2-trend-indicator">↗ +3</div>
                        <div class="v2-metric-value" id="active-clients">0</div>
                        <div class="v2-metric-label">活跃客户端</div>
                    </div>
                </div>
            </div>
            
            <!-- V2新功能 -->
            <div class="info-card v2-card">
                <h2 class="info-card-title">✨ V2架构特性</h2>
                <ul class="v2-feature-list">
                    <li>服务器端智能播放列表管理</li>
                    <li>WebSocket V2双向通信协议</li>
                    <li>MIN策略智能QA插入</li>
                    <li>主动内容预合成</li>
                    <li>客户端缓冲区健康监控</li>
                    <li>状态恢复与快照管理</li>
                    <li>Future-based缓存防止踩踏</li>
                    <li>实时系统性能监控</li>
                </ul>
            </div>
            
            <!-- 客户端连接 -->
            <div class="info-card v2-card">
                <h2 class="info-card-title">🔗 客户端连接</h2>
                <div class="v2-client-list" id="client-list">
                    <div class="v2-client-item">
                        <span class="v2-client-id">等待客户端连接...</span>
                    </div>
                </div>
                <div class="v2-control-buttons">
                    <button class="v2-btn v2-btn-primary" onclick="connectWebSocket()">连接WebSocket</button>
                    <button class="v2-btn v2-btn-secondary" onclick="refreshClients()">刷新列表</button>
                </div>
            </div>
            
            <!-- 播放控制 -->
            <div class="info-card v2-card">
                <h2 class="info-card-title">🎮 播放控制</h2>
                <!-- 状态反馈区域 -->
                <div id="status-feedback" class="v2-status-feedback" style="display: none;">
                    <span id="status-icon">ℹ️</span>
                    <span id="status-message">准备就绪</span>
                </div>
                <div class="v2-control-buttons">
                    <button class="v2-btn v2-btn-primary" onclick="startStream()">开始直播</button>
                    <button class="v2-btn v2-btn-secondary" onclick="pauseStream()">暂停</button>
                    <button class="v2-btn v2-btn-secondary" onclick="resumeStream()">恢复</button>
                    <button class="v2-btn v2-btn-secondary" onclick="stopStream()">停止</button>
                </div>
                
                <div class="v2-qa-section">
                    <h3 class="v2-section-title">插入QA测试</h3>
                    <input type="text" id="qa-input" class="v2-input" placeholder="输入测试问题...">
                    <button class="v2-btn v2-btn-primary" onclick="insertQA()">插入QA</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Session Manager - 统一的会话状态管理 -->
    <script src="/static/js/session_manager.js?v=1.0.0"></script>
    
    <script>
        // 封装所有流相关的状态到单一对象 (v2DashboardState 避免命名冲突)
        const v2DashboardState = {
            sessionId: null,
            clientId: 'v2-dashboard-' + Date.now(),
            ws: null,
            isConnected: false,
            isConnecting: false,
            isStarting: false
        };
        
        // UI状态管理
        const uiState = {
            statusFeedback: null,
            startButton: null,
            connectButton: null,
            pauseButton: null,
            resumeButton: null,
            stopButton: null
        };
        
        // 更新状态反馈UI
        function updateStatusFeedback(message, type = 'info') {
            const feedbackEl = document.getElementById('status-feedback');
            const iconEl = document.getElementById('status-icon');
            const messageEl = document.getElementById('status-message');
            
            if (!feedbackEl) return;
            
            feedbackEl.style.display = 'block';
            messageEl.textContent = message;
            
            // 根据类型设置样式和图标
            switch(type) {
                case 'success':
                    feedbackEl.style.background = '#dcfce7';
                    feedbackEl.style.color = '#166534';
                    iconEl.textContent = '✅';
                    break;
                case 'error':
                    feedbackEl.style.background = '#fee2e2';
                    feedbackEl.style.color = '#991b1b';
                    iconEl.textContent = '❌';
                    break;
                case 'warning':
                    feedbackEl.style.background = '#fef3c7';
                    feedbackEl.style.color = '#92400e';
                    iconEl.textContent = '⚠️';
                    break;
                case 'loading':
                    feedbackEl.style.background = '#e0f2fe';
                    feedbackEl.style.color = '#075985';
                    iconEl.textContent = '⏳';
                    break;
                default:
                    feedbackEl.style.background = '#f8f9fa';
                    feedbackEl.style.color = '#666';
                    iconEl.textContent = 'ℹ️';
            }
        }
        
        // 更新按钮状态
        function setButtonState(buttonId, enabled) {
            const button = document.querySelector(`button[onclick*="${buttonId}"]`);
            if (button) {
                button.disabled = !enabled;
                if (!enabled) {
                    button.style.opacity = '0.5';
                    button.style.cursor = 'not-allowed';
                } else {
                    button.style.opacity = '1';
                    button.style.cursor = 'pointer';
                }
            }
        }
        
        function connectWebSocket() {
            // 检查是否已连接
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                console.log('WebSocket already connected');
                updateStatusFeedback('WebSocket 已连接', 'info');
                return;
            }
            
            // 检查是否正在连接
            if (v2DashboardState.isConnecting) {
                console.log('WebSocket connection already in progress');
                return;
            }
            
            // 验证 session ID
            if (!v2DashboardState.sessionId) {
                console.error('No session ID available. Please start stream first.');
                updateStatusFeedback('请先点击"开始直播"按钮', 'warning');
                return;
            }
            
            v2DashboardState.isConnecting = true;
            updateStatusFeedback('正在连接 WebSocket...', 'loading');
            
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/v2/stream?client_id=${v2DashboardState.clientId}&session_id=${v2DashboardState.sessionId}`;
            
            console.log('Connecting to:', wsUrl);
            
            v2DashboardState.ws = new WebSocket(wsUrl);
            
            v2DashboardState.ws.onopen = () => {
                console.log('WebSocket V2 connected');
                v2DashboardState.isConnected = true;
                v2DashboardState.isConnecting = false;
                document.getElementById('ws-status').textContent = '已连接';
                document.getElementById('ws-status').className = 'value status-online';
                updateStatusFeedback('WebSocket 连接成功', 'success');
                setButtonState('connectWebSocket', false);
                setButtonState('pauseStream', true);
                setButtonState('resumeStream', true);
                setButtonState('stopStream', true);
            };
            
            v2DashboardState.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                } catch (e) {
                    console.log('Received binary data:', event.data);
                }
            };
            
            v2DashboardState.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                v2DashboardState.isConnecting = false;
                document.getElementById('ws-status').textContent = '连接错误';
                document.getElementById('ws-status').className = 'value status-offline';
                updateStatusFeedback('WebSocket 连接错误', 'error');
                setButtonState('connectWebSocket', true);
                setButtonState('pauseStream', false);
                setButtonState('resumeStream', false);
                setButtonState('stopStream', false);
            };
            
            v2DashboardState.ws.onclose = () => {
                console.log('WebSocket closed');
                v2DashboardState.isConnected = false;
                v2DashboardState.isConnecting = false;
                v2DashboardState.ws = null;
                document.getElementById('ws-status').textContent = '未连接';
                document.getElementById('ws-status').className = 'value status-offline';
                updateStatusFeedback('WebSocket 已断开', 'info');
                setButtonState('connectWebSocket', true);
                setButtonState('pauseStream', false);
                setButtonState('resumeStream', false);
                setButtonState('stopStream', false);
            };
        }
        
        function handleMessage(data) {
            switch(data.type) {
                case 'status':
                    updateStatus(data);
                    break;
                case 'clients':
                    updateClientList(data.clients);
                    break;
                case 'metrics':
                    updateMetrics(data);
                    break;
            }
        }
        
        function updateStatus(data) {
            if (data.playlist_mode) {
                document.getElementById('playlist-mode').textContent = data.playlist_mode;
            }
            if (data.qa_strategy) {
                document.getElementById('qa-strategy').textContent = data.qa_strategy;
            }
        }
        
        function updateClientList(clients) {
            const listEl = document.getElementById('client-list');
            listEl.innerHTML = '';
            
            if (!clients || clients.length === 0) {
                listEl.innerHTML = '<div class="v2-client-item"><span class="v2-client-id">暂无客户端连接</span></div>';
                return;
            }
            
            clients.forEach(client => {
                const item = document.createElement('div');
                item.className = 'v2-client-item';
                
                const statusClass = client.buffer_health === 'HEALTHY' ? 'v2-status-healthy' :
                                  client.buffer_health === 'AT_RISK' ? 'v2-status-at-risk' :
                                  'v2-status-depleted';
                
                item.innerHTML = `
                    <span class="v2-client-id">${client.id}</span>
                    <span class="v2-client-status ${statusClass}">${client.buffer_health}</span>
                `;
                
                listEl.appendChild(item);
            });
            
            document.getElementById('active-clients').textContent = clients.length;
        }
        
        function updateMetrics(data) {
            if (data.cache_hits !== undefined) {
                document.getElementById('cache-hits').textContent = data.cache_hits;
            }
            if (data.synthesis_queue !== undefined) {
                document.getElementById('synthesis-queue').textContent = data.synthesis_queue;
            }
        }
        
        function startStream() {
            // 防止重复点击
            if (v2DashboardState.isStarting) {
                console.log('Stream start already in progress');
                return;
            }
            
            // 如果已有连接，先清理
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                console.log('Closing existing WebSocket connection');
                v2DashboardState.ws.close();
                v2DashboardState.ws = null;
                v2DashboardState.isConnected = false;
            }
            
            v2DashboardState.isStarting = true;
            setButtonState('startStream', false);
            updateStatusFeedback('正在启动直播...', 'loading');
            
            fetch('/api/control/start-stream', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    mode: 'smart',
                    topic: 'AI直播测试'
                })
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Stream started:', data);
                v2DashboardState.sessionId = data.session_id;  // 保存 session_id
                console.log('Session ID saved:', v2DashboardState.sessionId);
                updateStatusFeedback('直播已启动，正在连接...', 'success');
                connectWebSocket();  // 自动连接 WebSocket
                v2DashboardState.isStarting = false;
                setButtonState('startStream', true);
            })
            .catch(error => {
                console.error('Error:', error);
                updateStatusFeedback(`启动失败: ${error.message}`, 'error');
                v2DashboardState.isStarting = false;
                setButtonState('startStream', true);
            });
        }
        
        function pauseStream() {
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                v2DashboardState.ws.send(JSON.stringify({type: 'pause'}));
                updateStatusFeedback('直播已暂停', 'info');
            } else {
                updateStatusFeedback('WebSocket 未连接', 'warning');
            }
        }
        
        function resumeStream() {
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                v2DashboardState.ws.send(JSON.stringify({type: 'resume'}));
                updateStatusFeedback('直播已恢复', 'success');
            } else {
                updateStatusFeedback('WebSocket 未连接', 'warning');
            }
        }
        
        function stopStream() {
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                v2DashboardState.ws.send(JSON.stringify({type: 'stop'}));
                updateStatusFeedback('正在停止直播...', 'loading');
                setTimeout(() => {
                    v2DashboardState.ws.close(1000, 'User stopped stream');
                    
                    // 使用 SessionManager 清理状态
                    if (window.sessionManager) {
                        window.sessionManager.endSession('normal');
                    } else {
                        // 降级处理：手动清理
                        v2DashboardState.sessionId = null;
                        sessionStorage.removeItem('currentSessionId');
                    }
                    
                    updateStatusFeedback('直播已停止', 'info');
                }, 500);
            } else {
                updateStatusFeedback('WebSocket 未连接', 'warning');
            }
        }
        
        function insertQA() {
            const question = document.getElementById('qa-input').value;
            if (!question) return;
            
            fetch('/api/control/submit-question', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    text: question,
                    priority: 'HIGH'
                })
            }).then(response => response.json())
              .then(data => {
                  console.log('QA inserted:', data);
                  document.getElementById('qa-input').value = '';
              })
              .catch(error => console.error('Error:', error));
        }
        
        function refreshClients() {
            if (v2DashboardState.ws && v2DashboardState.ws.readyState === WebSocket.OPEN) {
                v2DashboardState.ws.send(JSON.stringify({type: 'get_clients'}));
            } else {
                updateStatusFeedback('请先连接 WebSocket', 'warning');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            // 初始化按钮状态
            setButtonState('startStream', true);
            setButtonState('connectWebSocket', false);
            setButtonState('pauseStream', false);
            setButtonState('resumeStream', false);
            setButtonState('stopStream', false);
            
            // 显示初始状态
            updateStatusFeedback('准备就绪，请点击"开始直播"', 'info');
        });
    </script>
</body>
</html>