<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情 - AI直播运营控制台</title>
    
    <!-- Global Design System CSS -->
    <link rel="stylesheet" href="./static/css/global-design-system.css?v=1.0.0">
    
    <!-- UI Components Library -->
    <script src="./static/js/ui-components.js?v=1.0.0"></script>
    
    <style>
        /* Page Specific Styles */
        .page-header {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-lg) 0;
            margin-bottom: var(--spacing-xl);
        }
        
        .page-header .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-lg) var(--spacing-xl);
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: var(--spacing-xl);
        }
        
        .content-column {
            min-width: 0; /* 防止内容溢出 */
        }
        
        .sidebar-column {
            position: sticky;
            top: var(--spacing-lg);
            align-self: start;
        }
        
        /* 商品基本信息卡片 */
        .product-info-card {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }
        
        .product-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-lg);
        }
        
        .product-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-sm) 0;
        }
        
        .product-sku {
            font-size: 0.875rem;
            background: var(--bg-secondary);
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            color: var(--text-secondary);
            font-family: monospace;
        }
        
        .product-status {
            padding: 4px 12px;
            border-radius: var(--radius-sm);
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-active {
            background: var(--success-bg);
            color: var(--success-color);
        }
        
        .status-inactive {
            background: var(--warning-bg);
            color: var(--warning-color);
        }
        
        .product-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .stat-card {
            background: var(--bg-secondary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-md);
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
            margin-bottom: var(--spacing-xs);
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .product-description {
            background: var(--bg-secondary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-md);
            line-height: 1.6;
            color: var(--text-primary);
        }
        
        .product-meta {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
            padding: var(--spacing-lg);
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        /* QA管理区域 */
        .qa-section {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .qa-toolbar {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }
        
        .qa-search {
            flex: 1;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .qa-list {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .qa-item {
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            border: 1px solid var(--border-color);
        }
        
        .qa-question {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            line-height: 1.4;
        }
        
        .qa-answer {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--spacing-md);
        }
        
        .qa-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: var(--text-secondary);
        }
        
        .qa-actions {
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .qa-empty {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--text-secondary);
        }
        
        .qa-empty-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
            opacity: 0.5;
        }
        
        /* 侧边栏样式 */
        .sidebar-card {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-lg);
        }
        
        .sidebar-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-md) 0;
        }
        
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        /* 表单模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-overlay);
            z-index: var(--z-modal-backdrop);
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn var(--transition-base);
        }
        
        .modal-content {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: var(--shadow-xl);
            animation: slideUp var(--transition-base);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
        }
        
        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-secondary);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: all var(--transition-fast);
        }
        
        .modal-close:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }
        
        .form-group {
            margin-bottom: var(--spacing-lg);
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            font-size: 0.875rem;
        }
        
        .form-input, .form-textarea, .form-select {
            width: 100%;
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: border-color var(--transition-fast);
        }
        
        .form-input:focus, .form-textarea:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .modal-actions {
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-color);
        }
        
        @media (max-width: 1024px) {
            .main-container {
                grid-template-columns: 1fr;
                gap: var(--spacing-lg);
            }
            
            .sidebar-column {
                position: static;
                order: -1;
            }
            
            .sidebar-card {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: var(--spacing-md);
            }
        }
        
        @media (max-width: 768px) {
            .product-stats {
                grid-template-columns: 1fr 1fr;
            }
            
            .product-meta {
                grid-template-columns: 1fr;
            }
            
            .sidebar-card {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: var(--spacing-sm);
            }
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div>
                <div class="breadcrumb">
                    <a href="./">首页</a>
                    <span>></span>
                    <a href="./products">商品管理</a>
                    <span>></span>
                    <span id="breadcrumbProductName">商品详情</span>
                </div>
                <h1 class="page-title">📦 <span id="pageProductName">商品详情</span></h1>
            </div>
            <div>
                <a href="./products" class="btn btn-secondary">返回列表</a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-container">
        <!-- 主内容区域 -->
        <div class="content-column">
            <!-- 商品基本信息 -->
            <div class="product-info-card">
                <div class="product-header-info">
                    <div>
                        <h2 class="product-name" id="productName">加载中...</h2>
                        <div class="product-sku" id="productSku">-</div>
                    </div>
                    <div class="product-status" id="productStatus">-</div>
                </div>
                
                <div class="product-stats">
                    <div class="stat-card">
                        <span class="stat-value" id="productPrice">¥0</span>
                        <span class="stat-label">商品价格</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-value" id="productStock">0</span>
                        <span class="stat-label">库存数量</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-value" id="productCategory">-</span>
                        <span class="stat-label">商品分类</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-value" id="qaCount">0</span>
                        <span class="stat-label">QA条目</span>
                    </div>
                </div>
                
                <div class="product-description" id="productDescription">
                    加载中...
                </div>
                
                <div class="product-meta">
                    <div>创建时间: <span id="productCreatedAt">-</span></div>
                    <div>更新时间: <span id="productUpdatedAt">-</span></div>
                </div>
            </div>
            
            <!-- QA管理区域 -->
            <div class="qa-section" id="qa-section">
                <div class="section-header">
                    <h3 class="section-title">📝 QA管理</h3>
                    <button class="btn btn-primary btn-sm" onclick="openCreateQAModal()">
                        + 添加QA
                    </button>
                </div>
                
                <div class="qa-toolbar">
                    <input type="text" class="qa-search" placeholder="搜索问题或答案..." 
                           id="qaSearchInput" onkeyup="handleQASearch()">
                    <button class="btn btn-secondary btn-sm" onclick="loadProductQA()">
                        🔄 刷新
                    </button>
                </div>
                
                <div class="qa-list" id="qaList">
                    <div class="qa-empty">
                        <div class="qa-empty-icon">⏳</div>
                        <div>加载中...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 侧边栏 -->
        <div class="sidebar-column">
            <!-- 快速操作 -->
            <div class="sidebar-card">
                <h4 class="sidebar-title">⚡ 快速操作</h4>
                <div class="action-buttons">
                    <button class="btn btn-primary btn-sm" onclick="openEditProductModal()">
                        ✏️ 编辑商品
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="duplicateProduct()">
                        📋 复制商品
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="exportProductData()">
                        📤 导出数据
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="deleteProduct()">
                        🗑️ 删除商品
                    </button>
                </div>
            </div>
            
            <!-- QA统计 -->
            <div class="sidebar-card">
                <h4 class="sidebar-title">📊 QA统计</h4>
                <div class="product-stats">
                    <div class="stat-card">
                        <span class="stat-value" id="qaTotal">0</span>
                        <span class="stat-label">总QA数</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-value" id="qaPopular">0</span>
                        <span class="stat-label">热门QA</span>
                    </div>
                </div>
            </div>
            
            <!-- 最近活动 -->
            <div class="sidebar-card">
                <h4 class="sidebar-title">🕒 最近活动</h4>
                <div id="recentActivity" style="font-size: 0.875rem; color: var(--text-secondary);">
                    暂无活动记录
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑商品模态框 -->
    <div id="editProductModal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">编辑商品</h2>
                <button class="modal-close" onclick="closeEditProductModal()">×</button>
            </div>
            
            <form id="editProductForm" onsubmit="handleUpdateProduct(event)">
                <div class="form-group">
                    <label class="form-label" for="editProductName">商品名称 *</label>
                    <input type="text" id="editProductName" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="editProductCategory">商品分类 *</label>
                    <select id="editProductCategory" class="form-select" required>
                        <option value="electronics">电子产品</option>
                        <option value="clothing">服装</option>
                        <option value="home">家居用品</option>
                        <option value="beauty">美妆护肤</option>
                        <option value="food">食品饮料</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="editProductPrice">商品价格</label>
                    <input type="number" id="editProductPrice" class="form-input" min="0" step="0.01">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="editProductStock">库存数量</label>
                    <input type="number" id="editProductStock" class="form-input" min="0" step="1">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="editProductDescription">商品描述</label>
                    <textarea id="editProductDescription" class="form-textarea"></textarea>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeEditProductModal()">
                        取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        保存更改
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 编辑QA模态框 -->
    <div id="editQAModal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">编辑QA</h2>
                <button class="modal-close" onclick="closeEditQAModal()">×</button>
            </div>
            
            <form id="editQAForm" onsubmit="handleUpdateQA(event)">
                <div class="form-group">
                    <label class="form-label" for="editQaQuestion">问题 *</label>
                    <textarea id="editQaQuestion" class="form-textarea" required></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="editQaAnswer">答案 *</label>
                    <textarea id="editQaAnswer" class="form-textarea" required></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="editQaCategory">分类</label>
                    <select id="editQaCategory" class="form-select">
                        <option value="">不分类</option>
                        <option value="product_info">商品信息</option>
                        <option value="usage">使用方法</option>
                        <option value="purchase">购买相关</option>
                        <option value="service">售后服务</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="editQaConfidence">置信度</label>
                    <input type="range" id="editQaConfidence" min="0" max="1" step="0.1" value="0.8">
                    <div style="font-size: 0.875rem; color: var(--text-secondary); margin-top: var(--spacing-xs);">
                        当前值: <span id="editConfidenceValue">0.8</span>
                    </div>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeEditQAModal()">
                        取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        保存更改
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 添加QA模态框 -->
    <div id="createQAModal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">添加QA</h2>
                <button class="modal-close" onclick="closeCreateQAModal()">×</button>
            </div>
            
            <form id="createQAForm" onsubmit="handleCreateQA(event)">
                <div class="form-group">
                    <label class="form-label" for="qaQuestion">问题 *</label>
                    <textarea id="qaQuestion" class="form-textarea" required 
                              placeholder="输入客户可能提出的问题..."></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="qaAnswer">答案 *</label>
                    <textarea id="qaAnswer" class="form-textarea" required
                              placeholder="输入对应的答案..."></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="qaCategory">分类</label>
                    <select id="qaCategory" class="form-select">
                        <option value="">不分类</option>
                        <option value="product_info">商品信息</option>
                        <option value="usage">使用方法</option>
                        <option value="purchase">购买相关</option>
                        <option value="service">售后服务</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="qaConfidence">置信度</label>
                    <input type="range" id="qaConfidence" min="0" max="1" step="0.1" value="0.8">
                    <div style="font-size: 0.875rem; color: var(--text-secondary); margin-top: var(--spacing-xs);">
                        当前值: <span id="confidenceValue">0.8</span>
                    </div>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeCreateQAModal()">
                        取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        添加QA
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局状态
        let currentProductId = null;
        let currentProduct = null;
        let currentQAList = [];
        let filteredQAList = [];
        let qaSearchTimeout = null;
        let currentEditingQAId = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const productId = urlParams.get('id');
            
            if (productId) {
                currentProductId = parseInt(productId);
                loadProductDetail();
                loadProductQA();
            } else {
                showError('缺少商品ID参数');
            }
            
            // 置信度滑块事件
            document.getElementById('qaConfidence').addEventListener('input', function(e) {
                document.getElementById('confidenceValue').textContent = e.target.value;
            });
            
            document.getElementById('editQaConfidence').addEventListener('input', function(e) {
                document.getElementById('editConfidenceValue').textContent = e.target.value;
            });
        });

        // 加载商品详情
        async function loadProductDetail() {
            try {
                const response = await fetch(`/api/v1/simple-products/${currentProductId}`);
                if (!response.ok) throw new Error('获取商品详情失败');
                
                currentProduct = await response.json();
                renderProductDetail();
                
            } catch (error) {
                console.error('Failed to load product:', error);
                showError(error.message);
            }
        }

        // 渲染商品详情
        function renderProductDetail() {
            if (!currentProduct) return;
            
            // 更新页面标题和面包屑
            document.getElementById('pageProductName').textContent = currentProduct.name;
            document.getElementById('breadcrumbProductName').textContent = currentProduct.name;
            document.title = `${currentProduct.name} - 商品详情`;
            
            // 基本信息
            document.getElementById('productName').textContent = currentProduct.name;
            document.getElementById('productSku').textContent = currentProduct.sku;
            
            // 状态
            const status = currentProduct.stock > 0 ? 'active' : 'inactive';
            const statusText = currentProduct.stock > 0 ? '✅ 有库存' : '⚠️ 缺货';
            const statusClass = `status-${status}`;
            const statusElement = document.getElementById('productStatus');
            statusElement.textContent = statusText;
            statusElement.className = `product-status ${statusClass}`;
            
            // 统计数据
            document.getElementById('productPrice').textContent = `¥${currentProduct.price || 0}`;
            document.getElementById('productStock').textContent = currentProduct.stock || 0;
            document.getElementById('productCategory').textContent = getCategoryText(currentProduct.category);
            
            // 描述
            document.getElementById('productDescription').textContent = 
                currentProduct.description || '暂无商品描述';
            
            // 时间信息
            document.getElementById('productCreatedAt').textContent = 
                new Date(currentProduct.created_at).toLocaleString();
            document.getElementById('productUpdatedAt').textContent = 
                new Date(currentProduct.updated_at).toLocaleString();
        }

        // 加载产品QA
        async function loadProductQA() {
            try {
                const response = await fetch(`/api/v1/simple-products/${currentProductId}/qa`);
                if (!response.ok) throw new Error('获取QA列表失败');
                
                currentQAList = await response.json();
                filteredQAList = [...currentQAList];
                renderQAList();
                updateQAStats();
                
            } catch (error) {
                console.error('Failed to load QA:', error);
                showQAError(error.message);
            }
        }

        // 渲染QA列表
        function renderQAList() {
            const qaList = document.getElementById('qaList');
            
            if (filteredQAList.length === 0) {
                qaList.innerHTML = `
                    <div class="qa-empty">
                        <div class="qa-empty-icon">💬</div>
                        <div>暂无QA记录</div>
                        <button class="btn btn-primary btn-sm" onclick="openCreateQAModal()" 
                                style="margin-top: var(--spacing-md);">
                            + 添加第一个QA
                        </button>
                    </div>
                `;
                return;
            }
            
            let html = '';
            filteredQAList.forEach(qa => {
                html += `
                    <div class="qa-item">
                        <div class="qa-question">
                            Q: ${escapeHtml(qa.question)}
                        </div>
                        <div class="qa-answer">
                            A: ${escapeHtml(qa.answer)}
                        </div>
                        <div class="qa-meta">
                            <div>
                                ${qa.category ? `分类: ${qa.category}` : '无分类'} | 
                                置信度: ${qa.confidence_score} | 
                                命中次数: ${qa.hit_count || 0}
                            </div>
                            <div class="qa-actions">
                                <button class="btn btn-sm btn-secondary" onclick="editQA(${qa.id})">
                                    编辑
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteQA(${qa.id})">
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            qaList.innerHTML = html;
        }

        // 更新QA统计
        function updateQAStats() {
            document.getElementById('qaCount').textContent = currentQAList.length;
            document.getElementById('qaTotal').textContent = currentQAList.length;
            
            // 热门QA (命中次数>0的)
            const popularQA = currentQAList.filter(qa => (qa.hit_count || 0) > 0).length;
            document.getElementById('qaPopular').textContent = popularQA;
        }

        // QA搜索
        function handleQASearch() {
            clearTimeout(qaSearchTimeout);
            qaSearchTimeout = setTimeout(() => {
                const query = document.getElementById('qaSearchInput').value.toLowerCase().trim();
                
                if (!query) {
                    filteredQAList = [...currentQAList];
                } else {
                    filteredQAList = currentQAList.filter(qa => 
                        qa.question.toLowerCase().includes(query) ||
                        qa.answer.toLowerCase().includes(query)
                    );
                }
                
                renderQAList();
            }, 300);
        }

        // 打开编辑商品模态框
        function openEditProductModal() {
            if (!currentProduct) return;
            
            document.getElementById('editProductName').value = currentProduct.name;
            document.getElementById('editProductCategory').value = currentProduct.category;
            document.getElementById('editProductPrice').value = currentProduct.price || '';
            document.getElementById('editProductStock').value = currentProduct.stock || '';
            document.getElementById('editProductDescription').value = currentProduct.description || '';
            
            document.getElementById('editProductModal').style.display = 'flex';
        }

        // 关闭编辑商品模态框
        function closeEditProductModal() {
            document.getElementById('editProductModal').style.display = 'none';
        }

        // 处理更新商品
        async function handleUpdateProduct(event) {
            event.preventDefault();
            
            const formData = {
                name: document.getElementById('editProductName').value.trim(),
                category: document.getElementById('editProductCategory').value,
                price: parseFloat(document.getElementById('editProductPrice').value) || 0,
                stock: parseInt(document.getElementById('editProductStock').value) || 0,
                description: document.getElementById('editProductDescription').value.trim()
            };
            
            try {
                UI.Loading.show('保存中...');
                
                const response = await fetch(`/api/v1/simple-products/${currentProductId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || '更新失败');
                }
                
                UI.Loading.hide();
                UI.Toast.success('商品更新成功');
                closeEditProductModal();
                loadProductDetail(); // 重新加载数据
                
            } catch (error) {
                UI.Loading.hide();
                UI.Toast.error('更新失败: ' + error.message);
            }
        }

        // 打开创建QA模态框
        function openCreateQAModal() {
            document.getElementById('createQAForm').reset();
            document.getElementById('confidenceValue').textContent = '0.8';
            document.getElementById('createQAModal').style.display = 'flex';
        }

        // 关闭创建QA模态框
        function closeCreateQAModal() {
            document.getElementById('createQAModal').style.display = 'none';
        }

        // 处理创建QA
        async function handleCreateQA(event) {
            event.preventDefault();
            
            const formData = {
                question: document.getElementById('qaQuestion').value.trim(),
                answer: document.getElementById('qaAnswer').value.trim(),
                category: document.getElementById('qaCategory').value,
                confidence_score: parseFloat(document.getElementById('qaConfidence').value)
            };
            
            try {
                UI.Loading.show('添加中...');
                
                const response = await fetch(`/api/v1/simple-products/${currentProductId}/qa`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || '添加失败');
                }
                
                UI.Loading.hide();
                UI.Toast.success('QA添加成功');
                closeCreateQAModal();
                loadProductQA(); // 重新加载QA列表
                
            } catch (error) {
                UI.Loading.hide();
                UI.Toast.error('添加失败: ' + error.message);
            }
        }

        // 复制商品
        async function duplicateProduct() {
            if (!currentProduct) return;
            
            UI.Modal.confirm(
                `确定要复制商品 "${currentProduct.name}" 吗？`,
                async () => {
                    try {
                        UI.Loading.show('复制中...');
                        
                        const newSku = currentProduct.sku + '-COPY-' + Date.now();
                        const formData = {
                            sku: newSku,
                            name: currentProduct.name + ' (副本)',
                            category: currentProduct.category,
                            price: currentProduct.price || 0,
                            stock: currentProduct.stock || 0,
                            description: currentProduct.description || ''
                        };
                        
                        const response = await fetch('/api/v1/simple-products/', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(formData)
                        });
                        
                        if (!response.ok) {
                            const error = await response.json();
                            throw new Error(error.detail || '复制失败');
                        }
                        
                        const newProduct = await response.json();
                        
                        UI.Loading.hide();
                        UI.Toast.success('商品复制成功');
                        
                        // 跳转到新商品详情页
                        setTimeout(() => {
                            window.location.href = `./product_detail.html?id=${newProduct.id}`;
                        }, 1000);
                        
                    } catch (error) {
                        UI.Loading.hide();
                        UI.Toast.error('复制失败: ' + error.message);
                    }
                }
            );
        }

        // 导出商品数据
        function exportProductData() {
            if (!currentProduct) return;
            
            const data = {
                product: currentProduct,
                qa_list: currentQAList
            };
            
            const jsonStr = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `product_${currentProduct.sku}_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
            UI.Toast.success('数据导出成功');
        }

        // 删除商品
        function deleteProduct() {
            if (!currentProduct) return;
            
            UI.Modal.confirm(
                `确定要删除商品 "${currentProduct.name}" 吗？此操作不可撤销。`,
                async () => {
                    try {
                        UI.Loading.show('删除中...');
                        
                        const response = await fetch(`/api/v1/simple-products/${currentProductId}`, {
                            method: 'DELETE'
                        });
                        
                        if (!response.ok) {
                            throw new Error('删除失败');
                        }
                        
                        UI.Loading.hide();
                        UI.Toast.success('商品已删除');
                        
                        // 跳转回商品列表
                        setTimeout(() => {
                            window.location.href = './products';
                        }, 1000);
                        
                    } catch (error) {
                        UI.Loading.hide();
                        UI.Toast.error('删除失败: ' + error.message);
                    }
                }
            );
        }

        // 打开编辑QA模态框
        function editQA(qaId) {
            const qa = currentQAList.find(q => q.id === qaId);
            if (!qa) return;
            
            currentEditingQAId = qaId;
            
            document.getElementById('editQaQuestion').value = qa.question;
            document.getElementById('editQaAnswer').value = qa.answer;
            document.getElementById('editQaCategory').value = qa.category || '';
            document.getElementById('editQaConfidence').value = qa.confidence_score;
            document.getElementById('editConfidenceValue').textContent = qa.confidence_score;
            
            document.getElementById('editQAModal').style.display = 'flex';
        }

        // 关闭编辑QA模态框
        function closeEditQAModal() {
            document.getElementById('editQAModal').style.display = 'none';
            currentEditingQAId = null;
        }

        // 处理更新QA
        async function handleUpdateQA(event) {
            event.preventDefault();
            
            if (!currentEditingQAId) return;
            
            const formData = {
                question: document.getElementById('editQaQuestion').value.trim(),
                answer: document.getElementById('editQaAnswer').value.trim(),
                category: document.getElementById('editQaCategory').value,
                confidence_score: parseFloat(document.getElementById('editQaConfidence').value)
            };
            
            try {
                UI.Loading.show('更新中...');
                
                const response = await fetch(`/api/v1/simple-products/${currentProductId}/qa/${currentEditingQAId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || '更新失败');
                }
                
                UI.Loading.hide();
                UI.Toast.success('QA更新成功');
                closeEditQAModal();
                loadProductQA(); // 重新加载QA列表
                
            } catch (error) {
                UI.Loading.hide();
                UI.Toast.error('更新失败: ' + error.message);
            }
        }

        // 删除QA
        function deleteQA(qaId) {
            const qa = currentQAList.find(q => q.id === qaId);
            if (!qa) return;
            
            UI.Modal.confirm(
                `确定要删除这个QA吗？\n\n问题: ${qa.question.substring(0, 50)}${qa.question.length > 50 ? '...' : ''}`,
                async () => {
                    try {
                        UI.Loading.show('删除中...');
                        
                        const response = await fetch(`/api/v1/simple-products/${currentProductId}/qa/${qaId}`, {
                            method: 'DELETE'
                        });
                        
                        if (!response.ok) {
                            throw new Error('删除失败');
                        }
                        
                        UI.Loading.hide();
                        UI.Toast.success('QA已删除');
                        loadProductQA(); // 重新加载QA列表
                        
                    } catch (error) {
                        UI.Loading.hide();
                        UI.Toast.error('删除失败: ' + error.message);
                    }
                }
            );
        }

        // 工具函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function getCategoryText(category) {
            const categories = {
                'electronics': '电子产品',
                'clothing': '服装',
                'home': '家居用品',
                'beauty': '美妆护肤',
                'food': '食品饮料',
                'other': '其他'
            };
            return categories[category] || category;
        }
        
        function showError(message) {
            document.querySelector('.content-column').innerHTML = `
                <div style="text-align: center; padding: var(--spacing-xl);">
                    <h2>⚠️ 加载失败</h2>
                    <p style="color: var(--text-secondary);">${message}</p>
                    <button class="btn btn-primary" onclick="window.location.reload()">
                        重新加载
                    </button>
                </div>
            `;
        }
        
        function showQAError(message) {
            document.getElementById('qaList').innerHTML = `
                <div class="qa-empty">
                    <div class="qa-empty-icon">⚠️</div>
                    <div>加载失败: ${message}</div>
                    <button class="btn btn-primary btn-sm" onclick="loadProductQA()" 
                            style="margin-top: var(--spacing-md);">
                        重试
                    </button>
                </div>
            `;
        }

        // 关闭模态框事件
        document.getElementById('editProductModal').addEventListener('click', function(e) {
            if (e.target === this) closeEditProductModal();
        });
        
        document.getElementById('createQAModal').addEventListener('click', function(e) {
            if (e.target === this) closeCreateQAModal();
        });
        
        document.getElementById('editQAModal').addEventListener('click', function(e) {
            if (e.target === this) closeEditQAModal();
        });

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeEditProductModal();
                closeCreateQAModal();
                closeEditQAModal();
            }
        });
    </script>
</body>
</html>