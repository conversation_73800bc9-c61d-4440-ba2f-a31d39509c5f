<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI直播运营配置系统</title>
    
    <!-- Global Design System CSS -->
    <link rel="stylesheet" href="./static/css/global-design-system.css?v=1.0.0">
    
    <!-- UI Components Library -->
    <script src="./static/js/ui-components.js?v=1.0.0"></script>
    
    <style>
        /* Page Specific Styles - 保留原有功能样式 */
        .form-section {
            margin-bottom: var(--spacing-xl);
            animation: fadeInUp 0.6s ease;
            animation-fill-mode: both;
        }
        
        .form-section.hidden {
            display: none;
        }
        
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .required {
            color: var(--error-color);
        }
        
        /* Product Selector Styles */
        .product-selector {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-primary);
        }
        
        .product-search-box {
            display: flex;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
        }
        
        .product-search-box .form-control {
            flex: 1;
        }
        
        .product-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .product-loading {
            padding: var(--spacing-xl);
            text-align: center;
            color: var(--text-secondary);
        }
        
        .product-item {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color var(--transition-fast);
        }
        
        .product-item:hover {
            background: var(--bg-secondary);
        }
        
        .product-item.selected {
            background: var(--primary-50);
            border-color: var(--primary-color);
        }
        
        .product-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-xs);
        }
        
        .product-item-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .product-item-sku {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-family: monospace;
            background: var(--bg-secondary);
            padding: 2px 6px;
            border-radius: var(--radius-sm);
        }
        
        .product-item-price {
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .product-item-meta {
            font-size: 0.875rem;
            color: var(--text-secondary);
            display: flex;
            gap: var(--spacing-md);
        }
        
        .product-empty {
            padding: var(--spacing-xl);
            text-align: center;
            color: var(--text-secondary);
        }
        
        /* Selected Product Preview */
        .selected-product-preview {
            margin-top: var(--spacing-lg);
            padding: var(--spacing-lg);
            border: 2px solid var(--primary-color);
            border-radius: var(--radius-md);
            background: var(--primary-50);
        }
        
        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }
        
        .preview-header h4 {
            margin: 0;
            color: var(--primary-color);
            font-size: 1rem;
        }
        
        .preview-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-md);
        }
        
        .preview-basic-info {
            flex: 1;
        }
        
        .preview-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .preview-sku {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-family: monospace;
            margin-bottom: var(--spacing-xs);
        }
        
        .preview-category {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .preview-price-info {
            text-align: right;
        }
        
        .preview-price {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }
        
        .preview-stock {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .preview-description {
            padding: var(--spacing-md);
            background: var(--bg-primary);
            border-radius: var(--radius-sm);
            font-size: 0.875rem;
            color: var(--text-secondary);
            line-height: 1.6;
        }
        
        /* Price Configuration */
        .price-config {
            display: flex;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
        }
        
        .form-check {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .form-check input[type="radio"] {
            margin: 0;
        }
        
        .custom-price-section {
            margin-top: var(--spacing-md);
            padding: var(--spacing-md);
            border: 1px dashed var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-secondary);
        }
        
        .form-help {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-top: var(--spacing-xs);
            line-height: 1.4;
        }
        
        /* Progress Indicator */
        .progress-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--spacing-xl);
            position: relative;
        }
        
        .progress-indicator::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gray-200);
            z-index: 0;
        }
        
        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 1;
            flex: 1;
            cursor: pointer;
        }
        
        .progress-step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: var(--gray-500);
            margin-bottom: var(--spacing-sm);
            transition: all var(--transition-base);
        }
        
        .progress-step.active .progress-step-number {
            background: var(--primary-gradient);
            color: var(--text-inverse);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .progress-step.completed .progress-step-number {
            background: var(--success-color);
            color: var(--text-inverse);
        }
        
        .progress-step-title {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-align: center;
        }
        
        /* Navigation Buttons */
        .nav-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: var(--spacing-xl);
            padding: var(--spacing-lg);
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-card);
        }
        
        /* Persona Selector */
        .persona-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }
        
        .persona-card {
            padding: var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-base);
            background: var(--bg-secondary);
        }
        
        .persona-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-card);
        }
        
        .persona-card.selected {
            border-color: var(--primary-color);
            background: var(--primary-bg);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .persona-icon {
            font-size: 2rem;
            margin-bottom: var(--spacing-sm);
        }
        
        .persona-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .persona-description {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        /* Selling Points */
        .selling-point-item {
            background: var(--bg-secondary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-md);
            border-left: 3px solid var(--primary-color);
            position: relative;
        }
        
        .selling-point-remove {
            position: absolute;
            top: var(--spacing-md);
            right: var(--spacing-md);
            background: var(--error-color);
            color: white;
            border: none;
            border-radius: var(--radius-sm);
            padding: 4px 8px;
            cursor: pointer;
            font-size: 0.75rem;
        }
        
        /* Validation Summary */
        .validation-summary {
            background: var(--bg-secondary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .validation-title {
            font-weight: 600;
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }
        
        .validation-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) 0;
        }
        
        .validation-icon {
            margin-right: var(--spacing-sm);
        }
        
        .validation-icon.success {
            color: var(--success-color);
        }
        
        .validation-icon.error {
            color: var(--error-color);
        }
        
        .validation-icon.warning {
            color: var(--warning-color);
        }
        
        /* Loading Animation */
        .loading {
            border: 3px solid var(--gray-200);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: var(--spacing-lg) auto;
        }
        
        /* Save Status */
        .save-status {
            text-align: center;
            margin-top: var(--spacing-md);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-full);
            font-size: 0.875rem;
            transition: all var(--transition-base);
        }
        
        .save-status.saving {
            background: var(--warning-bg);
            color: var(--warning-color);
        }
        
        .save-status.saved {
            background: var(--success-bg);
            color: var(--success-color);
        }
        
        .save-status.error {
            background: var(--error-bg);
            color: var(--error-color);
        }
        
        /* Character Counter */
        .character-counter {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-top: 4px;
        }
        
        .character-counter.warning {
            color: var(--warning-color);
        }
        
        .character-counter.error {
            color: var(--error-color);
        }
        
        /* Field Validation */
        .field-error {
            border-color: var(--error-color) !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
        }
        
        .field-error-message {
            color: var(--error-color);
            font-size: 0.875rem;
            margin-top: 4px;
        }
        
        .field-success {
            border-color: var(--success-color) !important;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
        }
        
        @media (max-width: 768px) {
            .progress-indicator {
                flex-direction: column;
                gap: var(--spacing-md);
            }
            
            .progress-indicator::before {
                display: none;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .persona-selector {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header with Navigation -->
    <div class="header">
        <div class="container">
            <div class="header-content" style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px; opacity: 0.9; font-size: 0.875rem;">
                        <a href="./" style="color: inherit; text-decoration: none;">首页</a>
                        <span>></span>
                        <span>运营配置</span>
                    </div>
                    <h1 style="margin: 0; font-size: 1.75rem;">
                        🎬 AI直播运营配置系统
                    </h1>
                    <div style="margin-top: 8px; opacity: 0.9;">
                        完整配置您的AI直播内容和策略
                    </div>
                </div>
                <div>
                    <a href="./" class="btn btn-secondary">返回控制台</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container" style="padding-top: 2rem; padding-bottom: 2rem;">
        
        <!-- Progress Header Card -->
        <div class="card" style="margin-bottom: var(--spacing-xl);">
            <div class="card-body">
                <h2 style="text-align: center; margin-bottom: var(--spacing-lg);">配置向导</h2>
                
                <!-- Progress Indicator -->
                <div class="progress-indicator">
                    <div class="progress-step active" onclick="goToSection(1)">
                        <div class="progress-step-number">1</div>
                        <div class="progress-step-title">基础信息</div>
                    </div>
                    <div class="progress-step" onclick="goToSection(2)">
                        <div class="progress-step-number">2</div>
                        <div class="progress-step-title">商品信息</div>
                    </div>
                    <div class="progress-step" onclick="goToSection(3)">
                        <div class="progress-step-number">3</div>
                        <div class="progress-step-title">卖点结构</div>
                    </div>
                    <div class="progress-step" onclick="goToSection(4)">
                        <div class="progress-step-number">4</div>
                        <div class="progress-step-title">人设配置</div>
                    </div>
                    <div class="progress-step" onclick="goToSection(5)">
                        <div class="progress-step-number">5</div>
                        <div class="progress-step-title">高级设置</div>
                    </div>
                    <div class="progress-step" onclick="goToSection(6)">
                        <div class="progress-step-number">6</div>
                        <div class="progress-step-title">审核提交</div>
                    </div>
                </div>
                
                <!-- Progress Bar -->
                <div class="progress" style="margin-top: var(--spacing-lg);">
                    <div class="progress-bar" id="progressBar" style="width: 16.67%;"></div>
                </div>
                <div class="progress-text" id="progressText">第 1 步，共 6 步</div>
                
                <!-- Save Status Indicator -->
                <div class="save-status" id="saveStatus" style="display: none;">
                    <span class="save-status-icon"></span>
                    <span class="save-status-text"></span>
                    <span class="save-timestamp"></span>
                </div>
            </div>
        </div>

        <!-- Form Sections -->
        <form id="operationalForm">
            
            <!-- Section 1: Basic Information -->
            <div id="section1" class="info-card form-section">
                <h2 class="info-card-title">基础信息配置</h2>
                <p class="info-card-meta">设置直播的基本信息，包括标题、类型、时长等核心参数</p>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="streamTitle">直播标题 <span class="required">*</span></label>
                        <input type="text" class="form-control" id="streamTitle" name="stream_title" placeholder="请输入吸引人的直播标题">
                        <div style="font-size: 0.875rem; color: var(--text-muted); margin-top: 4px;">标题将显示给观众，建议包含产品关键词</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="streamType">直播类型 <span class="required">*</span></label>
                        <select class="form-control form-select" id="streamType" name="stream_type">
                            <option value="">请选择直播类型</option>
                            <option value="product_launch">新品发布</option>
                            <option value="daily_stream">日常直播</option>
                            <option value="flash_sale">限时特卖</option>
                            <option value="special_event">特殊活动</option>
                            <option value="q_and_a">问答互动</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="plannedDuration">计划时长（分钟） <span class="required">*</span></label>
                        <input type="number" class="form-control" id="plannedDuration" name="planned_duration" min="30" max="180" value="90">
                        <div style="font-size: 0.875rem; color: var(--text-muted); margin-top: 4px;">建议60-120分钟，系统会自动调节内容节奏</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="targetTimezone">目标时区</label>
                        <select class="form-control form-select" id="targetTimezone" name="target_timezone">
                            <option value="Asia/Tokyo">Asia/Tokyo (东京时间)</option>
                            <option value="Asia/Shanghai">Asia/Shanghai (北京时间)</option>
                            <option value="Asia/Seoul">Asia/Seoul (首尔时间)</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <input type="checkbox" id="enableAutoInteraction" name="enable_auto_interaction" checked style="margin-right: 8px;">
                        启用自动互动（推荐）
                    </label>
                    <div style="font-size: 0.875rem; color: var(--text-muted); margin-top: 4px;">AI会自动回应观众问题和进行主动互动</div>
                </div>
            </div>
            
            <!-- Section 2: Product Information -->
            <div id="section2" class="info-card form-section hidden">
                <h2 class="info-card-title">商品信息配置</h2>
                <p class="info-card-meta">从商品管理系统中选择主推商品，系统将自动填充商品信息</p>
                
                <!-- 商品选择器 -->
                <div class="form-group">
                    <label class="form-label">选择主推商品 <span class="required">*</span></label>
                    <div class="product-selector">
                        <div class="product-search-box">
                            <input type="text" class="form-control" id="productSearchInput" 
                                   placeholder="搜索商品名称或SKU..." 
                                   onkeyup="handleProductSearch()">
                            <button type="button" class="btn btn-secondary" onclick="refreshProducts()">
                                🔄 刷新
                            </button>
                            <button type="button" class="btn btn-primary" onclick="openProductManagement()">
                                🛍️ 管理商品
                            </button>
                        </div>
                        
                        <div id="productList" class="product-list">
                            <div class="product-loading">
                                <div class="loading"></div>
                                <div>正在加载商品列表...</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 选中商品预览 -->
                <div id="selectedProductPreview" class="selected-product-preview" style="display: none;">
                    <div class="preview-header">
                        <h4>已选择商品</h4>
                        <button type="button" class="btn btn-sm btn-secondary" onclick="clearProductSelection()">
                            清除选择
                        </button>
                    </div>
                    <div class="preview-content">
                        <div class="preview-basic-info">
                            <div class="preview-title" id="previewProductName">-</div>
                            <div class="preview-sku" id="previewProductSku">-</div>
                            <div class="preview-category" id="previewProductCategory">-</div>
                        </div>
                        <div class="preview-price-info">
                            <div class="preview-price" id="previewProductPrice">¥0</div>
                            <div class="preview-stock" id="previewProductStock">库存: 0</div>
                        </div>
                    </div>
                    <div class="preview-description" id="previewProductDescription">暂无描述</div>
                </div>
                
                <!-- 隐藏字段存储选中的商品ID -->
                <input type="hidden" id="selectedProductId" name="selected_product_id" value="">
                
                <!-- 价格覆盖选项 -->
                <div class="form-group">
                    <label class="form-label">价格配置</label>
                    <div class="price-config">
                        <div class="form-check">
                            <input type="radio" id="useOriginalPrice" name="price_config" value="original" checked
                                   onchange="handlePriceConfigChange()">
                            <label for="useOriginalPrice">使用商品原价</label>
                        </div>
                        <div class="form-check">
                            <input type="radio" id="useCustomPrice" name="price_config" value="custom"
                                   onchange="handlePriceConfigChange()">
                            <label for="useCustomPrice">自定义直播价格</label>
                        </div>
                    </div>
                    
                    <div id="customPriceSection" class="custom-price-section" style="display: none;">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" for="customCurrentPrice">直播售价 <span class="required">*</span></label>
                                <input type="number" class="form-control" id="customCurrentPrice" name="custom_current_price" 
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="customOriginalPrice">对比原价</label>
                                <input type="number" class="form-control" id="customOriginalPrice" name="custom_original_price" 
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 商品规格补充 -->
                <div class="form-group">
                    <label class="form-label" for="keySpecifications">补充规格信息</label>
                    <textarea class="form-control" id="keySpecifications" name="key_specifications" rows="4" 
                        placeholder="补充商品的关键规格信息，每行一个规格：&#10;颜色：红色&#10;尺寸：L码&#10;材质：纯棉"></textarea>
                    <div class="form-help">商品基础信息将从产品管理系统中自动获取，此处可补充直播特有的规格说明</div>
                </div>
            </div>
            
            <!-- Section 3: Selling Points Structure -->
            <div id="section3" class="info-card form-section hidden">
                <h2 class="info-card-title">卖点结构配置</h2>
                <p class="info-card-meta">构建商品的核心卖点体系，包括主价值主张、支撑事实和应对策略</p>
                
                <div class="form-group">
                    <label class="form-label" for="valueProposition">主价值主张 <span class="required">*</span></label>
                    <textarea class="form-control" id="valueProposition" name="primary_value_proposition" rows="3"
                        placeholder="用1-2句话概括商品的核心价值，如：高品质、低价格、独特功能等" 
                        oninput="updateCharacterCount('valueProposition', 'valuePropositionCount', 20, 300)"></textarea>
                    <div class="character-counter" id="valuePropositionCount">0 / 300 字符</div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="competitiveAdvantages">竞争优势</label>
                        <textarea class="form-control" id="competitiveAdvantages" rows="4"
                            placeholder="每行一个优势，如：&#10;比竞品A便宜20%&#10;独有专利技术&#10;更长的质保期"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="callToActions">行动号召 (CTA)</label>
                        <textarea class="form-control" id="callToActions" name="call_to_actions" rows="4"
                            placeholder="每行一个CTA，如：&#10;立即下单享优惠&#10;限时特价，数量有限&#10;点击下方链接购买"></textarea>
                    </div>
                </div>
                
                <!-- Selling Points Container -->
                <div class="form-group">
                    <label class="form-label">卖点列表</label>
                    <div id="sellingPointsContainer"></div>
                    <button type="button" class="btn btn-secondary" onclick="addSellingPoint()">
                        + 添加卖点
                    </button>
                </div>
            </div>
            
            <!-- Section 4: Persona Configuration -->
            <div id="section4" class="info-card form-section hidden">
                <h2 class="info-card-title">人设配置</h2>
                <p class="info-card-meta">选择AI主播的人设风格、语音特征和互动方式</p>
                
                <div class="form-group">
                    <label class="form-label">选择人设模板 <span class="required">*</span></label>
                    <div class="persona-selector" id="personaSelector">
                        <!-- Persona cards will be dynamically loaded here -->
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="customGreetings">自定义开场白</label>
                    <textarea class="form-control" id="customGreetings" rows="4"
                        placeholder="每行一个开场白，系统会随机选择使用&#10;如：大家好，欢迎来到我的直播间！&#10;今天要给大家介绍一款超棒的商品"></textarea>
                </div>
            </div>
            
            <!-- Section 5: Advanced Settings -->
            <div id="section5" class="info-card form-section hidden">
                <h2 class="info-card-title">高级设置</h2>
                <p class="info-card-meta">微调系统行为参数，优化直播效果（可选配置）</p>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="wordsPerMinute">语速覆盖（每分钟字数）</label>
                        <input type="number" class="form-control" id="wordsPerMinute" min="100" max="300" placeholder="默认：200">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="emotionalTone">情绪倾向</label>
                        <select class="form-control form-select" id="emotionalTone">
                            <option value="">使用默认设置</option>
                            <option value="enthusiastic">热情洋溢</option>
                            <option value="calm">沉稳专业</option>
                            <option value="friendly">亲切友好</option>
                            <option value="urgent">紧迫感强</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="interactionLevel">互动积极性</label>
                        <select class="form-control form-select" id="interactionLevel">
                            <option value="">使用默认设置</option>
                            <option value="high">高度互动</option>
                            <option value="medium">适度互动</option>
                            <option value="low">低度互动</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="repetitionInterval">重复间隔（分钟）</label>
                        <input type="number" class="form-control" id="repetitionInterval" min="5" max="60" placeholder="默认：15">
                    </div>
                </div>
            </div>
            
            <!-- Section 6: Review and Validation -->
            <div id="section6" class="info-card form-section hidden">
                <h2 class="info-card-title">审核与提交</h2>
                <p class="info-card-meta">检查配置完整性，验证合规性，准备提交配置</p>
                
                <div id="validationSummary" class="validation-summary">
                    <div class="validation-title">配置验证中...</div>
                    <div class="loading"></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="approvalNotes">审批备注</label>
                    <textarea class="form-control" id="approvalNotes" rows="3"
                        placeholder="记录审批过程中的重要信息或修改说明"></textarea>
                </div>
                
                <div style="text-align: center; margin-top: var(--spacing-xl);">
                    <button type="button" class="btn btn-success btn-lg" onclick="submitConfiguration()">
                        提交配置
                    </button>
                </div>
            </div>
        </form>

        <!-- Navigation Buttons -->
        <div class="nav-buttons">
            <button type="button" class="btn btn-secondary" id="prevBtn" onclick="changeSection(-1)" disabled>
                上一步
            </button>
            <button type="button" class="btn btn-primary" id="nextBtn" onclick="changeSection(1)">
                下一步
            </button>
        </div>
    </div>

    <script>
        let currentSection = 1;
        const totalSections = 6;
        let formData = {};
        let validationResults = {};
        let sellingPointsCount = 0;
        
        // Product selector state
        let availableProducts = [];
        let filteredProducts = [];
        let selectedProduct = null;
        let productSearchTimeout = null;
        
        // Initialize persona templates
        const personaTemplates = [
            {
                id: 'professional',
                icon: '👔',
                name: '专业顾问型',
                description: '知识渊博，讲解详细，适合高价值商品'
            },
            {
                id: 'enthusiastic',
                icon: '🎉',
                name: '热情推销型',
                description: '充满活力，善于调动气氛，适合大众商品'
            },
            {
                id: 'friendly',
                icon: '😊',
                name: '亲切朋友型',
                description: '像朋友般真诚推荐，适合生活用品'
            },
            {
                id: 'expert',
                icon: '🎓',
                name: '专家达人型',
                description: '行业专家形象，适合专业产品'
            }
        ];
        
        // Initialize on DOM ready
        document.addEventListener('DOMContentLoaded', function() {
            loadPersonaTemplates();
            initializeFormListeners();
            updateProgressIndicator();
        });
        
        // Load persona templates
        function loadPersonaTemplates() {
            const container = document.getElementById('personaSelector');
            container.innerHTML = personaTemplates.map(persona => `
                <div class="persona-card" data-persona-id="${persona.id}" onclick="selectPersona('${persona.id}')">
                    <div class="persona-icon">${persona.icon}</div>
                    <div class="persona-name">${persona.name}</div>
                    <div class="persona-description">${persona.description}</div>
                </div>
            `).join('');
        }
        
        // Select persona
        function selectPersona(personaId) {
            document.querySelectorAll('.persona-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-persona-id="${personaId}"]`).classList.add('selected');
            formData.selectedPersona = personaId;
        }
        
        // Add selling point
        function addSellingPoint() {
            sellingPointsCount++;
            const container = document.getElementById('sellingPointsContainer');
            const pointHtml = `
                <div class="selling-point-item" id="sellingPoint${sellingPointsCount}">
                    <button type="button" class="selling-point-remove" onclick="removeSellingPoint(${sellingPointsCount})">
                        删除
                    </button>
                    <div class="form-group">
                        <label class="form-label">卖点标题</label>
                        <input type="text" class="form-control" name="selling_point_title_${sellingPointsCount}" 
                            placeholder="如：超强续航">
                    </div>
                    <div class="form-group">
                        <label class="form-label">卖点描述</label>
                        <textarea class="form-control" name="selling_point_description_${sellingPointsCount}" 
                            rows="3" placeholder="详细说明这个卖点的内容和价值"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">支撑事实</label>
                        <textarea class="form-control" name="selling_point_facts_${sellingPointsCount}" 
                            rows="2" placeholder="列出支撑这个卖点的具体事实和数据"></textarea>
                    </div>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', pointHtml);
            UI.Toast.success('已添加新卖点');
        }
        
        // Remove selling point
        function removeSellingPoint(id) {
            document.getElementById(`sellingPoint${id}`).remove();
            UI.Toast.info('卖点已删除');
        }
        
        // Change section
        function changeSection(direction) {
            const newSection = currentSection + direction;
            if (newSection >= 1 && newSection <= totalSections) {
                // Validate current section before moving
                if (direction > 0 && !validateCurrentSection()) {
                    UI.Toast.error('请完成必填项后继续');
                    return;
                }
                
                // Hide current section
                document.getElementById(`section${currentSection}`).classList.add('hidden');
                
                // Show new section
                currentSection = newSection;
                document.getElementById(`section${currentSection}`).classList.remove('hidden');
                
                // Update UI
                updateProgressIndicator();
                updateNavigationButtons();
                
                // Special handling for validation section
                if (currentSection === 6) {
                    performValidation();
                }
                
                // Scroll to top
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }
        
        // Go to specific section
        function goToSection(section) {
            if (section >= 1 && section <= totalSections) {
                document.getElementById(`section${currentSection}`).classList.add('hidden');
                currentSection = section;
                document.getElementById(`section${currentSection}`).classList.remove('hidden');
                updateProgressIndicator();
                updateNavigationButtons();
                
                if (currentSection === 2) {
                    initializeProductSelector();
                } else if (currentSection === 6) {
                    performValidation();
                }
                
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }
        
        // Update progress indicator
        function updateProgressIndicator() {
            // Update progress steps
            document.querySelectorAll('.progress-step').forEach((step, index) => {
                if (index < currentSection - 1) {
                    step.classList.add('completed');
                    step.classList.remove('active');
                } else if (index === currentSection - 1) {
                    step.classList.add('active');
                    step.classList.remove('completed');
                } else {
                    step.classList.remove('active', 'completed');
                }
            });
            
            // Update progress bar
            const progress = (currentSection / totalSections) * 100;
            document.getElementById('progressBar').style.width = `${progress}%`;
            document.getElementById('progressText').textContent = `第 ${currentSection} 步，共 ${totalSections} 步`;
        }
        
        // Update navigation buttons
        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            prevBtn.disabled = currentSection === 1;
            
            if (currentSection === totalSections) {
                nextBtn.style.display = 'none';
            } else {
                nextBtn.style.display = 'block';
            }
        }
        
        // Validate current section
        function validateCurrentSection() {
            const section = document.getElementById(`section${currentSection}`);
            const requiredFields = section.querySelectorAll('[required], .required');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (field.tagName === 'INPUT' || field.tagName === 'TEXTAREA') {
                    if (!field.value.trim()) {
                        field.classList.add('field-error');
                        isValid = false;
                    } else {
                        field.classList.remove('field-error');
                    }
                }
            });
            
            return isValid;
        }
        
        // Perform validation
        function performValidation() {
            const validationSummary = document.getElementById('validationSummary');
            validationSummary.innerHTML = '<div class="validation-title">正在验证配置...</div><div class="loading"></div>';
            
            setTimeout(() => {
                const validationItems = [
                    { name: '基础信息', status: 'success', message: '已完成' },
                    { name: '商品信息', status: 'success', message: '已完成' },
                    { name: '卖点结构', status: 'success', message: '已配置' },
                    { name: '人设选择', status: formData.selectedPersona ? 'success' : 'warning', message: formData.selectedPersona ? '已选择' : '未选择' },
                    { name: '高级设置', status: 'success', message: '使用默认值' }
                ];
                
                validationSummary.innerHTML = `
                    <div class="validation-title">配置验证结果</div>
                    ${validationItems.map(item => `
                        <div class="validation-item">
                            <span class="validation-icon ${item.status}">
                                ${item.status === 'success' ? '✓' : item.status === 'warning' ? '!' : '✗'}
                            </span>
                            <span>${item.name}: ${item.message}</span>
                        </div>
                    `).join('')}
                `;
            }, 1500);
        }
        
        // Update character count
        function updateCharacterCount(fieldId, counterId, min, max) {
            const field = document.getElementById(fieldId);
            const counter = document.getElementById(counterId);
            const length = field.value.length;
            
            counter.textContent = `${length} / ${max} 字符`;
            
            if (length < min) {
                counter.classList.add('error');
                counter.classList.remove('warning');
            } else if (length > max * 0.8) {
                counter.classList.add('warning');
                counter.classList.remove('error');
            } else {
                counter.classList.remove('error', 'warning');
            }
        }
        
        // Initialize form listeners
        function initializeFormListeners() {
            // Auto-save functionality
            const form = document.getElementById('operationalForm');
            let saveTimeout;
            
            form.addEventListener('input', function() {
                clearTimeout(saveTimeout);
                showSaveStatus('saving');
                
                saveTimeout = setTimeout(() => {
                    // Simulate save
                    saveFormData();
                    showSaveStatus('saved');
                }, 1000);
            });
        }
        
        // Show save status
        function showSaveStatus(status) {
            const statusElement = document.getElementById('saveStatus');
            statusElement.style.display = 'block';
            statusElement.className = `save-status ${status}`;
            
            const statusText = statusElement.querySelector('.save-status-text') || statusElement;
            const timestamp = new Date().toLocaleTimeString();
            
            switch(status) {
                case 'saving':
                    statusElement.innerHTML = '<span class="save-status-icon">⏳</span> 正在保存...';
                    break;
                case 'saved':
                    statusElement.innerHTML = `<span class="save-status-icon">✓</span> 已保存 <span class="save-timestamp">${timestamp}</span>`;
                    break;
                case 'error':
                    statusElement.innerHTML = '<span class="save-status-icon">✗</span> 保存失败';
                    break;
            }
            
            if (status === 'saved') {
                setTimeout(() => {
                    statusElement.style.display = 'none';
                }, 3000);
            }
        }
        
        // Save form data
        function saveFormData() {
            const form = document.getElementById('operationalForm');
            const formData = new FormData(form);
            
            // Store in localStorage for persistence
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });
            
            localStorage.setItem('operationalFormData', JSON.stringify(data));
        }
        
        // === Product Selector Functions ===
        
        // Load products from API
        async function loadProducts() {
            try {
                const response = await fetch('/api/v1/simple-products/');
                if (!response.ok) throw new Error('获取商品列表失败');
                
                availableProducts = await response.json();
                filteredProducts = [...availableProducts];
                renderProductList();
                
            } catch (error) {
                console.error('Failed to load products:', error);
                showProductError(error.message);
            }
        }
        
        // Render product list
        function renderProductList() {
            const productList = document.getElementById('productList');
            
            if (filteredProducts.length === 0) {
                productList.innerHTML = `
                    <div class="product-empty">
                        <div>😕 暂无商品</div>
                        <div style="margin-top: 8px; font-size: 0.875rem;">
                            请先在商品管理中添加商品
                        </div>
                    </div>
                `;
                return;
            }
            
            let html = '';
            filteredProducts.forEach(product => {
                const isSelected = selectedProduct && selectedProduct.id === product.id;
                const categoryText = getCategoryText(product.category);
                
                html += `
                    <div class="product-item ${isSelected ? 'selected' : ''}" onclick="selectProduct(${product.id})">
                        <div class="product-item-header">
                            <div>
                                <div class="product-item-name">${escapeHtml(product.name)}</div>
                                <div class="product-item-sku">${escapeHtml(product.sku)}</div>
                            </div>
                            <div class="product-item-price">¥${product.price || 0}</div>
                        </div>
                        <div class="product-item-meta">
                            <span>分类: ${categoryText}</span>
                            <span>库存: ${product.stock || 0}</span>
                        </div>
                    </div>
                `;
            });
            
            productList.innerHTML = html;
        }
        
        // Show product loading error
        function showProductError(message) {
            const productList = document.getElementById('productList');
            productList.innerHTML = `
                <div class="product-empty">
                    <div>⚠️ 加载失败</div>
                    <div style="margin-top: 8px; font-size: 0.875rem;">
                        ${message}
                    </div>
                    <button type="button" class="btn btn-sm btn-primary" onclick="loadProducts()" 
                            style="margin-top: 12px;">
                        重试
                    </button>
                </div>
            `;
        }
        
        // Select a product
        function selectProduct(productId) {
            selectedProduct = availableProducts.find(p => p.id === productId);
            if (!selectedProduct) return;
            
            // Update UI
            renderProductList();
            showSelectedProductPreview();
            
            // Store selected product ID
            document.getElementById('selectedProductId').value = productId;
            
            // Trigger validation update
            updateValidation();
        }
        
        // Show selected product preview
        function showSelectedProductPreview() {
            if (!selectedProduct) return;
            
            const preview = document.getElementById('selectedProductPreview');
            const categoryText = getCategoryText(selectedProduct.category);
            
            document.getElementById('previewProductName').textContent = selectedProduct.name;
            document.getElementById('previewProductSku').textContent = selectedProduct.sku;
            document.getElementById('previewProductCategory').textContent = categoryText;
            document.getElementById('previewProductPrice').textContent = `¥${selectedProduct.price || 0}`;
            document.getElementById('previewProductStock').textContent = `库存: ${selectedProduct.stock || 0}`;
            document.getElementById('previewProductDescription').textContent = 
                selectedProduct.description || '暂无商品描述';
            
            preview.style.display = 'block';
        }
        
        // Clear product selection
        function clearProductSelection() {
            selectedProduct = null;
            document.getElementById('selectedProductId').value = '';
            document.getElementById('selectedProductPreview').style.display = 'none';
            renderProductList();
            updateValidation();
        }
        
        // Handle product search
        function handleProductSearch() {
            clearTimeout(productSearchTimeout);
            productSearchTimeout = setTimeout(() => {
                const query = document.getElementById('productSearchInput').value.toLowerCase().trim();
                
                if (!query) {
                    filteredProducts = [...availableProducts];
                } else {
                    filteredProducts = availableProducts.filter(product => 
                        product.name.toLowerCase().includes(query) ||
                        product.sku.toLowerCase().includes(query)
                    );
                }
                
                renderProductList();
            }, 300);
        }
        
        // Refresh products
        function refreshProducts() {
            loadProducts();
        }
        
        // Open product management
        function openProductManagement() {
            window.open('./products', '_blank');
        }
        
        // Handle price configuration change
        function handlePriceConfigChange() {
            const useCustom = document.getElementById('useCustomPrice').checked;
            const customSection = document.getElementById('customPriceSection');
            
            if (useCustom) {
                customSection.style.display = 'block';
                // Pre-fill with selected product price if available
                if (selectedProduct) {
                    document.getElementById('customCurrentPrice').value = selectedProduct.price || '';
                }
            } else {
                customSection.style.display = 'none';
            }
        }
        
        // Utility functions for products
        function getCategoryText(category) {
            const categories = {
                'electronics': '电子产品',
                'clothing': '服装',
                'home': '家居用品',
                'beauty': '美妆护肤',
                'food': '食品饮料',
                'other': '其他'
            };
            return categories[category] || category;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Initialize product selector when section 2 is shown
        function initializeProductSelector() {
            if (availableProducts.length === 0) {
                loadProducts();
            }
        }
        
        // Submit configuration
        function submitConfiguration() {
            UI.Modal.confirm('确定要提交配置吗？提交后将生成直播脚本。', () => {
                UI.Loading.show('正在提交配置...');
                
                // Simulate submission
                setTimeout(() => {
                    UI.Loading.hide();
                    UI.Toast.success('配置提交成功！即将跳转到脚本预览页面...');
                    
                    setTimeout(() => {
                        // Redirect to script preview or dashboard
                        window.location.href = './admin_console.html';
                    }, 2000);
                }, 2000);
            });
        }
    </script>
</body>
</html>