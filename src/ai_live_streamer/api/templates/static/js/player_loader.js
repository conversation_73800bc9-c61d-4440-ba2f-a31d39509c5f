/**
 * Player Loader - 加载模块化的播放器组件
 * 这个文件用于确保所有模块按正确顺序加载
 */

(function() {
    'use strict';
    
    console.log('🔄 Loading modular audio player...');
    
    // 检查是否所有模块都已加载
    function checkModulesLoaded() {
        const required = ['ConnectionManager', 'AudioPlayer', 'ProtocolHandler', 'WebAudioPlayer'];
        const missing = required.filter(module => !window[module]);
        
        if (missing.length > 0) {
            console.error('❌ Missing modules:', missing);
            return false;
        }
        
        console.log('✅ All modules loaded successfully');
        return true;
    }
    
    // 初始化播放器实例
    function initializePlayer() {
        if (!checkModulesLoaded()) {
            console.error('❌ Cannot initialize player - modules missing');
            return null;
        }
        
        // 创建全局播放器实例
        window.audioPlayer = new WebAudioPlayer();
        
        // 设置UI回调（如果存在）
        if (typeof updatePlayerStatus === 'function') {
            window.audioPlayer.onStatusUpdate = updatePlayerStatus;
        }
        
        if (typeof handlePlayerError === 'function') {
            window.audioPlayer.onError = handlePlayerError;
        }
        
        console.log('✅ Audio player instance created');
        return window.audioPlayer;
    }
    
    // 等待DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializePlayer);
    } else {
        // DOM已加载，直接初始化
        setTimeout(initializePlayer, 100);
    }
    
    // 导出初始化函数供手动调用
    window.initializeAudioPlayer = initializePlayer;
})();