/**
 * WebSocket重连管理器
 * 控制面板WebSocket架构重构 v2.0
 * 
 * 提供健壮的WebSocket自动重连机制，支持指数退避策略
 */

class WebSocketReconnectManager {
    constructor(options = {}) {
        // 配置选项
        this.options = {
            initialDelay: options.initialDelay || 1000,           // 初始重连延迟（毫秒）
            maxDelay: options.maxDelay || 16000,                  // 最大重连延迟（毫秒）
            backoffMultiplier: options.backoffMultiplier || 2,    // 退避乘数
            maxRetries: options.maxRetries || Infinity,           // 最大重试次数
            jitterFactor: options.jitterFactor || 0.3,            // 抖动因子（0-1）
            heartbeatInterval: options.heartbeatInterval || 30000, // 心跳间隔（毫秒）
            heartbeatTimeout: options.heartbeatTimeout || 5000,    // 心跳超时（毫秒）
            enableHeartbeat: options.enableHeartbeat !== false,    // 是否启用心跳
            autoReconnect: options.autoReconnect !== false,        // 是否自动重连
            debug: options.debug || false                          // 调试模式
        };
        
        // 状态管理
        this.ws = null;                    // WebSocket实例
        this.url = null;                   // WebSocket URL
        this.protocols = null;             // 子协议
        this.retryCount = 0;               // 当前重试次数
        this.currentDelay = this.options.initialDelay; // 当前延迟
        this.reconnectTimer = null;        // 重连定时器
        this.heartbeatTimer = null;        // 心跳定时器
        this.heartbeatTimeoutTimer = null; // 心跳超时定时器
        this.heartbeatSequence = 0;        // 心跳序列号
        this.isIntentionallyClosed = false; // 是否主动关闭
        this.connectionState = 'disconnected'; // 连接状态
        this.lastMessageId = null;         // 最后接收的消息ID
        this.pendingMessages = [];         // 待发送消息队列
        
        // 事件处理器
        this.eventHandlers = {
            open: [],
            close: [],
            message: [],
            error: [],
            reconnecting: [],
            reconnected: [],
            maxRetriesReached: [],
            stateChange: []
        };
        
        // 性能监控
        this.stats = {
            connectAttempts: 0,
            successfulConnects: 0,
            failedConnects: 0,
            messagesSent: 0,
            messagesReceived: 0,
            lastConnectTime: null,
            lastDisconnectTime: null,
            totalUptime: 0,
            totalDowntime: 0
        };
        
        this.log('WebSocketReconnectManager initialized', this.options);
    }
    
    /**
     * 连接到WebSocket服务器
     */
    connect(url, protocols = []) {
        this.url = url;
        this.protocols = protocols;
        this.isIntentionallyClosed = false;
        
        this.log(`Connecting to ${url}`);
        this.stats.connectAttempts++;
        
        this.createWebSocket();
    }
    
    /**
     * 创建WebSocket实例
     */
    createWebSocket() {
        try {
            // 清理旧的WebSocket
            if (this.ws) {
                this.ws.onopen = null;
                this.ws.onclose = null;
                this.ws.onmessage = null;
                this.ws.onerror = null;
                
                if (this.ws.readyState === WebSocket.OPEN) {
                    this.ws.close();
                }
            }
            
            // 创建新的WebSocket
            this.ws = new WebSocket(this.url, this.protocols);
            
            // 设置事件处理器
            this.ws.onopen = this.handleOpen.bind(this);
            this.ws.onclose = this.handleClose.bind(this);
            this.ws.onmessage = this.handleMessage.bind(this);
            this.ws.onerror = this.handleError.bind(this);
            
            this.updateConnectionState('connecting');
            
        } catch (error) {
            this.log('Failed to create WebSocket:', error);
            this.stats.failedConnects++;
            this.handleError(error);
        }
    }
    
    /**
     * 处理连接打开
     */
    handleOpen(event) {
        this.log('WebSocket connected');
        
        // 更新统计
        this.stats.successfulConnects++;
        this.stats.lastConnectTime = Date.now();
        
        // 重置重连状态
        this.retryCount = 0;
        this.currentDelay = this.options.initialDelay;
        
        // 清除重连定时器
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        // 更新连接状态
        this.updateConnectionState('connected');
        
        // 启动心跳
        if (this.options.enableHeartbeat) {
            this.startHeartbeat();
        }
        
        // 发送待发送的消息
        this.flushPendingMessages();
        
        // 触发事件
        this.emit('open', event);
        
        // 如果是重连成功
        if (this.retryCount > 0) {
            this.emit('reconnected', {
                attempts: this.retryCount,
                delay: this.currentDelay
            });
        }
    }
    
    /**
     * 处理连接关闭
     */
    handleClose(event) {
        this.log('WebSocket closed', { code: event.code, reason: event.reason });
        
        // 更新统计
        this.stats.lastDisconnectTime = Date.now();
        
        // 停止心跳
        this.stopHeartbeat();
        
        // 更新连接状态
        this.updateConnectionState('disconnected');
        
        // 触发事件
        this.emit('close', event);
        
        // 自动重连
        if (!this.isIntentionallyClosed && this.options.autoReconnect) {
            this.scheduleReconnect();
        }
    }
    
    /**
     * 处理消息接收
     */
    handleMessage(event) {
        this.stats.messagesReceived++;
        
        try {
            // 尝试解析JSON消息
            let message = event.data;
            if (typeof message === 'string') {
                try {
                    message = JSON.parse(message);
                } catch (e) {
                    // 不是JSON，保持原样
                }
            }
            
            // 记录消息ID
            if (message && message.messageId) {
                this.lastMessageId = message.messageId;
            }
            
            // 处理心跳响应
            if (message && message.type === 'heartbeat_ack') {
                this.handleHeartbeatAck(message);
                return;
            }
            
            // 触发消息事件
            this.emit('message', event);
            
        } catch (error) {
            this.log('Error handling message:', error);
        }
    }
    
    /**
     * 处理错误
     */
    handleError(error) {
        this.log('WebSocket error:', error);
        this.stats.failedConnects++;
        
        this.emit('error', error);
    }
    
    /**
     * 计划重连
     */
    scheduleReconnect() {
        if (this.retryCount >= this.options.maxRetries) {
            this.log('Max retries reached');
            this.emit('maxRetriesReached', {
                retries: this.retryCount,
                maxRetries: this.options.maxRetries
            });
            return;
        }
        
        // 计算延迟（指数退避 + 抖动）
        const jitter = this.currentDelay * this.options.jitterFactor * (Math.random() * 2 - 1);
        const delay = Math.min(this.currentDelay + jitter, this.options.maxDelay);
        
        this.log(`Scheduling reconnect in ${Math.round(delay)}ms (attempt ${this.retryCount + 1})`);
        
        // 更新状态
        this.updateConnectionState('reconnecting');
        
        // 触发重连事件
        this.emit('reconnecting', {
            attempt: this.retryCount + 1,
            delay: delay
        });
        
        // 设置重连定时器
        this.reconnectTimer = setTimeout(() => {
            this.retryCount++;
            this.currentDelay = Math.min(this.currentDelay * this.options.backoffMultiplier, this.options.maxDelay);
            this.createWebSocket();
        }, delay);
    }
    
    /**
     * 发送消息
     */
    send(data) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            try {
                // 如果是对象，转换为JSON
                const message = typeof data === 'object' ? JSON.stringify(data) : data;
                
                this.ws.send(message);
                this.stats.messagesSent++;
                
                return true;
            } catch (error) {
                this.log('Failed to send message:', error);
                
                // 添加到待发送队列
                this.pendingMessages.push(data);
                
                return false;
            }
        } else {
            // 添加到待发送队列
            this.pendingMessages.push(data);
            
            this.log('WebSocket not ready, message queued');
            
            return false;
        }
    }
    
    /**
     * 发送待发送的消息
     */
    flushPendingMessages() {
        if (this.pendingMessages.length > 0) {
            this.log(`Flushing ${this.pendingMessages.length} pending messages`);
            
            const messages = [...this.pendingMessages];
            this.pendingMessages = [];
            
            messages.forEach(message => {
                this.send(message);
            });
        }
    }
    
    /**
     * 主动关闭连接
     */
    close(code = 1000, reason = 'Normal closure') {
        this.log('Closing WebSocket connection');
        
        this.isIntentionallyClosed = true;
        
        // 清除定时器
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        // 停止心跳
        this.stopHeartbeat();
        
        // 关闭WebSocket
        if (this.ws) {
            this.ws.close(code, reason);
        }
        
        this.updateConnectionState('disconnected');
    }
    
    /**
     * 启动心跳
     */
    startHeartbeat() {
        this.stopHeartbeat();
        
        this.heartbeatTimer = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                const heartbeat = {
                    type: 'heartbeat',
                    sequence: ++this.heartbeatSequence,
                    timestamp: Date.now()
                };
                
                this.send(heartbeat);
                
                // 设置心跳超时
                this.heartbeatTimeoutTimer = setTimeout(() => {
                    this.log('Heartbeat timeout, closing connection');
                    this.ws.close(4000, 'Heartbeat timeout');
                }, this.options.heartbeatTimeout);
            }
        }, this.options.heartbeatInterval);
    }
    
    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
        
        if (this.heartbeatTimeoutTimer) {
            clearTimeout(this.heartbeatTimeoutTimer);
            this.heartbeatTimeoutTimer = null;
        }
    }
    
    /**
     * 处理心跳响应
     */
    handleHeartbeatAck(message) {
        // 清除心跳超时定时器
        if (this.heartbeatTimeoutTimer) {
            clearTimeout(this.heartbeatTimeoutTimer);
            this.heartbeatTimeoutTimer = null;
        }
    }
    
    /**
     * 更新连接状态
     */
    updateConnectionState(state) {
        const oldState = this.connectionState;
        this.connectionState = state;
        
        if (oldState !== state) {
            this.emit('stateChange', {
                oldState,
                newState: state
            });
        }
    }
    
    /**
     * 注册事件处理器
     */
    on(event, handler) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].push(handler);
        }
    }
    
    /**
     * 移除事件处理器
     */
    off(event, handler) {
        if (this.eventHandlers[event]) {
            const index = this.eventHandlers[event].indexOf(handler);
            if (index > -1) {
                this.eventHandlers[event].splice(index, 1);
            }
        }
    }
    
    /**
     * 触发事件
     */
    emit(event, data) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            });
        }
    }
    
    /**
     * 获取连接状态
     */
    getState() {
        return this.connectionState;
    }
    
    /**
     * 是否已连接
     */
    isConnected() {
        return this.ws && this.ws.readyState === WebSocket.OPEN;
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return { ...this.stats };
    }
    
    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = {
            connectAttempts: 0,
            successfulConnects: 0,
            failedConnects: 0,
            messagesSent: 0,
            messagesReceived: 0,
            lastConnectTime: null,
            lastDisconnectTime: null,
            totalUptime: 0,
            totalDowntime: 0
        };
    }
    
    /**
     * 日志输出
     */
    log(...args) {
        if (this.options.debug) {
            console.log('[WebSocketReconnect]', ...args);
        }
    }
}

// 导出
window.WebSocketReconnectManager = WebSocketReconnectManager;

// 兼容性导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WebSocketReconnectManager;
}