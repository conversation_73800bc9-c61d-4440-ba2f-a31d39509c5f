/**
 * UI Components Library
 * 统一的UI组件库
 * 
 * 提供可复用的UI组件和交互功能
 * Author: <PERSON> Code
 * Version: 1.0.0
 */

(function(window) {
    'use strict';

    // UI Components Namespace
    const UI = window.UI || {};

    /**
     * Toast Notification Component
     * 轻量级通知提示组件
     */
    UI.Toast = {
        container: null,
        
        init() {
            if (!this.container) {
                this.container = document.createElement('div');
                this.container.className = 'toast-container';
                this.container.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                `;
                document.body.appendChild(this.container);
            }
        },
        
        show(message, type = 'info', duration = 3000) {
            this.init();
            
            const toast = document.createElement('div');
            toast.className = `toast toast-${type} animate-fadeInUp`;
            toast.style.cssText = `
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                display: flex;
                align-items: center;
                gap: 10px;
                min-width: 250px;
                max-width: 400px;
                word-wrap: break-word;
                animation: fadeInUp 0.3s ease;
            `;
            
            // Set colors based on type
            const colors = {
                success: { bg: '#d4f4dd', color: '#22543d', icon: '✓' },
                error: { bg: '#fee', color: '#742a2a', icon: '✕' },
                warning: { bg: '#fff5e6', color: '#744210', icon: '!' },
                info: { bg: '#e6f4ff', color: '#2c5282', icon: 'ℹ' }
            };
            
            const style = colors[type] || colors.info;
            toast.style.backgroundColor = style.bg;
            toast.style.color = style.color;
            
            toast.innerHTML = `
                <span style="font-size: 18px; font-weight: bold;">${style.icon}</span>
                <span>${message}</span>
            `;
            
            this.container.appendChild(toast);
            
            // Auto remove
            setTimeout(() => {
                toast.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, duration);
        },
        
        success(message, duration) {
            this.show(message, 'success', duration);
        },
        
        error(message, duration) {
            this.show(message, 'error', duration);
        },
        
        warning(message, duration) {
            this.show(message, 'warning', duration);
        },
        
        info(message, duration) {
            this.show(message, 'info', duration);
        }
    };

    /**
     * Modal Component
     * 模态框组件
     */
    UI.Modal = {
        create(options = {}) {
            const {
                title = '提示',
                content = '',
                confirmText = '确定',
                cancelText = '取消',
                onConfirm = () => {},
                onCancel = () => {},
                showCancel = true,
                type = 'default'
            } = options;
            
            // Create modal backdrop
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop';
            backdrop.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 9998;
                animation: fadeIn 0.2s ease;
            `;
            
            // Create modal
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                z-index: 9999;
                min-width: 400px;
                max-width: 90%;
                animation: slideUp 0.3s ease;
            `;
            
            // Type-specific styling
            const typeStyles = {
                success: { icon: '✓', color: '#48bb78' },
                error: { icon: '✕', color: '#fc8181' },
                warning: { icon: '!', color: '#f6ad55' },
                info: { icon: 'ℹ', color: '#63b3ed' }
            };
            
            const typeStyle = typeStyles[type] || { icon: '', color: '#667eea' };
            
            modal.innerHTML = `
                <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #e5e7eb;">
                    <h3 style="margin: 0; display: flex; align-items: center; gap: 10px;">
                        ${typeStyle.icon ? `<span style="color: ${typeStyle.color}; font-size: 24px;">${typeStyle.icon}</span>` : ''}
                        ${title}
                    </h3>
                </div>
                <div class="modal-body" style="padding: 20px;">
                    ${content}
                </div>
                <div class="modal-footer" style="padding: 20px; border-top: 1px solid #e5e7eb; display: flex; justify-content: flex-end; gap: 10px;">
                    ${showCancel ? `<button class="btn btn-secondary modal-cancel">${cancelText}</button>` : ''}
                    <button class="btn btn-primary modal-confirm">${confirmText}</button>
                </div>
            `;
            
            // Add event listeners
            const confirmBtn = modal.querySelector('.modal-confirm');
            const cancelBtn = modal.querySelector('.modal-cancel');
            
            const close = () => {
                backdrop.style.animation = 'fadeOut 0.2s ease';
                modal.style.animation = 'slideDown 0.2s ease';
                setTimeout(() => {
                    backdrop.remove();
                    modal.remove();
                }, 200);
            };
            
            confirmBtn.addEventListener('click', () => {
                onConfirm();
                close();
            });
            
            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    onCancel();
                    close();
                });
            }
            
            backdrop.addEventListener('click', () => {
                if (showCancel) {
                    onCancel();
                    close();
                }
            });
            
            // Append to body
            document.body.appendChild(backdrop);
            document.body.appendChild(modal);
            
            return { backdrop, modal, close };
        },

        show(title, content) {
            // Create and show a simple modal without buttons
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop';
            backdrop.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 9998;
                animation: fadeIn 0.2s ease;
            `;
            
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                z-index: 9999;
                min-width: 500px;
                max-width: 90%;
                max-height: 80%;
                animation: slideUp 0.3s ease;
                overflow-y: auto;
            `;
            
            modal.innerHTML = `
                <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #e5e7eb;">
                    <h3 style="margin: 0;">${title}</h3>
                </div>
                <div class="modal-body" style="padding: 20px;">
                    ${content}
                </div>
            `;
            
            // Store references for hide method
            this.currentModal = modal;
            this.currentBackdrop = backdrop;
            
            document.body.appendChild(backdrop);
            document.body.appendChild(modal);
        },

        hide() {
            if (this.currentModal && this.currentBackdrop) {
                this.currentBackdrop.style.animation = 'fadeOut 0.2s ease';
                this.currentModal.style.animation = 'slideDown 0.2s ease';
                
                setTimeout(() => {
                    if (this.currentModal) this.currentModal.remove();
                    if (this.currentBackdrop) this.currentBackdrop.remove();
                    this.currentModal = null;
                    this.currentBackdrop = null;
                }, 200);
            }
        },
        
        confirm(message, onConfirm = () => {}) {
            return this.create({
                title: '确认',
                content: message,
                type: 'warning',
                onConfirm
            });
        },
        
        alert(message, type = 'info') {
            return this.create({
                title: type === 'error' ? '错误' : type === 'success' ? '成功' : '提示',
                content: message,
                type: type,
                showCancel: false
            });
        }
    };

    /**
     * Loading Component
     * 加载指示器组件
     */
    UI.Loading = {
        overlay: null,
        
        show(message = '加载中...') {
            if (this.overlay) return;
            
            this.overlay = document.createElement('div');
            this.overlay.className = 'loading-overlay';
            this.overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.9);
                z-index: 9997;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.2s ease;
            `;
            
            this.overlay.innerHTML = `
                <div class="loading-spinner" style="
                    width: 50px;
                    height: 50px;
                    border: 4px solid #e5e7eb;
                    border-top-color: #667eea;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                "></div>
                <div style="margin-top: 20px; color: #4b5563; font-size: 14px;">${message}</div>
            `;
            
            document.body.appendChild(this.overlay);
        },
        
        hide() {
            if (this.overlay) {
                this.overlay.style.animation = 'fadeOut 0.2s ease';
                setTimeout(() => {
                    if (this.overlay) {
                        this.overlay.remove();
                        this.overlay = null;
                    }
                }, 200);
            }
        }
    };

    /**
     * Progress Bar Component
     * 进度条组件
     */
    UI.ProgressBar = {
        create(options = {}) {
            const {
                container,
                value = 0,
                max = 100,
                label = '',
                showPercentage = true,
                animated = true
            } = options;
            
            const progressWrapper = document.createElement('div');
            progressWrapper.className = 'progress-wrapper';
            progressWrapper.style.cssText = 'margin-bottom: 20px;';
            
            if (label) {
                const labelEl = document.createElement('div');
                labelEl.style.cssText = 'margin-bottom: 8px; font-size: 14px; color: #4b5563;';
                labelEl.textContent = label;
                progressWrapper.appendChild(labelEl);
            }
            
            const progress = document.createElement('div');
            progress.className = 'progress';
            progress.style.cssText = `
                height: 8px;
                background: #e5e7eb;
                border-radius: 9999px;
                overflow: hidden;
                position: relative;
            `;
            
            const progressBar = document.createElement('div');
            progressBar.className = 'progress-bar';
            progressBar.style.cssText = `
                height: 100%;
                background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                width: ${(value / max) * 100}%;
                transition: width 0.3s ease;
                position: relative;
                overflow: hidden;
            `;
            
            if (animated) {
                const shimmer = document.createElement('div');
                shimmer.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    bottom: 0;
                    right: 0;
                    background: linear-gradient(
                        90deg,
                        transparent 0%,
                        rgba(255, 255, 255, 0.4) 50%,
                        transparent 100%
                    );
                    animation: shimmer 2s infinite;
                `;
                progressBar.appendChild(shimmer);
            }
            
            progress.appendChild(progressBar);
            progressWrapper.appendChild(progress);
            
            if (showPercentage) {
                const percentage = document.createElement('div');
                percentage.style.cssText = 'margin-top: 8px; text-align: center; font-size: 14px; color: #6b7280;';
                percentage.textContent = `${Math.round((value / max) * 100)}%`;
                progressWrapper.appendChild(percentage);
            }
            
            if (container) {
                if (typeof container === 'string') {
                    document.querySelector(container).appendChild(progressWrapper);
                } else {
                    container.appendChild(progressWrapper);
                }
            }
            
            return {
                element: progressWrapper,
                update(newValue) {
                    const percent = Math.min(100, Math.max(0, (newValue / max) * 100));
                    progressBar.style.width = `${percent}%`;
                    if (showPercentage) {
                        progressWrapper.querySelector('div:last-child').textContent = `${Math.round(percent)}%`;
                    }
                }
            };
        }
    };

    /**
     * Dropdown Component
     * 下拉菜单组件
     */
    UI.Dropdown = {
        create(trigger, options = {}) {
            const {
                items = [],
                position = 'bottom',
                onSelect = () => {}
            } = options;
            
            const dropdown = document.createElement('div');
            dropdown.className = 'dropdown';
            dropdown.style.cssText = `
                position: absolute;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border: 1px solid #e5e7eb;
                padding: 8px 0;
                z-index: 1000;
                min-width: 150px;
                display: none;
                animation: scaleIn 0.2s ease;
            `;
            
            items.forEach(item => {
                const menuItem = document.createElement('div');
                menuItem.className = 'dropdown-item';
                menuItem.style.cssText = `
                    padding: 8px 16px;
                    cursor: pointer;
                    transition: background 0.2s ease;
                    font-size: 14px;
                    color: #374151;
                `;
                
                menuItem.textContent = item.label;
                menuItem.addEventListener('mouseenter', () => {
                    menuItem.style.background = '#f3f4f6';
                });
                menuItem.addEventListener('mouseleave', () => {
                    menuItem.style.background = 'transparent';
                });
                menuItem.addEventListener('click', () => {
                    onSelect(item);
                    this.hide(dropdown);
                });
                
                dropdown.appendChild(menuItem);
            });
            
            // Position dropdown
            const rect = trigger.getBoundingClientRect();
            dropdown.style.top = `${rect.bottom + 5}px`;
            dropdown.style.left = `${rect.left}px`;
            
            // Toggle on trigger click
            trigger.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggle(dropdown);
            });
            
            // Close on outside click
            document.addEventListener('click', () => {
                this.hide(dropdown);
            });
            
            document.body.appendChild(dropdown);
            
            return dropdown;
        },
        
        show(dropdown) {
            dropdown.style.display = 'block';
        },
        
        hide(dropdown) {
            dropdown.style.display = 'none';
        },
        
        toggle(dropdown) {
            if (dropdown.style.display === 'none') {
                this.show(dropdown);
            } else {
                this.hide(dropdown);
            }
        }
    };

    /**
     * Tabs Component
     * 标签页组件
     */
    UI.Tabs = {
        init(container) {
            const tabs = container.querySelectorAll('[data-tab]');
            const contents = container.querySelectorAll('[data-tab-content]');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const target = tab.dataset.tab;
                    
                    // Update active tab
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    
                    // Update active content
                    contents.forEach(content => {
                        if (content.dataset.tabContent === target) {
                            content.style.display = 'block';
                            content.style.animation = 'fadeIn 0.3s ease';
                        } else {
                            content.style.display = 'none';
                        }
                    });
                });
            });
            
            // Activate first tab by default
            if (tabs.length > 0) {
                tabs[0].click();
            }
        }
    };

    /**
     * Form Validation
     * 表单验证辅助
     */
    UI.FormValidation = {
        rules: {
            required: (value) => value && value.trim() !== '',
            email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
            minLength: (min) => (value) => value && value.length >= min,
            maxLength: (max) => (value) => value && value.length <= max,
            pattern: (pattern) => (value) => pattern.test(value)
        },
        
        validate(form, rules) {
            const errors = {};
            
            Object.keys(rules).forEach(fieldName => {
                const field = form.querySelector(`[name="${fieldName}"]`);
                if (!field) return;
                
                const fieldRules = rules[fieldName];
                const value = field.value;
                
                for (const rule of fieldRules) {
                    if (!rule.validator(value)) {
                        errors[fieldName] = rule.message;
                        break;
                    }
                }
            });
            
            return {
                isValid: Object.keys(errors).length === 0,
                errors
            };
        },
        
        showErrors(form, errors) {
            // Clear previous errors
            form.querySelectorAll('.error-message').forEach(el => el.remove());
            form.querySelectorAll('.error').forEach(el => el.classList.remove('error'));
            
            // Show new errors
            Object.keys(errors).forEach(fieldName => {
                const field = form.querySelector(`[name="${fieldName}"]`);
                if (!field) return;
                
                field.classList.add('error');
                
                const errorEl = document.createElement('div');
                errorEl.className = 'error-message';
                errorEl.style.cssText = 'color: #ef4444; font-size: 12px; margin-top: 4px;';
                errorEl.textContent = errors[fieldName];
                
                field.parentNode.appendChild(errorEl);
            });
        }
    };

    /**
     * Smooth Scroll
     * 平滑滚动
     */
    UI.smoothScroll = function(target, duration = 500) {
        const targetElement = typeof target === 'string' ? document.querySelector(target) : target;
        if (!targetElement) return;
        
        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        let startTime = null;
        
        function animation(currentTime) {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const run = ease(timeElapsed, startPosition, distance, duration);
            window.scrollTo(0, run);
            if (timeElapsed < duration) requestAnimationFrame(animation);
        }
        
        function ease(t, b, c, d) {
            t /= d / 2;
            if (t < 1) return c / 2 * t * t + b;
            t--;
            return -c / 2 * (t * (t - 2) - 1) + b;
        }
        
        requestAnimationFrame(animation);
    };

    /**
     * Debounce Function
     * 防抖函数
     */
    UI.debounce = function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    };

    /**
     * Throttle Function
     * 节流函数
     */
    UI.throttle = function(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    };

    /**
     * Copy to Clipboard
     * 复制到剪贴板
     */
    UI.copyToClipboard = async function(text) {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
                UI.Toast.success('已复制到剪贴板');
            } else {
                // Fallback
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                try {
                    document.execCommand('copy');
                    UI.Toast.success('已复制到剪贴板');
                } catch (err) {
                    UI.Toast.error('复制失败');
                }
                
                document.body.removeChild(textArea);
            }
        } catch (err) {
            UI.Toast.error('复制失败');
        }
    };

    /**
     * Format Date
     * 日期格式化
     */
    UI.formatDate = function(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    };

    /**
     * Initialize all components
     * 初始化所有组件
     */
    UI.init = function() {
        // Auto-init tabs
        document.querySelectorAll('[data-tabs]').forEach(container => {
            UI.Tabs.init(container);
        });
        
        // Auto-init tooltips (if needed)
        // Auto-init other components...
        
        console.log('UI Components Library initialized');
    };

    // Add CSS animations if not exists
    if (!document.querySelector('#ui-animations')) {
        const style = document.createElement('style');
        style.id = 'ui-animations';
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            @keyframes slideUp {
                from {
                    opacity: 0;
                    transform: translate(-50%, -40%);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%);
                }
            }
            @keyframes slideDown {
                from {
                    opacity: 1;
                    transform: translate(-50%, -50%);
                }
                to {
                    opacity: 0;
                    transform: translate(-50%, -40%);
                }
            }
            @keyframes scaleIn {
                from {
                    opacity: 0;
                    transform: scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            @keyframes shimmer {
                0% { transform: translateX(-100%); }
                100% { transform: translateX(100%); }
            }
        `;
        document.head.appendChild(style);
    }

    // Export to window
    window.UI = UI;

    // Auto-init when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', UI.init);
    } else {
        UI.init();
    }

})(window);