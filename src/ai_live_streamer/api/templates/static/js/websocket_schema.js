/**
 * WebSocket消息结构定义
 * 控制面板WebSocket架构重构 v2.0
 * 
 * 此文件定义了前后端WebSocket通信的统一消息格式
 */

// 消息类型枚举
const WSMessageType = {
    // 状态相关
    STATE_UPDATE: 'state_update',      // 服务端推送状态更新
    STATE_REQUEST: 'state_request',    // 客户端请求当前状态
    
    // 控制指令
    COMMAND: 'command',                // 客户端发送控制指令
    REPORT: 'report',                  // 客户端报告执行结果
    
    // 错误处理
    ERROR: 'error',                    // 错误消息
    
    // 心跳
    HEARTBEAT: 'heartbeat',            // 心跳消息
    HEARTBEAT_ACK: 'heartbeat_ack',    // 心跳响应
    
    // 音频相关
    AUDIO_DATA: 'audio_data',          // 音频数据（二进制）
    AUDIO_CONFIG: 'audio_config',      // 音频配置
    
    // QA相关
    QA_REQUEST: 'qa_request',          // QA请求
    QA_RESPONSE: 'qa_response',        // QA响应
    
    // 会话管理
    SESSION_JOIN: 'session_join',      // 加入会话
    SESSION_LEAVE: 'session_leave',    // 离开会话
    SESSION_REJOIN: 'session_rejoin'   // 重新加入会话
};

// 指令类型枚举
const CommandAction = {
    PLAY: 'play',
    PAUSE: 'pause',
    RESUME: 'resume',
    STOP: 'stop',
    SKIP: 'skip',
    SEEK: 'seek',
    VOLUME: 'volume',
    MUTE: 'mute',
    UNMUTE: 'unmute'
};

// 基础消息结构
class WSMessage {
    constructor(type, data = {}) {
        this.type = type;
        this.timestamp = Date.now();
        this.messageId = this.generateMessageId();
        this.data = data;
    }
    
    generateMessageId() {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    
    toJSON() {
        return {
            type: this.type,
            timestamp: this.timestamp,
            messageId: this.messageId,
            data: this.data
        };
    }
}

// 状态更新消息
class StateUpdateMessage extends WSMessage {
    constructor(stateData) {
        super(WSMessageType.STATE_UPDATE, stateData);
    }
}

// 指令消息
class CommandMessage extends WSMessage {
    constructor(action, params = {}) {
        const commandId = `cmd-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        super(WSMessageType.COMMAND, {
            commandId,
            action,
            params
        });
        this.commandId = commandId;
    }
}

// 报告消息
class ReportMessage extends WSMessage {
    constructor(commandId, status, result = {}) {
        super(WSMessageType.REPORT, {
            commandId,
            status,
            result,
            executedAt: Date.now()
        });
    }
}

// 错误消息
class ErrorMessage extends WSMessage {
    constructor(error, context = {}) {
        super(WSMessageType.ERROR, {
            error: error.message || error,
            code: error.code || 'UNKNOWN_ERROR',
            context,
            stack: error.stack || null
        });
    }
}

// 心跳消息
class HeartbeatMessage extends WSMessage {
    constructor(sequence = 0) {
        super(WSMessageType.HEARTBEAT, {
            sequence,
            clientTime: Date.now()
        });
    }
}

// 会话加入消息
class SessionJoinMessage extends WSMessage {
    constructor(sessionId, metadata = {}) {
        super(WSMessageType.SESSION_JOIN, {
            sessionId,
            metadata,
            rejoin: false
        });
    }
}

// 会话重新加入消息
class SessionRejoinMessage extends WSMessage {
    constructor(sessionId, lastMessageId = null, metadata = {}) {
        super(WSMessageType.SESSION_REJOIN, {
            sessionId,
            lastMessageId,
            metadata,
            rejoin: true
        });
    }
}

// QA请求消息
class QARequestMessage extends WSMessage {
    constructor(question, priority = 'medium', metadata = {}) {
        super(WSMessageType.QA_REQUEST, {
            question,
            priority,
            metadata,
            requestId: `qa-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        });
    }
}

// 消息验证器
class MessageValidator {
    static validate(message) {
        if (!message) {
            throw new Error('Message is null or undefined');
        }
        
        if (!message.type) {
            throw new Error('Message type is required');
        }
        
        if (!message.timestamp) {
            throw new Error('Message timestamp is required');
        }
        
        if (!message.messageId) {
            throw new Error('Message ID is required');
        }
        
        // 类型特定验证
        switch (message.type) {
            case WSMessageType.COMMAND:
                if (!message.data?.commandId || !message.data?.action) {
                    throw new Error('Command message requires commandId and action');
                }
                break;
                
            case WSMessageType.REPORT:
                if (!message.data?.commandId || !message.data?.status) {
                    throw new Error('Report message requires commandId and status');
                }
                break;
                
            case WSMessageType.SESSION_JOIN:
            case WSMessageType.SESSION_REJOIN:
                if (!message.data?.sessionId) {
                    throw new Error('Session message requires sessionId');
                }
                break;
        }
        
        return true;
    }
    
    static isValidType(type) {
        return Object.values(WSMessageType).includes(type);
    }
}

// 消息工厂
class MessageFactory {
    static createStateUpdate(stateData) {
        return new StateUpdateMessage(stateData);
    }
    
    static createCommand(action, params) {
        return new CommandMessage(action, params);
    }
    
    static createReport(commandId, status, result) {
        return new ReportMessage(commandId, status, result);
    }
    
    static createError(error, context) {
        return new ErrorMessage(error, context);
    }
    
    static createHeartbeat(sequence) {
        return new HeartbeatMessage(sequence);
    }
    
    static createSessionJoin(sessionId, metadata) {
        return new SessionJoinMessage(sessionId, metadata);
    }
    
    static createSessionRejoin(sessionId, lastMessageId, metadata) {
        return new SessionRejoinMessage(sessionId, lastMessageId, metadata);
    }
    
    static createQARequest(question, priority, metadata) {
        return new QARequestMessage(question, priority, metadata);
    }
    
    static parseMessage(jsonString) {
        try {
            const parsed = typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString;
            MessageValidator.validate(parsed);
            return parsed;
        } catch (error) {
            throw new Error(`Failed to parse WebSocket message: ${error.message}`);
        }
    }
}

// 导出
window.WSMessageType = WSMessageType;
window.CommandAction = CommandAction;
window.WSMessage = WSMessage;
window.MessageFactory = MessageFactory;
window.MessageValidator = MessageValidator;

// 兼容性导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        WSMessageType,
        CommandAction,
        WSMessage,
        MessageFactory,
        MessageValidator
    };
}