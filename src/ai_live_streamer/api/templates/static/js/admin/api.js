/**
 * Admin API Client
 * 统一的API客户端，处理所有后端调用
 */

class AdminApi {
    constructor() {
        this.baseURL = '';
        this.timeout = 30000;
        this.retryAttempts = 3;
        this.retryDelay = 1000;
    }

    /**
     * 通用的fetch封装
     */
    async request(url, options = {}) {
        const config = {
            timeout: this.timeout,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        let lastError;
        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), config.timeout);

                const response = await fetch(url, {
                    ...config,
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                lastError = error;
                if (attempt < this.retryAttempts && error.name !== 'AbortError') {
                    await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
                    continue;
                }
                throw error;
            }
        }
    }

    // ===== 商品管理 API =====
    async getProducts() {
        return await this.request('/api/v1/simple-products/');
    }

    async getProduct(productId) {
        return await this.request(`/api/v1/simple-products/${productId}`);
    }

    async createProduct(productData) {
        return await this.request('/api/v1/simple-products/', {
            method: 'POST',
            body: JSON.stringify(productData)
        });
    }

    async updateProduct(productId, productData) {
        return await this.request(`/api/v1/simple-products/${productId}`, {
            method: 'PUT',
            body: JSON.stringify(productData)
        });
    }

    async deleteProduct(productId) {
        return await this.request(`/api/v1/simple-products/${productId}`, {
            method: 'DELETE'
        });
    }

    // ===== 表单管理 API =====
    async getForms() {
        return await this.request('/api/console/forms');
    }

    async deleteForm(formId) {
        return await this.request(`/api/console/forms/${formId}`, {
            method: 'DELETE'
        });
    }

    // ===== 脚本管理 API =====
    async getScripts() {
        return await this.request('/api/console/scripts');
    }

    async generateScript(formId, regenerate = true) {
        return await this.request('/api/script-preview/generate', {
            method: 'POST',
            body: JSON.stringify({ form_id: formId, regenerate })
        });
    }

    async deleteScript(formId) {
        return await this.request(`/api/console/scripts/${formId}`, {
            method: 'DELETE'
        });
    }

    async prepareStream(formId, options = {}) {
        const data = {
            form_id: formId,
            persona: 'default',
            auto_start: false,
            ...options
        };
        return await this.request('/api/script-preview/prepare-stream', {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // ===== 系统状态 API =====
    async getStats() {
        return await this.request('/api/console/stats');
    }

    async getAchievements() {
        return await this.request('/api/achievements/');
    }

    async getStreamStatus() {
        return await this.request('/api/control/status');
    }

    async stopStream(reason = '停止直播') {
        return await this.request('/api/control/sessions/main/stop', {
            method: 'POST',
            body: JSON.stringify({ reason })
        });
    }
}

// 创建全局实例
window.AdminApi = new AdminApi();