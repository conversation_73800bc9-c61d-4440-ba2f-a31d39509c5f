/**
 * Admin Console Main Entry
 * 管理控制台主入口文件
 * 
 * 负责Tab切换、事件委托和模块加载
 */

class AdminConsole {
    constructor() {
        this.currentTab = 'dashboard';
        this.tabModules = {};
        this.initialized = false;
    }

    init() {
        if (this.initialized) return;

        // 初始化事件委托
        this.initEventDelegation();

        // 初始化Tab切换
        this.initTabSwitching();

        // 加载默认Tab内容
        this.loadTabContent(this.currentTab);

        this.initialized = true;
        console.log('Admin Console initialized');
    }

    /**
     * 初始化事件委托
     */
    initEventDelegation() {
        const mainContainer = document.querySelector('.main-container');
        const navContainer = document.querySelector('.nav-container');

        if (!mainContainer || !navContainer) {
            console.error('Required containers not found');
            return;
        }

        // 导航Tab点击事件
        DOM.delegate(navContainer, '[data-tab]', 'click', (e, target) => {
            const tabName = target.dataset.tab;
            this.showTab(tabName);
        });

        // 通用动作事件委托
        DOM.delegate(mainContainer, '[data-action]', 'click', (e, target) => {
            e.preventDefault();
            const action = target.dataset.action;
            console.log('Action clicked:', action, target);
            this.handleAction(action, target, e);
        });

        // 通用输入事件委托
        DOM.delegate(mainContainer, '[data-action]', 'input', (e, target) => {
            const action = target.dataset.action;
            this.handleInputAction(action, target, e);
        });

        DOM.delegate(mainContainer, '[data-action]', 'change', (e, target) => {
            const action = target.dataset.action;
            this.handleInputAction(action, target, e);
        });
    }

    /**
     * 初始化Tab切换
     */
    initTabSwitching() {
        // 设置初始活跃Tab
        const activeTab = DOM.$('.nav-tab.active');
        if (activeTab && activeTab.dataset.tab) {
            this.currentTab = activeTab.dataset.tab;
        }
    }

    /**
     * 显示指定Tab
     */
    showTab(tabName) {
        // 更新导航状态
        DOM.$$('.nav-tab').forEach(tab => {
            DOM.removeClass(tab, 'active');
        });
        DOM.addClass(`[data-tab="${tabName}"]`, 'active');

        // 更新内容区域
        DOM.$$('.tab-content').forEach(content => {
            DOM.removeClass(content, 'active');
        });
        DOM.addClass(`#${tabName}`, 'active');

        // 更新当前Tab
        this.currentTab = tabName;

        // 加载Tab内容
        this.loadTabContent(tabName);
    }

    /**
     * 加载Tab内容
     */
    async loadTabContent(tabName) {
        try {
            switch(tabName) {
                case 'dashboard':
                    await this.loadDashboard();
                    break;
                case 'products':
                    await this.loadProducts();
                    break;
                case 'forms':
                    await this.loadForms();
                    break;
                case 'scripts':
                    await this.loadScripts();
                    break;
                case 'monitor':
                    // Monitor tab doesn't need loading
                    break;
            }
        } catch (error) {
            console.error(`Failed to load tab content for ${tabName}:`, error);
            UI.Toast.error(`加载${tabName}失败: ${error.message}`);
        }
    }

    /**
     * 处理点击动作
     */
    async handleAction(action, element, event) {
        const data = element.dataset;

        switch(action) {
            case 'show-tab':
                this.showTab(data.tab);
                break;

            case 'navigate-to-step':
                this.navigateToStep(data.step);
                break;

            case 'open-link':
                window.open(data.url, data.target || '_blank');
                break;

            case 'load-products':
                await this.loadProducts();
                break;

            case 'load-forms':
                await this.loadForms();
                break;

            case 'load-scripts':
                await this.loadScripts();
                break;

            case 'open-create-product-modal':
                this.openCreateProductModal();
                break;

            case 'show-generate-script-modal':
                this.showGenerateScriptModal();
                break;

            case 'copy-to-clipboard':
                await DOM.copyToClipboard(data.text);
                break;

            // 产品相关动作
            case 'manage-product':
                this.manageProduct(data.productId);
                break;

            case 'delete-product':
                await this.deleteProduct(data.productId);
                break;

            case 'manage-product-qa':
                this.manageProductQA(data.productId);
                break;

            // 脚本和直播相关动作
            case 'launch-stream-from-script':
                await this.launchStreamFromScript(data.formId, data.formTitle);
                break;

            case 'delete-script':
                await this.deleteScript(data.formId);
                break;

            case 'generate-script-for-form':
                await this.generateScriptForForm(data.formId);
                break;

            case 'delete-form':
                await this.deleteForm(data.formId);
                break;

            default:
                console.warn(`Unknown action: ${action}`);
        }
    }

    /**
     * 处理输入动作（搜索、筛选等）
     */
    handleInputAction(action, element, event) {
        switch(action) {
            case 'handle-product-search':
                this.debouncedProductSearch();
                break;

            case 'handle-product-filter':
                this.handleProductFilter();
                break;
        }
    }

    /**
     * 导航到工作流步骤
     */
    navigateToStep(step) {
        switch(step) {
            case 'product':
                this.showTab('products');
                break;
            case 'config':
                window.location.href = '/config';
                break;
            case 'script':
                this.showTab('scripts');
                break;
            case 'live':
                window.open('/control', '_blank');
                break;
        }
    }

    /**
     * === Dashboard相关方法 ===
     */
    async loadDashboard() {
        try {
            // 并行加载统计数据和成就数据
            await Promise.all([
                this.loadDashboardStats(),
                this.loadAchievementsData()
            ]);
        } catch (error) {
            console.error('Failed to load dashboard:', error);
        }
    }

    async loadDashboardStats() {
        try {
            const response = await fetch('/api/console/stats');
            const data = await response.json();
            
            DOM.text('#totalConfigs', data.total_forms);
            DOM.text('#totalScriptsCount', data.total_scripts);
            
            // Update system status badge
            const statusBadge = DOM.$('#systemStatusBadge');
            if (statusBadge) {
                statusBadge.textContent = data.system_status === 'online' ? '✓ 运行中' : '⚠ ' + data.system_status;
                statusBadge.style.color = data.system_status === 'online' ? '#38a169' : '#e53e3e';
            }
            
            // Update workflow progress
            this.updateWorkflowProgress(data);
        } catch (error) {
            console.error('Failed to load dashboard stats:', error);
            DOM.text('#totalConfigs', '?');
            DOM.text('#totalScriptsCount', '?');
            
            const statusBadge = DOM.$('#systemStatusBadge');
            if (statusBadge) {
                statusBadge.textContent = '⚠ 未知';
                statusBadge.style.color = '#e53e3e';
            }
        }
    }

    async loadAchievementsData() {
        try {
            const response = await fetch('/api/achievements/');
            if (!response.ok) throw new Error('Failed to fetch achievements');
            
            const data = await response.json();
            
            // Update data source badge
            const badge = DOM.$('#dataSourceBadge');
            if (badge) {
                badge.textContent = data.data_source === 'mock' ? 'Mock数据' : '真实数据';
                badge.style.background = data.data_source === 'mock' ? 
                    'rgba(255,193,7,0.3)' : 'rgba(52,211,153,0.3)';
            }
            
            // Animate numbers
            Format.animateNumber('totalSessions', 0, data.total_sessions, 1500);
            Format.animateNumber('totalDuration', 0, data.total_duration_hours, 1500, 1);
            Format.animateNumber('totalQuestions', 0, data.total_questions_answered, 1500);
            
            // Update assets
            Format.animateNumber('totalProducts', 0, data.total_products, 1000);
            Format.animateNumber('totalConfigs', 0, data.total_configs, 1000);
            Format.animateNumber('totalScriptsCount', 0, data.total_scripts, 1000);
            
            // Update stats bar
            DOM.text('#avgDuration', 
                data.avg_session_duration_hours ? data.avg_session_duration_hours.toFixed(1) + 'h' : '-');
            DOM.text('#avgQuestions', 
                data.avg_questions_per_session ? Math.round(data.avg_questions_per_session) : '-');
            DOM.text('#systemUptime', 
                data.system_uptime_days ? data.system_uptime_days + '天' : '-');
            
        } catch (error) {
            console.error('Failed to load achievements:', error);
            const badge = DOM.$('#dataSourceBadge');
            if (badge) badge.textContent = '加载失败';
        }
    }

    updateWorkflowProgress(stats) {
        const steps = DOM.$$('.workflow-step');
        const progressBar = DOM.$('#workflowProgress');
        let completedSteps = 0;
        
        // 重置所有步骤状态
        steps.forEach(step => {
            DOM.removeClass(step, 'active');
            DOM.removeClass(step, 'completed');
        });
        
        // Check which steps are completed based on stats
        if (stats && stats.total_forms > 0) {
            // Step 1 & 2 & 3 completed if forms exist
            steps[0] && DOM.addClass(steps[0], 'completed');
            steps[1] && DOM.addClass(steps[1], 'completed');
            steps[2] && DOM.addClass(steps[2], 'completed');
            completedSteps = 3;
            
            if (stats.total_scripts > 0) {
                // Step 4 completed if scripts exist
                steps[3] && DOM.addClass(steps[3], 'completed');
                completedSteps = 4;
                
                // Mark next step as active
                if (steps[4]) DOM.addClass(steps[4], 'active');
            } else {
                // Mark step 4 as active (need to generate script)
                if (steps[3]) DOM.addClass(steps[3], 'active');
            }
        } else {
            // Mark step 1 as active (need to start configuration)
            if (steps[0]) DOM.addClass(steps[0], 'active');
        }
        
        // Update progress bar width
        if (progressBar) {
            const progressPercentage = (completedSteps / steps.length) * 100;
            progressBar.style.width = progressPercentage + '%';
        }
    }

    // ===== 模块导入区域 =====
    // 这些方法将在后续步骤中从独立模块导入
    
    async loadProducts() {
        const grid = DOM.$('#productsGrid');
        try {
            DOM.renderLoading(grid, '加载商品中...');
            
            const response = await fetch('/api/v1/simple-products/');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const products = await response.json();
            this.currentProducts = products;
            this.filteredProducts = products;
            
            this.renderProducts(this.filteredProducts);
        } catch (error) {
            console.error('Failed to load products:', error);
            DOM.renderError(grid, '加载商品失败', error.message);
            
            // 防御性检查：确保UI组件可用
            if (window.UI && window.UI.Toast && typeof window.UI.Toast.error === 'function') {
                UI.Toast.error('加载商品失败: ' + error.message);
            } else {
                console.warn('UI.Toast not available, using alert as fallback');
                alert('加载商品失败: ' + error.message);
            }
        }
    }

    async loadForms() {
        const grid = DOM.$('#formsGrid');
        try {
            DOM.renderLoading(grid, '加载配置中...');
            
            const response = await fetch('/api/console/forms');
            const data = await response.json();
            
            let html = '';
            
            // Add existing forms
            data.forms.forEach(form => {
                const statusClass = form.status === 'completed' ? 'status-completed' : 'status-draft';
                const statusText = form.status === 'completed' ? '已完成' : '草稿';
                
                html += `
                    <div class="info-card">
                        <div class="info-card-header">
                            <div>
                                <h3 class="info-card-title">${form.title}</h3>
                                <div class="info-card-subtitle">ID: ${form.form_id.substring(0, 8)}...</div>
                            </div>
                            <div class="info-card-status ${statusClass === 'status-completed' ? 'success' : 'warning'}">${statusText}</div>
                        </div>
                        
                        <div class="info-card-meta">
                            产品: ${form.product_name}<br>
                            创建时间: ${Format.date(form.created_at)}
                        </div>
                        
                        <div class="info-card-description">
                            完成度: ${form.completion_percentage}%
                        </div>
                        
                        <div class="info-card-stats">
                            <div class="info-card-stat-item">
                                <span class="info-card-stat-value">${form.completion_percentage}%</span>
                                <span class="info-card-stat-label">完成度</span>
                            </div>
                        </div>
                        
                        <div class="info-card-actions">
                            <a href="/config?form_id=${form.form_id}" class="btn btn-primary btn-sm">编辑</a>
                            <button class="btn btn-secondary btn-sm" data-action="generate-script-for-form" data-form-id="${form.form_id}">生成脚本</button>
                            <button class="btn btn-danger btn-sm" data-action="delete-form" data-form-id="${form.form_id}">删除</button>
                        </div>
                    </div>
                `;
            });
            
            // Add new form card
            html += `
                <div class="info-card">
                    <div class="info-card-header">
                        <div>
                            <h3 class="info-card-title">新建配置</h3>
                            <div class="info-card-subtitle">创建直播配置</div>
                        </div>
                        <div class="info-card-status success">可创建</div>
                    </div>
                    
                    <div class="info-card-description">
                        点击创建新的直播配置，设置直播参数和内容
                    </div>
                    
                    <div class="info-card-actions">
                        <a href="/config" class="btn btn-primary btn-sm">+ 创建</a>
                    </div>
                </div>
            `;
            
            grid.innerHTML = html;
        } catch (error) {
            console.error('Failed to load forms:', error);
            DOM.renderError(grid, '加载配置失败', error.message);
        }
    }

    async loadScripts() {
        const grid = DOM.$('#scriptsGrid');
        try {
            DOM.renderLoading(grid, '加载脚本中...');
            
            const response = await fetch('/api/console/scripts');
            const data = await response.json();
            
            if (!data.scripts || data.scripts.length === 0) {
                DOM.renderEmpty(grid, '暂无脚本', 
                    '<button class="btn btn-primary" data-action="show-generate-script-modal">生成新脚本</button>');
                return;
            }
            
            let html = '';
            
            // Add existing scripts
            data.scripts.forEach(script => {
                const warnings = script.warnings && script.warnings.length > 0 
                    ? `<br>⚠️ ${script.warnings.length} 个警告` 
                    : '';
                
                html += `
                    <div class="info-card">
                        <div class="info-card-header">
                            <div>
                                <h3 class="info-card-title">${script.form_title}</h3>
                                <div class="info-card-subtitle">ID: ${script.form_id.substring(0, 8)}...</div>
                            </div>
                            <div class="info-card-status ${warnings ? 'warning' : 'success'}">${warnings ? '有警告' : '正常'}</div>
                        </div>
                        
                        <div class="info-card-meta">
                            段落数: ${script.total_segments}<br>
                            单词数: ${script.estimated_words}
                        </div>
                        
                        <div class="info-card-description">
                            ${script.preview_content}
                        </div>
                        
                        <div class="info-card-stats">
                            <div class="info-card-stat-item">
                                <span class="info-card-stat-value">${script.total_duration_minutes}</span>
                                <span class="info-card-stat-label">分钟</span>
                            </div>
                            <div class="info-card-stat-item">
                                <span class="info-card-stat-value">${script.total_segments}</span>
                                <span class="info-card-stat-label">段落</span>
                            </div>
                        </div>
                        
                        <div class="info-card-actions">
                            <a href="/api/script-preview/view/${script.form_id}" target="_blank" class="btn btn-primary btn-sm">查看完整</a>
                            <button class="btn btn-success btn-sm" data-action="launch-stream-from-script" data-form-id="${script.form_id}" data-form-title="${script.form_title}">🚀 启动直播</button>
                            <button class="btn btn-danger btn-sm" data-action="delete-script" data-form-id="${script.form_id}">删除</button>
                        </div>
                    </div>
                `;
            });
            
            grid.innerHTML = html;
        } catch (error) {
            console.error('Failed to load scripts:', error);
            DOM.renderError(grid, '加载脚本失败', error.message);
        }
    }

    // 其他方法的临时占位符...
    openCreateProductModal() {
        // 防御性检查：确保UI组件可用
        if (!window.UI || !window.UI.Modal || typeof window.UI.Modal.show !== 'function') {
            console.error('UI.Modal not available');
            alert('UI组件未加载，请刷新页面重试');
            return;
        }
        
        UI.Modal.show('新建商品', `
            <form id="productForm" onsubmit="window.AdminConsole.handleSubmitProduct(event)">
                <div class="form-group">
                    <label class="form-label">商品SKU *</label>
                    <input type="text" id="modalProductSku" class="form-input" required 
                           placeholder="例: PROD-001" maxlength="50">
                </div>
                
                <div class="form-group">
                    <label class="form-label">商品名称 *</label>
                    <input type="text" id="modalProductName" class="form-input" required 
                           placeholder="输入商品名称" maxlength="200">
                </div>
                
                <div class="form-group">
                    <label class="form-label">商品分类 *</label>
                    <select id="modalProductCategory" class="form-select" required>
                        <option value="">请选择分类</option>
                        <option value="electronics">电子产品</option>
                        <option value="clothing">服装</option>
                        <option value="home">家居用品</option>
                        <option value="beauty">美妆护肤</option>
                        <option value="food">食品饮料</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">商品价格</label>
                    <input type="number" id="modalProductPrice" class="form-input" 
                           placeholder="0.00" min="0" step="0.01">
                </div>
                
                <div class="form-group">
                    <label class="form-label">库存数量</label>
                    <input type="number" id="modalProductStock" class="form-input" 
                           placeholder="0" min="0" step="1">
                </div>
                
                <div class="form-group">
                    <label class="form-label">商品描述</label>
                    <textarea id="modalProductDescription" class="form-textarea" 
                              placeholder="输入商品详细描述..." maxlength="1000" style="min-height: 100px;"></textarea>
                </div>
                
                <div style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="UI.Modal.hide()">取消</button>
                    <button type="submit" class="btn btn-primary">创建商品</button>
                </div>
            </form>
        `);
    }

    async handleSubmitProduct(event) {
        event.preventDefault();
        
        const formData = {
            sku: document.getElementById('modalProductSku').value,
            name: document.getElementById('modalProductName').value,
            category: document.getElementById('modalProductCategory').value,
            price: parseFloat(document.getElementById('modalProductPrice').value) || null,
            stock: parseInt(document.getElementById('modalProductStock').value) || null,
            description: document.getElementById('modalProductDescription').value
        };
        
        try {
            const response = await fetch('/api/v1/simple-products/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.detail || '创建失败');
            }
            
            UI.Modal.hide();
            UI.Toast.success('商品创建成功！');
            this.loadProducts();
        } catch (error) {
            UI.Toast.error('创建失败: ' + error.message);
        }
    }

    async handleUpdateProduct(event, productId) {
        event.preventDefault();
        
        const formData = {
            sku: document.getElementById('modalProductSku').value,
            name: document.getElementById('modalProductName').value,
            category: document.getElementById('modalProductCategory').value,
            price: parseFloat(document.getElementById('modalProductPrice').value) || null,
            stock: parseInt(document.getElementById('modalProductStock').value) || null,
            description: document.getElementById('modalProductDescription').value
        };
        
        try {
            const response = await fetch(`/api/v1/simple-products/${productId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.detail || '更新失败');
            }
            
            UI.Modal.hide();
            UI.Toast.success('商品更新成功！');
            this.loadProducts();
        } catch (error) {
            UI.Toast.error('更新失败: ' + error.message);
        }
    }

    showGenerateScriptModal() {
        // 显示提示弹层：需要从直播配置中生成脚本
        UI.Modal.show('🤖 生成新脚本', `
            <div style="text-align: center; padding: 20px;">
                <p style="font-size: 48px; margin-bottom: 20px;">⚙️</p>
                <p style="color: #666; margin-bottom: 20px; font-size: 16px;">
                    脚本生成需要从直播配置中获取信息
                </p>
                <p style="color: #666; margin-bottom: 30px;">
                    您将跳转到配置管理页面来选择或创建配置
                </p>
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button class="btn btn-secondary" onclick="UI.Modal.hide()">取消</button>
                    <button class="btn btn-primary" onclick="window.AdminConsole.redirectToConfig()">前往配置管理</button>
                </div>
            </div>
        `);
    }

    redirectToConfig() {
        UI.Modal.hide();
        window.location.href = '/config';
    }

    manageProduct(productId) {
        // 跳转到商品管理页面
        window.open(`/product_detail.html?id=${productId}`, '_blank');
    }
    
    // 暂时保留编辑模态框函数，以备将来需要
    async openEditProductModal(productId) {
        try {
            const response = await fetch(`/api/v1/simple-products/${productId}`);
            if (!response.ok) throw new Error('获取商品信息失败');
            
            const product = await response.json();
            
            UI.Modal.show('编辑商品', `
                <form id="productForm" onsubmit="window.AdminConsole.handleUpdateProduct(event, ${productId})">
                    <div class="form-group">
                        <label class="form-label">商品SKU *</label>
                        <input type="text" id="modalProductSku" class="form-input" required 
                               value="${product.sku}" maxlength="50">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">商品名称 *</label>
                        <input type="text" id="modalProductName" class="form-input" required 
                               value="${product.name}" maxlength="200">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">商品分类 *</label>
                        <select id="modalProductCategory" class="form-select" required>
                            <option value="">请选择分类</option>
                            <option value="electronics" ${product.category === 'electronics' ? 'selected' : ''}>电子产品</option>
                            <option value="clothing" ${product.category === 'clothing' ? 'selected' : ''}>服装</option>
                            <option value="home" ${product.category === 'home' ? 'selected' : ''}>家居用品</option>
                            <option value="beauty" ${product.category === 'beauty' ? 'selected' : ''}>美妆护肤</option>
                            <option value="food" ${product.category === 'food' ? 'selected' : ''}>食品饮料</option>
                            <option value="other" ${product.category === 'other' ? 'selected' : ''}>其他</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">商品价格</label>
                        <input type="number" id="modalProductPrice" class="form-input" 
                               value="${product.price || ''}" min="0" step="0.01">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">库存数量</label>
                        <input type="number" id="modalProductStock" class="form-input" 
                               value="${product.stock || ''}" min="0" step="1">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">商品描述</label>
                        <textarea id="modalProductDescription" class="form-textarea" 
                                  maxlength="1000" style="min-height: 100px;">${product.description || ''}</textarea>
                    </div>
                    
                    <div style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;">
                        <button type="button" class="btn btn-secondary" onclick="UI.Modal.hide()">取消</button>
                        <button type="submit" class="btn btn-primary">保存更改</button>
                    </div>
                </form>
            `);
        } catch (error) {
            UI.Toast.error('加载商品失败: ' + error.message);
        }
    }
    async deleteProduct(productId) {
        UI.Modal.confirm('确定要删除这个商品吗？此操作不可撤销。', async () => {
            try {
                const response = await fetch(`/api/v1/simple-products/${productId}`, {
                    method: 'DELETE'
                });
                
                if (!response.ok) {
                    throw new Error('删除失败');
                }
                
                UI.Toast.success('商品已删除');
                this.loadProducts();
            } catch (error) {
                UI.Toast.error('删除失败: ' + error.message);
            }
        });
    }
    manageProductQA(productId) {
        // 直接跳转到商品详情页面的QA管理区域
        window.open(`/product_detail.html?id=${productId}#qa-section`, '_blank');
    }
    async launchStreamFromScript(formId, title) {
        try {
            // 先检查是否有活跃的直播
            const statusResponse = await fetch('/api/control/status');
            const statusData = await statusResponse.json();
            
            if (statusData.stream_status && statusData.stream_status !== 'stopped') {
                const statusText = Format.statusLabel(statusData.stream_status, 'stream');
                if (!confirm(`当前有活跃的直播 (${statusText})\n\n是否要停止当前直播并启动新的直播？`)) {
                    return;
                }
                
                await fetch('/api/control/sessions/main/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ reason: '启动新直播' })
                });
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // 显示初始化进度
            StreamInitManager.show();
            StreamInitManager.startSimulatedProgress();
            
            // 启动直播
            const response = await fetch('/api/script-preview/prepare-stream', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    form_id: formId,
                    persona: 'default',
                    auto_start: false,
                    stream_title: title
                })
            });
            
            const data = await response.json();
            
            // 成功 - 显示成功对话框
            StreamInitManager.showSuccess(
                `🎉 直播初始化成功！`,
                `标题: ${data.stream_title}\n段落数: ${data.total_segments}\n预计时长: ${data.estimated_duration_minutes} 分钟\n\n⚠️ 直播已准备就绪，请在控制面板中手动点击"开始直播"按钮开始播放`,
                () => {
                    window.open('/control', '_blank');
                }
            );
            
            // 更新仪表板统计
            await this.loadDashboardStats();
            
        } catch (error) {
            console.error('Launch stream failed:', error);
            StreamInitManager.showError(`启动失败: ${error.message}`);
        }
    }
    async deleteScript(formId) {
        if (!confirm('确定要删除这个脚本吗？此操作不可撤销。')) {
            return;
        }
        
        try {
            await fetch(`/api/console/scripts/${formId}`, {
                method: 'DELETE'
            });
            UI.Toast.success('脚本已删除');
            this.loadScripts();
            this.loadDashboardStats();
        } catch (error) {
            console.error('Delete script failed:', error);
            UI.Toast.error('删除失败: ' + error.message);
        }
    }
    
    async generateScriptForForm(formId) {
        // 显示进度模态框
        ScriptProgressManager.show();
        
        // 创建AbortController用于取消请求
        const abortController = new AbortController();
        ScriptProgressManager.setAbortController(abortController);
        
        // 开始模拟进度
        ScriptProgressManager.startSimulatedProgress();
        
        try {
            const response = await fetch('/api/script-preview/generate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ form_id: formId, regenerate: true }),
                signal: abortController.signal
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || '脚本生成失败');
            }
            
            // 成功：跳转到100%并显示成功状态
            ScriptProgressManager.showSuccess('脚本生成成功！');
            
            // 更新页面数据
            this.loadScripts();
            this.loadDashboardStats();
            
            // 延迟打开脚本预览（等模态框关闭后）
            setTimeout(() => {
                window.open(`/api/script-preview/view/${formId}`, '_blank');
            }, 2500);
            
        } catch (error) {
            if (error.name === 'AbortError') {
                // 用户取消了生成
                ScriptProgressManager.hide();
            } else {
                ScriptProgressManager.showError('生成失败: ' + error.message);
            }
        }
    }
    async deleteForm(formId) {
        if (!confirm('确定要删除这个配置吗？此操作不可撤销。')) {
            return;
        }
        
        try {
            await fetch(`/api/console/forms/${formId}`, {
                method: 'DELETE'
            });
            UI.Toast.success('配置已删除');
            this.loadForms();
            this.loadDashboardStats();
        } catch (error) {
            console.error('Delete form failed:', error);
            UI.Toast.error('删除失败: ' + error.message);
        }
    }
    
    // 防抖搜索
    debouncedProductSearch = DOM.debounce(() => {
        const searchTerm = DOM.$('#productSearchInput').value.toLowerCase();
        const category = DOM.$('#productCategoryFilter').value;
        
        this.filteredProducts = this.currentProducts.filter(product => {
            const matchesSearch = !searchTerm || 
                product.name.toLowerCase().includes(searchTerm) ||
                product.sku.toLowerCase().includes(searchTerm) ||
                (product.description && product.description.toLowerCase().includes(searchTerm));
            
            const matchesCategory = !category || product.category === category;
            
            return matchesSearch && matchesCategory;
        });
        
        this.renderProducts(this.filteredProducts);
    }, 300);

    handleProductFilter() {
        this.debouncedProductSearch();
    }

    renderProducts(products) {
        const grid = DOM.$('#productsGrid');
        
        if (!products || products.length === 0) {
            DOM.renderEmpty(grid, '暂无商品', 
                '<button class="btn btn-primary" data-action="open-create-product-modal">新建商品</button>');
            return;
        }
        
        const categoryLabels = {
            'electronics': '电子产品',
            'clothing': '服装',
            'home': '家居用品',
            'beauty': '美妆护肤',
            'food': '食品饮料',
            'other': '其他'
        };
        
        let html = '';
        products.forEach(product => {
            const status = product.stock > 0 ? 'success' : 'warning';
            const statusText = product.stock > 0 ? '有库存' : '缺货';
            
            html += `
                <div class="info-card">
                    <div class="info-card-header">
                        <div>
                            <h3 class="info-card-title">${product.name}</h3>
                            <div class="info-card-subtitle">${product.sku}</div>
                        </div>
                        <div class="info-card-status ${status}">${statusText}</div>
                    </div>
                    
                    <div class="info-card-meta">
                        分类: ${categoryLabels[product.category] || product.category}<br>
                        创建时间: ${new Date(product.created_at || Date.now()).toLocaleDateString()}
                    </div>
                    
                    <div class="info-card-description">
                        ${product.description || '暂无描述'}
                    </div>
                    
                    <div class="info-card-stats">
                        <div class="info-card-stat-item">
                            <span class="info-card-stat-value">¥${product.price || '0'}</span>
                            <span class="info-card-stat-label">价格</span>
                        </div>
                        <div class="info-card-stat-item">
                            <span class="info-card-stat-value">${product.stock || '0'}</span>
                            <span class="info-card-stat-label">库存</span>
                        </div>
                    </div>
                    
                    <div class="info-card-actions">
                        <button class="btn btn-primary btn-sm" data-action="manage-product" data-product-id="${product.id}">管理</button>
                        <button class="btn btn-secondary btn-sm" data-action="manage-product-qa" data-product-id="${product.id}">管理QA</button>
                        <button class="btn btn-danger btn-sm" data-action="delete-product" data-product-id="${product.id}">删除</button>
                    </div>
                </div>
            `;
        });
        
        grid.innerHTML = html;
    }
}

// 创建全局实例
window.AdminConsole = new AdminConsole();

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    window.AdminConsole.init();
});