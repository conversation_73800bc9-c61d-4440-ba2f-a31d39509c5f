"""Operational forms API endpoints

Provides REST endpoints for the 6-section operational input form system
including form creation, editing, validation, and session management.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from uuid import uuid4
import json
from fastapi import APIRouter, HTTPException, Depends, status, BackgroundTasks
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
from loguru import logger

from ..models.forms import (
    OperationalForm, FormSession, BasicInformation, ProductInformation,
    SellingPointsStructure, PersonaConfiguration, AdvancedSettings,
    ReviewAndValidation, SellingPoint, ValidationRule, ValidationStatus,
    PriorityLevel
)
from ..core.exceptions import ValidationError
from ..models.persona import PersonaManager, PersonaConfig, PersonaTemplate, PersonaType, VoiceStyle
from ..services.form_processor import FormProcessor, ValidationContext
from ..core.exceptions import ServiceError, ValidationError
from ..core.config import cfg


# Request/Response models
class FormCreateRequest(BaseModel):
    """Request to create new operational form"""
    created_by: str
    template_id: Optional[str] = None


class StandardErrorResponse(BaseModel):
    """Standardized error response structure"""
    error: str
    message: str
    details: Optional[str] = None
    error_code: Optional[str] = None
    timestamp: str
    request_id: Optional[str] = None


def create_error_response(
    error_type: str,
    message: str,
    details: Optional[str] = None,
    error_code: Optional[str] = None,
    request_id: Optional[str] = None,
    field_errors: Optional[List[Dict[str, Any]]] = None
) -> dict:
    """Create standardized error response

    Args:
        error_type: Type of error (validation, database, processing, etc.)
        message: Human-readable error message
        details: Additional error details
        error_code: Application-specific error code
        request_id: Request identifier for tracing
        field_errors: List of field-specific validation errors

    Returns:
        Standardized error response dictionary
    """
    response = {
        "error": error_type,
        "message": message,
        "details": details,
        "error_code": error_code,
        "timestamp": datetime.utcnow().isoformat(),
        "request_id": request_id
    }

    if field_errors:
        response["field_errors"] = field_errors

    return response


class FormUpdateRequest(BaseModel):
    """Request to update form section"""
    section_number: int
    section_data: Dict[str, Any]
    product_id: Optional[int] = None  # 添加产品ID字段


class ValidationRequest(BaseModel):
    """Request to validate form section or complete form"""
    validation_type: str  # "section" or "complete"
    section_number: Optional[int] = None


class SessionRequest(BaseModel):
    """Request to create or extend session"""
    user_id: str
    form_id: str


# Router setup
operational_router = APIRouter(prefix="/api/operational", tags=["operational"])

# Initialize persistence layer
from ..services.persistence import DatabaseManager, FormStorage, SessionStorage

# Initialize database and storage services
db_manager = DatabaseManager(cfg.database_path)
form_storage = FormStorage(db_manager)
session_storage = SessionStorage(db_manager)

# Ensure database schema exists (verification call)
try:
    db_manager.initialize_schema()  # Idempotent operation, safe to call multiple times
    logger.debug("Database schema verification completed")
except Exception as e:
    logger.error(f"Database schema verification failed: {e}")
    raise

# Initialize persona manager with default personas
def create_default_personas() -> PersonaManager:
    """Create PersonaManager with default personas"""
    manager = PersonaManager()
    
    # 活力主播
    energetic_template = PersonaTemplate(
        name="活力主播",
        description="热情洋溢、充满活力的主播风格，适合时尚数码、运动产品",
        greeting_template="大家好！欢迎来到我的直播间！今天给大家带来超棒的产品！",
        narration_template="这款产品真的太棒了！{product_feature}，相信大家一定会喜欢的！",
        qa_template="这个问题问得很好！{answer}，希望能帮到大家！",
        transition_template="接下来，我们来看看{next_topic}，这个真的很重要！",
        closing_template="感谢大家今天的观看，记得关注我们哦！",
        tone_keywords=["热情", "活力", "兴奋", "积极"],
        speaking_patterns=["真的太棒了！", "相信大家", "一定会喜欢", "超级推荐"],
        max_sentence_length=20
    )
    
    energetic_persona = PersonaConfig(
        persona_id="energetic_host",
        name="活力主播",
        description="热情洋溢、充满活力，适合时尚数码产品",
        persona_type=PersonaType.TEMPLATE_BASED,
        template_config=energetic_template,
        voice_style=VoiceStyle.ENERGETIC,
        tts_voice_name="zh-CN-XiaoyiNeural",
        speaking_rate=1.1
    )
    
    # 专业顾问
    professional_template = PersonaTemplate(
        name="专业顾问",
        description="理性专业、逻辑清晰的顾问风格，适合高端产品",
        greeting_template="欢迎大家，今天我将为大家详细介绍这款优质产品。",
        narration_template="从技术角度来看，{product_feature}具有显著优势。",
        qa_template="关于这个问题，我来为大家详细解答：{answer}",
        transition_template="现在让我们转到{next_topic}这个重要方面。",
        closing_template="以上就是今天的详细介绍，感谢大家的关注。",
        tone_keywords=["专业", "理性", "详细", "权威"],
        speaking_patterns=["从技术角度", "具有优势", "值得注意的是", "综合来看"],
        max_sentence_length=25
    )
    
    professional_persona = PersonaConfig(
        persona_id="professional_advisor",
        name="专业顾问", 
        description="理性专业、逻辑清晰，适合高端产品和B2B销售",
        persona_type=PersonaType.TEMPLATE_BASED,
        template_config=professional_template,
        voice_style=VoiceStyle.PROFESSIONAL,
        tts_voice_name="zh-CN-XiaoxiaoNeural",
        speaking_rate=0.9
    )
    
    # 邻家姐姐
    warm_template = PersonaTemplate(
        name="邻家姐姐",
        description="亲切温和、贴心细致的邻家风格，适合生活用品",
        greeting_template="大家好呀，我是你们的小姐姐，今天要给大家推荐一个好东西！",
        narration_template="这款{product_name}真的很贴心，{product_feature}，特别适合我们日常使用。",
        qa_template="姐妹问得很实用呢！{answer}，这个真的要注意哦。",
        transition_template="说到{next_topic}，这个也是很多姐妹关心的问题。",
        closing_template="今天的分享就到这里啦，大家有问题随时问我哦！",
        tone_keywords=["亲切", "贴心", "温和", "实用"],
        speaking_patterns=["姐妹们", "真的很贴心", "特别适合", "要注意哦"],
        max_sentence_length=18
    )
    
    warm_persona = PersonaConfig(
        persona_id="warm_sister",
        name="邻家姐姐",
        description="亲切温和、贴心细致，适合生活用品和母婴产品",
        persona_type=PersonaType.TEMPLATE_BASED,
        template_config=warm_template,
        voice_style=VoiceStyle.WARM,
        tts_voice_name="zh-CN-XiaohanNeural",
        speaking_rate=1.0
    )
    
    # 潮流达人
    trendy_template = PersonaTemplate(
        name="潮流达人",
        description="时尚前卫、活力四射的潮流风格，适合美妆时尚",
        greeting_template="宝贝们！今天又来给大家种草啦！这次的好物绝对不能错过！",
        narration_template="OMG！{product_name}简直是{product_feature}的天花板！必须拥有！",
        qa_template="宝贝这个问题太关键了！{answer}，相信我的眼光！",
        transition_template="接下来的{next_topic}更是重点中的重点！",
        closing_template="今天的分享就到这里，记得三连支持！爱你们！",
        tone_keywords=["时尚", "潮流", "前卫", "活力"],
        speaking_patterns=["宝贝们", "OMG", "绝对", "必须拥有", "相信我"],
        max_sentence_length=15
    )
    
    trendy_persona = PersonaConfig(
        persona_id="trendy_influencer",
        name="潮流达人",
        description="时尚前卫、活力四射，适合美妆时尚和潮流单品",
        persona_type=PersonaType.TEMPLATE_BASED,
        template_config=trendy_template,
        voice_style=VoiceStyle.ENERGETIC,
        tts_voice_name="zh-CN-XiaoyouNeural",
        speaking_rate=1.2
    )
    
    # 添加所有personas到manager
    manager.add_persona(energetic_persona)
    manager.add_persona(professional_persona)
    manager.add_persona(warm_persona)
    manager.add_persona(trendy_persona)
    
    # 设置默认persona
    manager.default_persona_id = "energetic_host"
    
    return manager

persona_manager = create_default_personas()
form_processor = FormProcessor()


# Dependency functions
async def get_form(form_id: str) -> OperationalForm:
    """Get form by ID"""
    try:
        form = form_storage.get_form(form_id)
        if not form:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Form not found: {form_id}"
            )
        return form
    except ServiceError as e:
        logger.error(f"Failed to get form {form_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(e)}"
        )


async def get_session(session_id: str) -> FormSession:
    """Get session by ID"""
    try:
        session = session_storage.get_session(session_id)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Session not found or expired: {session_id}"
            )
        
        # Mark activity automatically
        session_storage.mark_session_activity(session_id)
        return session
    except ServiceError as e:
        logger.error(f"Failed to get session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(e)}"
        )


# Form management endpoints
@operational_router.post("/forms", response_model=Dict[str, Any])
async def create_form(request: FormCreateRequest) -> Dict[str, Any]:
    """Create new operational form"""
    try:
        form_id = str(uuid4())
        
        # Create form with default values
        form = OperationalForm(
            form_id=form_id,
            created_by=request.created_by,
            basic_information=BasicInformation(
                stream_title="待填写直播标题请修改为至少10个字符",
                stream_type="daily_stream"
            ),
            product_information=ProductInformation(
                primary_sku="TBD",
                product_name="待填写商品名称",
                brand="待确认",
                category="待分类",
                current_price=1,
                key_specifications=["待添加规格"]
            ),
            selling_points_structure=SellingPointsStructure(
                primary_value_proposition="我们的产品提供卓越的品质和性价比，满足客户的核心需求并带来超值体验",
                selling_points=[
                    SellingPoint(
                        point_id="default_1",
                        title="高品质保障服务",
                        description="采用优质材料和先进工艺，确保产品质量达到行业领先水平",
                        priority=PriorityLevel.HIGH,
                        supporting_facts=["通过ISO质量认证", "用户满意度超过95%"]
                    ),
                    SellingPoint(
                        point_id="default_2", 
                        title="超值性价比优势",
                        description="相比同类产品提供更优的价格和更全面的功能组合",
                        priority=PriorityLevel.MEDIUM,
                        supporting_facts=["价格比竞品低20%", "功能更加完善"]
                    ),
                    SellingPoint(
                        point_id="default_3",
                        title="贴心售后服务",
                        description="提供全方位的售前售后服务支持，让客户购买无忧",
                        priority=PriorityLevel.MEDIUM,
                        supporting_facts=["7*24小时客服", "30天无理由退换"]
                    )
                ],
                competitive_advantages=["行业领先技术"],
                call_to_actions=["立即下单", "限时优惠"]
            ),
            persona_configuration=PersonaConfiguration(
                selected_persona_id="default",
                persona_name="Default Persona"
            ),
            advanced_settings=AdvancedSettings(),
            review_and_validation=ReviewAndValidation()
        )
        
        # Apply template if specified
        if request.template_id:
            form = await apply_form_template(form, request.template_id)
        
        # Initialize validation rules
        form.review_and_validation = await initialize_validation_rules(form)
        
        # Store form in database
        form_storage.create_form(form)
        
        # Initialize empty cache state for new form
        await invalidate_form_caches(form_id)
        
        logger.info(f"Created new operational form: {form_id}")
        
        return {
            "form_id": form_id,
            "status": "created",
            "created_at": form.created_at.isoformat(),
            "completion_percentage": form.calculate_completion_percentage()
        }
        
    except Exception as e:
        logger.error(f"Failed to create form: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create form: {str(e)}"
        )


@operational_router.get("/forms/{form_id}", response_model=OperationalForm)
async def get_form_details(form: OperationalForm = Depends(get_form)) -> OperationalForm:
    """Get complete form details"""
    # Update completion percentage
    form.calculate_completion_percentage()
    return form

@operational_router.get("/forms/{form_id}/validation", response_model=Dict[str, Any])
async def get_form_validation_status(form: OperationalForm = Depends(get_form)) -> Dict[str, Any]:
    """Get form validation status for script generation readiness"""
    try:
        # Import validation function
        from .script_preview import validate_form_for_script_generation
        validation_result = validate_form_for_script_generation(form)
        
        return {
            "form_id": form.form_id,
            "is_ready_for_script": validation_result["is_valid"],
            "completion_percentage": validation_result["completion_percentage"],
            "data_quality_score": validation_result["data_quality_score"],
            "has_template_data": form.has_template_data(),
            "template_fields": form.get_template_data_fields(),
            "validation_errors": validation_result["errors"],
            "suggestions": validation_result["suggestions"]
        }
        
    except Exception as e:
        logger.error(f"Failed to get form validation status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve form validation status"
        )


@operational_router.put("/forms/{form_id}/section/{section_number}")
async def update_form_section(
    section_number: int,
    section_data: Dict[str, Any],
    form: OperationalForm = Depends(get_form)
) -> Dict[str, Any]:
    """Update specific form section"""
    try:
        if section_number < 1 or section_number > 6:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response(
                    error_type="validation",
                    message="Invalid section number",
                    details="Section number must be between 1 and 6",
                    error_code="INVALID_SECTION_NUMBER"
                )
            )
        
        # Update the appropriate section
        if section_number == 1:
            form.basic_information = BasicInformation(**section_data)
        elif section_number == 2:
            # 处理商品信息更新，支持商品选择器数据
            processed_section_data = await process_product_information_update(section_data)
            form.product_information = ProductInformation(**processed_section_data)
        elif section_number == 3:
            form.selling_points_structure = SellingPointsStructure(**section_data)
        elif section_number == 4:
            form.persona_configuration = PersonaConfiguration(**section_data)
        elif section_number == 5:
            form.advanced_settings = AdvancedSettings(**section_data)
        elif section_number == 6:
            form.review_and_validation = ReviewAndValidation(**section_data)
        
        # Update metadata
        form.current_section = section_number
        form.update_modification_time()
        
        # Recalculate completion
        completion_percentage = form.calculate_completion_percentage()
        
        # Save updated form to database
        form_storage.update_form(form)
        
        # Synchronize session state with form state for all active sessions
        try:
            active_sessions = session_storage.list_sessions_for_form(form.form_id)
            for session_info in active_sessions:
                session = session_storage.get_session(session_info.get('session_id'))
                if session and not session.is_expired():
                    # Update session's current section to match form
                    session.current_section = section_number
                    session.mark_activity()  # Update last activity timestamp
                    session_storage.update_session(session)
                    logger.debug(f"Synchronized session {session.session_id} with form {form.form_id}")
        except Exception as e:
            # Don't fail the form update if session sync fails, just log
            logger.warning(f"Failed to synchronize sessions for form {form.form_id}: {e}")
        
        # Comprehensive cache invalidation since form data changed
        await invalidate_form_caches(form.form_id, section_number)
        
        logger.info(f"Updated form {form.form_id} section {section_number}")
        
        return {
            "status": "updated",
            "section_number": section_number,
            "completion_percentage": completion_percentage,
            "last_modified_at": form.last_modified_at.isoformat()
        }
        
    except ValidationError as ve:
        # Handle our custom ValidationError with structured field errors
        field_errors = None
        if ve.context and 'parsed_errors' in ve.context:
            parsed_errors = ve.context['parsed_errors']
            field_errors = parsed_errors.get('field_errors', [])

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                error_type="validation",
                message="数据验证失败",
                details=str(ve),
                error_code="VALIDATION_FAILED",
                field_errors=field_errors
            )
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(
                error_type="validation",
                message="Invalid section data",
                details=str(e),
                error_code="INVALID_SECTION_DATA"
            )
        )
    except Exception as e:
        logger.error(f"Failed to update form section: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response(
                error_type="database",
                message="Failed to update section",
                details=str(e),
                error_code="SECTION_UPDATE_FAILED"
            )
        )


@operational_router.post("/forms/{form_id}/validate")
async def validate_form(
    request: ValidationRequest,
    form: OperationalForm = Depends(get_form)
) -> Dict[str, Any]:
    """Validate form section or complete form (validation only, no processing)"""
    try:
        if request.validation_type == "section" and request.section_number:
            # Validate specific section using enhanced processor
            result = await form_processor.validate_section(form, request.section_number)
            
            return {
                "is_valid": result.validation_passed,
                "validation_errors": result.error_messages,
                "processing_time_seconds": result.processing_time_seconds,
                "validated_at": datetime.utcnow().isoformat()
            }
            
        elif request.validation_type == "complete":
            # PURE VALIDATION ONLY - do not process content/persona
            validation_context = ValidationContext(
                form_id=form.form_id,
                section_being_validated=0,  # Complete form
                user_id=form.created_by,
                submission_timestamp=datetime.utcnow(),
                previous_validations=[]
            )
            
            start_time = datetime.utcnow()
            result = await form_processor._comprehensive_validation(form, validation_context)
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                "is_valid": result.validation_passed,
                "validation_errors": result.error_messages,
                "content_generated": False,  # No content generation in validation-only mode
                "persona_configured": False,  # No persona config in validation-only mode
                "processing_time_seconds": processing_time,
                "content_preview": None,  # No preview in validation-only mode
                "validated_at": datetime.utcnow().isoformat(),
                "validation_mode": "validation_only"  # Indicate this is pure validation
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid validation request"
            )
        
    except Exception as e:
        logger.error(f"Form validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Validation failed: {str(e)}"
        )


@operational_router.post("/forms/{form_id}/process")
async def process_form(
    form: OperationalForm = Depends(get_form)
) -> Dict[str, Any]:
    """Process form with validation, content generation, and persona configuration"""
    try:
        # Full processing including validation, content generation, and persona config
        processing_result = await form_processor.process_form_submission(form)
        
        return {
            "status": "processed" if processing_result.success else "failed",
            "form_id": form.form_id,
            "processed_at": datetime.utcnow().isoformat(),
            "processing_time_seconds": processing_result.processing_time_seconds,
            "validation_passed": processing_result.validation_passed,
            "content_generated": processing_result.content_generated,
            "persona_configured": processing_result.persona_configured,
            "content_preview": processing_result.generated_content_preview,
            "error_messages": processing_result.error_messages,
            "message": "Form processed successfully" if processing_result.success else "Form processing failed"
        }
        
    except Exception as e:
        logger.error(f"Form processing failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Processing failed: {str(e)}"
        )


@operational_router.post("/forms/{form_id}/submit")
async def submit_form(
    form: OperationalForm = Depends(get_form),
    background_tasks: BackgroundTasks = BackgroundTasks()
) -> Dict[str, Any]:
    """Submit completed form for enhanced processing"""
    try:
        # Enhanced validation and processing
        processing_result = await form_processor.process_form_submission(form)
        
        if not processing_result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Form processing failed",
                    "errors": processing_result.error_messages,
                    "validation_passed": processing_result.validation_passed,
                    "content_generated": processing_result.content_generated,
                    "persona_configured": processing_result.persona_configured
                }
            )
        
        # Mark as submitted and processed
        form.is_submitted = True
        form.is_processed = True
        form.processed_at = datetime.utcnow()
        form.processing_status = "completed"
        form.update_modification_time()
        
        # Save updated form to database
        form_storage.update_form(form)
        
        logger.info(f"Form {form.form_id} submitted and processed successfully")
        
        return {
            "status": "processed",
            "form_id": form.form_id,
            "submitted_at": form.last_modified_at.isoformat(),
            "processed_at": form.processed_at.isoformat(),
            "processing_time_seconds": processing_result.processing_time_seconds,
            "validation_passed": processing_result.validation_passed,
            "content_generated": processing_result.content_generated,
            "persona_configured": processing_result.persona_configured,
            "content_preview": processing_result.generated_content_preview,
            "message": "Form processed successfully and ready for streaming"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Enhanced form submission failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Submission failed: {str(e)}"
        )


# Session management endpoints
@operational_router.post("/sessions", response_model=Dict[str, Any])
async def create_session(request: SessionRequest) -> Dict[str, Any]:
    """Create new form editing session"""
    try:
        session_id = str(uuid4())
        
        # Verify form exists
        form = form_storage.get_form(request.form_id)
        if not form:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Form not found: {request.form_id}"
            )
        
        # Create session with 30-minute expiration
        session = FormSession(
            session_id=session_id,
            form_id=request.form_id,
            user_id=request.user_id,
            expires_at=datetime.utcnow() + timedelta(minutes=30)
        )
        
        # Store session in database
        session_storage.create_session(session)
        
        logger.info(f"Created session {session_id} for form {request.form_id}")
        
        return {
            "session_id": session_id,
            "expires_at": session.expires_at.isoformat(),
            "auto_save_enabled": session.auto_save_enabled
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create session: {str(e)}"
        )


@operational_router.post("/sessions/{session_id}/extend")
async def extend_session(
    session: FormSession = Depends(get_session)
) -> Dict[str, Any]:
    """Extend session expiration"""
    try:
        updated_session = session_storage.extend_session(session.session_id, 30)
        if not updated_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        return {
            "session_id": updated_session.session_id,
            "expires_at": updated_session.expires_at.isoformat(),
            "extended_at": datetime.utcnow().isoformat()
        }
    except ServiceError as e:
        logger.error(f"Failed to extend session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(e)}"
        )


@operational_router.get("/sessions/{session_id}/status")
async def get_session_status(
    session: FormSession = Depends(get_session)
) -> Dict[str, Any]:
    """Get session status and form progress"""
    form = await get_form(session.form_id)
    
    return {
        "session_id": session.session_id,
        "form_id": session.form_id,
        "current_section": session.current_section,
        "unsaved_changes": session.unsaved_changes,
        "expires_at": session.expires_at.isoformat(),
        "form_completion_percentage": form.calculate_completion_percentage(),
        "form_is_complete": form.is_complete
    }


# Persona management endpoints
@operational_router.get("/personas")
async def list_available_personas() -> Dict[str, Any]:
    """List available personas for selection"""
    personas = persona_manager.list_available_personas()
    
    return {
        "personas": personas,
        "default_persona_id": persona_manager.default_persona_id
    }


@operational_router.get("/personas/{persona_id}")
async def get_persona_details(persona_id: str) -> Dict[str, Any]:
    """Get detailed persona information"""
    if persona_id not in persona_manager.available_personas:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Persona not found: {persona_id}"
        )
    
    persona = persona_manager.available_personas[persona_id]
    
    return {
        "persona_id": persona.persona_id,
        "name": persona.name,
        "description": persona.description,
        "voice_style": persona.voice_style.value,
        "tts_voice_name": persona.tts_voice_name,
        "speaking_rate": persona.speaking_rate
    }


# Form templates endpoints
@operational_router.get("/templates")
async def list_form_templates() -> Dict[str, Any]:
    """List available form templates"""
    templates = [
        {
            "template_id": "product_launch",
            "name": "Product Launch Stream",
            "description": "Template for new product launch streams"
        },
        {
            "template_id": "daily_stream",
            "name": "Daily Stream",
            "description": "Template for regular daily streams"
        },
        {
            "template_id": "flash_sale",
            "name": "Flash Sale",
            "description": "Template for urgent flash sale streams"
        }
    ]
    
    return {"templates": templates}


# Utility functions
async def process_product_information_update(section_data: Dict[str, Any]) -> Dict[str, Any]:
    """处理商品信息更新，支持产品选择器数据格式
    
    Args:
        section_data: 原始表单数据
        
    Returns:
        处理后的商品信息数据，兼容ProductInformation模型
    """
    try:
        # 检查是否使用了商品选择器
        selected_product_id = section_data.get('selected_product_id')
        
        if selected_product_id:
            # 从数据库获取商品信息
            from ..services.simple_product_service import SimpleProductService
            from ..services.persistence.database_manager import DatabaseManager
            from ..core.config import cfg
            
            db_manager = DatabaseManager(cfg.database_path)
            product_service = SimpleProductService(db_manager)
            
            try:
                product = product_service.get_product(selected_product_id)
                
                # 用产品信息填充section_data
                section_data.update({
                    'selected_product_id': selected_product_id,
                    'primary_sku': product['sku'],
                    'product_name': product['name'],
                    'brand': section_data.get('brand', '未知品牌'),  # 保留用户输入或默认值
                    'category': product['category'],
                    'key_specifications': section_data.get('key_specifications', ['请添加规格信息']),
                })
                
                # 处理价格配置
                use_custom_price = section_data.get('use_custom_price', False)
                custom_streaming_price = section_data.get('custom_streaming_price')
                
                section_data.update({
                    'use_custom_price': use_custom_price,
                    'custom_streaming_price': custom_streaming_price,
                })
                
                if use_custom_price and custom_streaming_price:
                    section_data['current_price'] = custom_streaming_price
                else:
                    section_data['current_price'] = product['price']
                
                logger.info(f"使用商品ID {selected_product_id} 的信息更新表单")
                
            except Exception as e:
                logger.error(f"获取商品信息失败: {e}")
                # 如果获取商品信息失败，使用原始数据但记录错误
                section_data['selected_product_id'] = selected_product_id
        
        # 确保必要字段存在（向后兼容）
        required_fields = {
            'primary_sku': section_data.get('primary_sku', 'TBD'),
            'product_name': section_data.get('product_name', '待填写商品名称'),
            'brand': section_data.get('brand', '待确认'),
            'category': section_data.get('category', '待分类'),
            'current_price': section_data.get('current_price', 1),
            'key_specifications': section_data.get('key_specifications', ['待添加规格'])
        }
        
        section_data.update(required_fields)
        
        return section_data
        
    except Exception as e:
        logger.error(f"处理商品信息更新失败: {e}")
        # 返回原始数据作为退备
        return section_data


async def validate_form_section(form: OperationalForm, section_number: int) -> List[str]:
    """Validate specific form section"""
    errors = []
    
    try:
        if section_number == 1:
            if not form.basic_information.stream_title.strip():
                errors.append("Stream title is required")
        elif section_number == 2:
            # 验证商品信息，支持产品选择器和手动输入
            product_info = form.product_information
            
            # 如果使用了商品选择器，验证selected_product_id
            if hasattr(product_info, 'selected_product_id') and product_info.selected_product_id:
                if product_info.selected_product_id <= 0:
                    errors.append("选中的商品ID无效")
            else:
                # 传统手动输入验证
                if not product_info.primary_sku.strip():
                    errors.append("Primary SKU is required")
            
            if product_info.current_price <= 0:
                errors.append("Product price must be greater than 0")
        elif section_number == 3:
            if not form.selling_points_structure.primary_value_proposition.strip():
                errors.append("Primary value proposition is required")
            if len(form.selling_points_structure.selling_points) < 3:
                errors.append("At least 3 selling points are required")
        elif section_number == 4:
            if not form.persona_configuration.selected_persona_id:
                errors.append("Persona selection is required")
        # Section 5 (advanced settings) is optional
        # Section 6 (review) is handled separately
        
    except Exception as e:
        errors.append(f"Section validation error: {str(e)}")
    
    return errors


async def initialize_validation_rules(form: OperationalForm) -> ReviewAndValidation:
    """Initialize validation rules for form"""
    content_rules = [
        ValidationRule(
            rule_id="content_001",
            rule_name="Stream Title Validation",
            description="Stream title must be descriptive and appropriate",
            is_mandatory=True
        ),
        ValidationRule(
            rule_id="content_002", 
            rule_name="Selling Points Verification",
            description="All selling points must have verified supporting facts",
            is_mandatory=True
        )
    ]
    
    compliance_rules = [
        ValidationRule(
            rule_id="compliance_001",
            rule_name="Advertising Standards",
            description="Content must comply with advertising standards",
            is_mandatory=True
        ),
        ValidationRule(
            rule_id="compliance_002",
            rule_name="Product Claims Verification",
            description="All product claims must be substantiated",
            is_mandatory=True
        )
    ]
    
    technical_rules = [
        ValidationRule(
            rule_id="technical_001",
            rule_name="Persona Configuration",
            description="Persona settings must be technically valid",
            is_mandatory=True
        ),
        ValidationRule(
            rule_id="technical_002",
            rule_name="TTS Settings Validation",
            description="TTS settings must be within valid ranges",
            is_mandatory=False
        )
    ]
    
    return ReviewAndValidation(
        content_validation_rules=content_rules,
        compliance_validation_rules=compliance_rules,
        technical_validation_rules=technical_rules
    )


async def invalidate_form_caches(form_id: str, section_number: Optional[int] = None) -> None:
    """Comprehensive cache invalidation for form-related data
    
    Args:
        form_id: Form ID to invalidate caches for
        section_number: Section number that was updated (optional)
    """
    try:
        # 1. Invalidate script preview cache
        try:
            from .script_preview import preview_cache
            if form_id in preview_cache:
                del preview_cache[form_id]
                logger.info(f"Cleared script preview cache for form {form_id}")
        except ImportError:
            logger.debug("Script preview module not available, skipping script cache invalidation")
        
        # 2. Invalidate form processor cache if it exists
        try:
            if hasattr(form_processor, 'clear_form_cache'):
                form_processor.clear_form_cache(form_id)
                logger.info(f"Cleared form processor cache for form {form_id}")
        except Exception as e:
            logger.warning(f"Failed to clear form processor cache: {e}")
        
        # 3. Trigger validation status re-calculation for affected sections
        if section_number:
            try:
                # Force recalculation of validation status for dependent sections
                dependent_sections = {
                    1: [6],  # Basic info affects review section
                    2: [6],  # Product info affects review section
                    3: [6],  # Selling points affects review section
                    4: [6],  # Persona affects review section
                    5: [6],  # Advanced settings affects review section
                }
                
                if section_number in dependent_sections:
                    logger.debug(f"Section {section_number} update affects sections {dependent_sections[section_number]}")
                    
            except Exception as e:
                logger.warning(f"Failed to handle dependent section invalidation: {e}")
        
        logger.debug(f"Completed cache invalidation for form {form_id}, section {section_number}")
        
    except Exception as e:
        # Don't fail the main operation if cache invalidation fails
        logger.error(f"Cache invalidation failed for form {form_id}: {e}")


async def apply_form_template(form: OperationalForm, template_id: str) -> OperationalForm:
    """Apply template to form"""
    # Template application logic would go here
    # For now, just return the form unchanged
    logger.info(f"Applied template {template_id} to form {form.form_id}")
    return form


async def process_submitted_form(form: OperationalForm) -> None:
    """Process submitted form in background"""
    try:
        logger.info(f"Processing submitted form: {form.form_id}")
        
        # Here you would:
        # 1. Convert form data to system configuration
        # 2. Initialize persona and settings
        # 3. Prepare content for streaming
        # 4. Set up monitoring and alerts
        
        # For now, just log the processing
        logger.info(f"Form {form.form_id} processed successfully")
        
    except Exception as e:
        logger.error(f"Failed to process form {form.form_id}: {str(e)}")


# Processing statistics endpoint
@operational_router.get("/processing/stats")
async def get_processing_stats() -> Dict[str, Any]:
    """Get current form processing statistics"""
    try:
        form_stats = form_storage.get_form_stats()
        session_stats = session_storage.get_session_stats()
        processing_stats = form_processor.get_processing_stats()
        
        return {
            "form_stats": form_stats,
            "session_stats": session_stats,
            "processing_stats": processing_stats,
            "timestamp": datetime.utcnow().isoformat()
        }
    except ServiceError as e:
        logger.error(f"Failed to get processing stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(e)}"
        )


# Admin endpoints for testing
@operational_router.get("/admin/forms")
async def list_all_forms() -> Dict[str, Any]:
    """List all forms (admin endpoint)"""
    try:
        forms_summary = form_storage.list_forms(limit=1000)  # Get up to 1000 forms
        
        # Convert datetime objects to ISO strings for JSON serialization
        for form in forms_summary:
            form['created_at'] = form['created_at'].isoformat()
            form['last_modified_at'] = form['last_modified_at'].isoformat()
        
        return {
            "total_forms": len(forms_summary),
            "forms": forms_summary
        }
    except ServiceError as e:
        logger.error(f"Failed to list forms: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(e)}"
        )


@operational_router.delete("/admin/forms/{form_id}")
async def delete_form(form_id: str) -> Dict[str, Any]:
    """Delete form (admin endpoint)"""
    try:
        # Get sessions for this form before deletion
        sessions = session_storage.list_sessions_for_form(form_id)
        
        # Delete form (cascade will delete sections and sessions)
        deleted = form_storage.delete_form(form_id)
        
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Form not found: {form_id}"
            )
        
        return {
            "status": "deleted",
            "form_id": form_id,
            "sessions_cleaned": len(sessions)
        }
    except ServiceError as e:
        logger.error(f"Failed to delete form {form_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(e)}"
        )