"""
Script Management API Endpoints

REST API for managing persisted streaming scripts, including browsing,
searching, analytics, and lifecycle management operations.
"""

import os
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, status, Query, Path, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel, Field
from loguru import logger
import tempfile
import json
import csv
import io

from ..services.script_persistence_manager import ScriptPersistenceManager
from ..services.script_preview_observer import GlobalScriptObserver
from ..models.script_persistence import (
    ScriptQuery, ScriptAnalytics, GenerationMethod, ScriptStatus,
    ScriptExportFormat, ScriptExportRequest, PersistenceConfig
)
from ..core.exceptions import ServiceError
from ..core.config import cfg


# === Request/Response Models ===

class ScriptSearchRequest(BaseModel):
    """Request for script search with filtering"""
    text_query: Optional[str] = None
    form_id: Optional[str] = None
    generation_method: Optional[GenerationMethod] = None
    status: Optional[ScriptStatus] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    min_duration_seconds: Optional[int] = None
    max_duration_seconds: Optional[int] = None
    min_quality_score: Optional[float] = None
    tags: Optional[List[str]] = None
    created_by: Optional[str] = None
    
    # Pagination
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)
    order_by: str = "generated_at"
    order_desc: bool = True


class ScriptListResponse(BaseModel):
    """Response for script listing"""
    scripts: List[Dict[str, Any]]
    total_count: int
    page: int
    page_size: int
    total_pages: int


class ScriptDetailResponse(BaseModel):
    """Response for script details"""
    script_id: str
    form_id: str
    generated_at: datetime
    generation_method: str
    status: str
    total_duration_seconds: int
    segment_count: int
    quality_score: Optional[float]
    usage_count: int
    last_used_at: Optional[datetime]
    tags: List[str]
    segments: List[Dict[str, Any]]
    estimated_metrics: Dict[str, Any]
    performance_summary: Dict[str, Any]


class UsageTrackingRequest(BaseModel):
    """Request to track script usage"""
    session_id: Optional[str] = None
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    segments_used: int = 0
    segments_skipped: int = 0
    total_viewers: int = 0
    peak_viewers: int = 0
    average_engagement: float = 0.0
    questions_received: int = 0
    questions_answered: int = 0
    interaction_rate: float = 0.0
    stream_title: str = ""
    persona_used: str = ""
    platform_data: Dict[str, Any] = Field(default_factory=dict)


class AnalyticsRequest(BaseModel):
    """Request for analytics data"""
    days: int = Field(default=30, ge=1, le=365)
    include_trends: bool = True
    include_performance: bool = True
    script_ids: Optional[List[str]] = None


class ScriptExportResponse(BaseModel):
    """Response for script export"""
    export_id: str
    format: str
    file_path: Optional[str] = None
    download_url: Optional[str] = None
    created_at: datetime
    expires_at: datetime


# === Router Setup ===

script_management_router = APIRouter(prefix="/api/scripts", tags=["script_management"])

# Global persistence manager (lazy initialization)
_persistence_manager: Optional[ScriptPersistenceManager] = None

def get_persistence_manager() -> ScriptPersistenceManager:
    """Get or create persistence manager instance"""
    global _persistence_manager
    if _persistence_manager is None:
        try:
            config = PersistenceConfig(
                database_path=cfg.get('script_persistence.database_path', 'data/scripts.db'),
                enable_quality_validation=cfg.get('script_persistence.quality_validation', True),
                auto_archive_after_days=cfg.get('script_persistence.archive_after_days', 90)
            )
            _persistence_manager = ScriptPersistenceManager(config)
            logger.info("Script persistence manager initialized for API")
        except Exception as e:
            logger.error(f"Failed to initialize persistence manager: {e}")
            raise ServiceError(f"Persistence service unavailable: {e}")
    
    return _persistence_manager


# === Script Management Endpoints ===

@script_management_router.get("/", response_model=ScriptListResponse)
async def list_scripts(
    text_query: Optional[str] = Query(None, description="Full-text search query"),
    form_id: Optional[str] = Query(None, description="Filter by form ID"),
    generation_method: Optional[GenerationMethod] = Query(None, description="Filter by generation method"),
    status: Optional[ScriptStatus] = Query(None, description="Filter by status"),
    created_after: Optional[datetime] = Query(None, description="Filter by creation date (start)"),
    created_before: Optional[datetime] = Query(None, description="Filter by creation date (end)"),
    min_duration: Optional[int] = Query(None, description="Minimum duration in seconds"),
    max_duration: Optional[int] = Query(None, description="Maximum duration in seconds"),
    min_quality: Optional[float] = Query(None, description="Minimum quality score"),
    tags: Optional[str] = Query(None, description="Comma-separated tags"),
    created_by: Optional[str] = Query(None, description="Filter by creator"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    order_by: str = Query("generated_at", description="Sort field"),
    order_desc: bool = Query(True, description="Sort descending")
) -> ScriptListResponse:
    """List and search scripts with filtering and pagination"""
    try:
        persistence_manager = get_persistence_manager()
        
        # Build query
        script_query = ScriptQuery(
            form_id=form_id,
            generation_method=generation_method,
            status=status,
            created_after=created_after,
            created_before=created_before,
            min_duration_seconds=min_duration,
            max_duration_seconds=max_duration,
            min_quality_score=min_quality,
            tags=tags.split(',') if tags else None,
            created_by=created_by,
            offset=(page - 1) * page_size,
            limit=page_size,
            order_by=order_by,
            order_desc=order_desc
        )
        
        # Search scripts
        scripts, total_count = await persistence_manager.search_scripts(
            query=script_query,
            text_query=text_query
        )
        
        # Format response
        script_summaries = []
        for script in scripts:
            script_summaries.append({
                "script_id": script.script_id,
                "form_id": script.form_id,
                "generated_at": script.generated_at,
                "generation_method": script.generation_method.value,
                "status": script.status.value,
                "total_duration_seconds": script.total_duration_seconds,
                "segment_count": script.segment_count,
                "quality_score": script.quality_score,
                "usage_count": script.usage_count,
                "last_used_at": script.last_used_at,
                "tags": script.tags,
                "title": script.form_snapshot.get('basic_information', {}).get('stream_title', 'Untitled'),
                "product_name": script.form_snapshot.get('product_information', {}).get('product_name', 'Unknown')
            })
        
        total_pages = (total_count + page_size - 1) // page_size
        
        return ScriptListResponse(
            scripts=script_summaries,
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except Exception as e:
        logger.error(f"Script listing failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list scripts: {str(e)}"
        )


@script_management_router.get("/{script_id}", response_model=ScriptDetailResponse)
async def get_script_details(script_id: str = Path(..., description="Script ID")) -> ScriptDetailResponse:
    """Get detailed information about a specific script"""
    try:
        persistence_manager = get_persistence_manager()
        
        script = await persistence_manager.get_script(script_id)
        if not script:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Script not found: {script_id}"
            )
        
        # Get performance report
        performance_report = await persistence_manager.get_script_performance_report(script_id)
        
        # Format segments
        segments = []
        for segment in script.segments:
            segments.append({
                "segment_id": segment.segment_id,
                "segment_type": segment.segment_type,
                "title": segment.title,
                "content": segment.content,
                "duration_seconds": segment.duration_seconds,
                "priority": segment.priority,
                "segment_order": segment.segment_order,
                "usage_count": segment.usage_count,
                "last_used_at": segment.last_used_at
            })
        
        return ScriptDetailResponse(
            script_id=script.script_id,
            form_id=script.form_id,
            generated_at=script.generated_at,
            generation_method=script.generation_method.value,
            status=script.status.value,
            total_duration_seconds=script.total_duration_seconds,
            segment_count=script.segment_count,
            quality_score=script.quality_score,
            usage_count=script.usage_count,
            last_used_at=script.last_used_at,
            tags=script.tags,
            segments=segments,
            estimated_metrics=script.estimated_metrics,
            performance_summary=performance_report.get('quality_metrics', {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get script details for {script_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve script: {str(e)}"
        )


@script_management_router.post("/{script_id}/usage")
async def track_script_usage(
    script_id: str = Path(..., description="Script ID"),
    usage_data: UsageTrackingRequest = ...
) -> Dict[str, Any]:
    """Track script usage in a streaming session"""
    try:
        persistence_manager = get_persistence_manager()
        
        # Convert request to usage context
        usage_context = {
            "session_id": usage_data.session_id,
            "started_at": usage_data.started_at or datetime.now(),
            "ended_at": usage_data.ended_at,
            "segments_used": usage_data.segments_used,
            "segments_skipped": usage_data.segments_skipped,
            "total_viewers": usage_data.total_viewers,
            "peak_viewers": usage_data.peak_viewers,
            "average_engagement": usage_data.average_engagement,
            "questions_received": usage_data.questions_received,
            "questions_answered": usage_data.questions_answered,
            "interaction_rate": usage_data.interaction_rate,
            "stream_title": usage_data.stream_title,
            "persona_used": usage_data.persona_used,
            "platform_data": usage_data.platform_data
        }
        
        success = await persistence_manager.track_script_usage(script_id, usage_context)
        
        if success:
            return {
                "status": "success",
                "script_id": script_id,
                "session_id": usage_context["session_id"],
                "tracked_at": datetime.now()
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to track script usage"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Usage tracking failed for script {script_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Usage tracking failed: {str(e)}"
        )


@script_management_router.delete("/{script_id}")
async def delete_script(
    script_id: str = Path(..., description="Script ID"),
    hard_delete: bool = Query(False, description="Perform hard delete (permanent)")
) -> Dict[str, Any]:
    """Delete a script (soft delete by default)"""
    try:
        persistence_manager = get_persistence_manager()
        
        success = await persistence_manager.delete_script(script_id, soft_delete=not hard_delete)
        
        if success:
            return {
                "status": "success",
                "script_id": script_id,
                "delete_type": "hard" if hard_delete else "soft",
                "deleted_at": datetime.now()
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Script not found: {script_id}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Script deletion failed for {script_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Deletion failed: {str(e)}"
        )


# === Analytics Endpoints ===

@script_management_router.get("/analytics/overview")
async def get_analytics_overview(
    days: int = Query(30, ge=1, le=365, description="Analysis period in days"),
    include_trends: bool = Query(True, description="Include trend data")
) -> Dict[str, Any]:
    """Get comprehensive analytics overview"""
    try:
        persistence_manager = get_persistence_manager()
        
        analytics = await persistence_manager.get_analytics(days)
        
        response = {
            "period_days": days,
            "generated_at": datetime.now(),
            "overview": {
                "total_scripts": analytics.total_scripts,
                "total_sessions": analytics.total_sessions,
                "total_usage_minutes": analytics.total_usage_minutes,
                "scripts_reuse_rate": analytics.scripts_reuse_rate
            },
            "generation_metrics": {
                "average_generation_time_ms": analytics.average_generation_time_ms,
                "generation_success_rate": analytics.generation_success_rate,
                "scripts_by_method": {k.value: v for k, v in analytics.scripts_by_method.items()}
            },
            "quality_metrics": {
                "average_quality_score": analytics.average_quality_score,
                "quality_distribution": analytics.quality_distribution,
                "top_performing_scripts": analytics.top_performing_scripts[:10]
            },
            "engagement_metrics": {
                "average_engagement_score": analytics.average_engagement_score,
                "average_interaction_rate": analytics.average_interaction_rate,
                "average_retention_rate": analytics.average_retention_rate
            },
            "performance_indicators": {
                "generation_efficiency_score": analytics.generation_efficiency_score,
                "content_utilization_rate": analytics.content_utilization_rate,
                "user_satisfaction_score": analytics.user_satisfaction_score
            }
        }
        
        if include_trends:
            response["trends"] = {
                "generation_trend_7d": analytics.generation_trend_7d,
                "usage_trend_7d": analytics.usage_trend_7d,
                "quality_trend_7d": analytics.quality_trend_7d
            }
        
        return response
        
    except Exception as e:
        logger.error(f"Analytics overview failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Analytics retrieval failed: {str(e)}"
        )


@script_management_router.get("/analytics/performance/{script_id}")
async def get_script_performance(script_id: str = Path(..., description="Script ID")) -> Dict[str, Any]:
    """Get detailed performance analytics for a specific script"""
    try:
        persistence_manager = get_persistence_manager()
        
        report = await persistence_manager.get_script_performance_report(script_id)
        
        if "error" in report:
            if "not found" in report["error"].lower():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Script not found: {script_id}"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=report["error"]
                )
        
        return {
            "script_id": script_id,
            "analyzed_at": datetime.now(),
            **report
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Performance analysis failed for {script_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Performance analysis failed: {str(e)}"
        )


# === Export Endpoints ===

@script_management_router.post("/export")
async def export_scripts(
    export_request: ScriptExportRequest,
    background_tasks: BackgroundTasks
) -> ScriptExportResponse:
    """Export scripts in specified format"""
    try:
        persistence_manager = get_persistence_manager()
        
        # Generate export ID
        export_id = f"export_{int(datetime.now().timestamp())}"
        
        # Start background export task
        background_tasks.add_task(
            _process_script_export,
            persistence_manager,
            export_id,
            export_request
        )
        
        return ScriptExportResponse(
            export_id=export_id,
            format=export_request.export_format.value,
            created_at=datetime.now(),
            expires_at=datetime.now() + timedelta(hours=24)
        )
        
    except Exception as e:
        logger.error(f"Export request failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Export failed: {str(e)}"
        )


@script_management_router.get("/export/{export_id}/download")
async def download_export(export_id: str = Path(..., description="Export ID")):
    """Download exported scripts"""
    try:
        # Check if export file exists
        export_path = f"/tmp/{export_id}.zip"  # Simplified path
        
        if not os.path.exists(export_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Export not found or expired"
            )
        
        return FileResponse(
            path=export_path,
            filename=f"scripts_export_{export_id}.zip",
            media_type="application/zip"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Export download failed for {export_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Download failed: {str(e)}"
        )


# === Management Endpoints ===

@script_management_router.get("/system/stats")
async def get_system_stats() -> Dict[str, Any]:
    """Get system statistics and health information"""
    try:
        persistence_manager = get_persistence_manager()
        observer = GlobalScriptObserver.get_instance()
        
        manager_stats = persistence_manager.get_manager_stats()
        observer_stats = observer.get_observer_stats()
        repository_stats = await persistence_manager.repository.get_repository_stats()
        
        return {
            "timestamp": datetime.now(),
            "persistence_manager": manager_stats,
            "observer": observer_stats,
            "repository": repository_stats,
            "system_health": {
                "database_accessible": repository_stats.get("database_size_mb", 0) > 0,
                "persistence_enabled": observer_stats["config"]["enabled"],
                "last_persistence": manager_stats.get("last_persistence_at")
            }
        }
        
    except Exception as e:
        logger.error(f"System stats retrieval failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"System stats failed: {str(e)}"
        )


@script_management_router.post("/system/maintenance/cleanup")
async def cleanup_old_data(
    retention_days: int = Query(365, ge=30, le=3650, description="Retention period in days"),
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """Clean up old data based on retention policy"""
    try:
        persistence_manager = get_persistence_manager()
        
        # Start cleanup in background
        background_tasks.add_task(
            _cleanup_old_data_task,
            persistence_manager,
            retention_days
        )
        
        return {
            "status": "cleanup_started",
            "retention_days": retention_days,
            "started_at": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Cleanup request failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Cleanup failed: {str(e)}"
        )


@script_management_router.post("/system/maintenance/optimize")
async def optimize_database(background_tasks: BackgroundTasks) -> Dict[str, Any]:
    """Optimize database performance"""
    try:
        persistence_manager = get_persistence_manager()
        
        # Start optimization in background
        background_tasks.add_task(
            _optimize_database_task,
            persistence_manager
        )
        
        return {
            "status": "optimization_started",
            "started_at": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Database optimization failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Optimization failed: {str(e)}"
        )


# === Background Tasks ===

async def _process_script_export(
    persistence_manager: ScriptPersistenceManager,
    export_id: str,
    export_request: ScriptExportRequest
) -> None:
    """Background task to process script export"""
    try:
        logger.info(f"Starting export {export_id} for {len(export_request.script_ids)} scripts")
        
        # Get scripts to export
        scripts = []
        for script_id in export_request.script_ids:
            script = await persistence_manager.get_script(script_id)
            if script:
                scripts.append(script)
        
        # Create export file based on format
        export_path = f"/tmp/{export_id}"
        
        if export_request.export_format == ScriptExportFormat.JSON:
            await _export_scripts_json(scripts, export_path, export_request)
        elif export_request.export_format == ScriptExportFormat.CSV:
            await _export_scripts_csv(scripts, export_path, export_request)
        elif export_request.export_format == ScriptExportFormat.HTML:
            await _export_scripts_html(scripts, export_path, export_request)
        
        logger.info(f"Export {export_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Export {export_id} failed: {e}")


async def _export_scripts_json(scripts: List, export_path: str, export_request: ScriptExportRequest) -> None:
    """Export scripts as JSON"""
    export_data = {
        "export_id": export_path.split('/')[-1],
        "created_at": datetime.now().isoformat(),
        "format": "json",
        "script_count": len(scripts),
        "scripts": []
    }
    
    for script in scripts:
        script_data = {
            "script_id": script.script_id,
            "form_id": script.form_id,
            "generated_at": script.generated_at.isoformat(),
            "generation_method": script.generation_method.value,
            "status": script.status.value,
            "total_duration_seconds": script.total_duration_seconds,
            "quality_score": script.quality_score,
            "usage_count": script.usage_count,
            "tags": script.tags
        }
        
        if export_request.include_segments:
            script_data["segments"] = [
                {
                    "segment_id": seg.segment_id,
                    "segment_type": seg.segment_type,
                    "title": seg.title,
                    "content": seg.content,
                    "duration_seconds": seg.duration_seconds,
                    "priority": seg.priority
                }
                for seg in script.segments
            ]
        
        if export_request.include_metadata:
            script_data["estimated_metrics"] = script.estimated_metrics
            script_data["form_snapshot"] = script.form_snapshot
        
        export_data["scripts"].append(script_data)
    
    with open(f"{export_path}.json", 'w', encoding='utf-8') as f:
        json.dump(export_data, f, indent=2, ensure_ascii=False)


async def _export_scripts_csv(scripts: List, export_path: str, export_request: ScriptExportRequest) -> None:
    """Export scripts as CSV"""
    with open(f"{export_path}.csv", 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        # Write header
        headers = [
            'script_id', 'form_id', 'generated_at', 'generation_method',
            'status', 'total_duration_seconds', 'segment_count',
            'quality_score', 'usage_count', 'tags'
        ]
        writer.writerow(headers)
        
        # Write data
        for script in scripts:
            row = [
                script.script_id,
                script.form_id,
                script.generated_at.isoformat(),
                script.generation_method.value,
                script.status.value,
                script.total_duration_seconds,
                script.segment_count,
                script.quality_score or 0.0,
                script.usage_count,
                ','.join(script.tags)
            ]
            writer.writerow(row)


async def _export_scripts_html(scripts: List, export_path: str, export_request: ScriptExportRequest) -> None:
    """Export scripts as HTML report"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Scripts Export Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .script {{ border: 1px solid #ddd; margin: 20px 0; padding: 20px; }}
            .segment {{ background: #f9f9f9; margin: 10px 0; padding: 10px; }}
            .metadata {{ color: #666; font-size: 0.9em; }}
        </style>
    </head>
    <body>
        <h1>Scripts Export Report</h1>
        <p>Generated: {datetime.now().isoformat()}</p>
        <p>Scripts: {len(scripts)}</p>
        
        {''.join(_format_script_html(script, export_request) for script in scripts)}
    </body>
    </html>
    """
    
    with open(f"{export_path}.html", 'w', encoding='utf-8') as f:
        f.write(html_content)


def _format_script_html(script, export_request: ScriptExportRequest) -> str:
    """Format single script as HTML"""
    segments_html = ""
    if export_request.include_segments:
        segments_html = "".join([
            f"""
            <div class="segment">
                <h4>{seg.title}</h4>
                <p><strong>Type:</strong> {seg.segment_type} | 
                   <strong>Duration:</strong> {seg.duration_seconds}s</p>
                <p>{seg.content}</p>
            </div>
            """
            for seg in script.segments
        ])
    
    return f"""
    <div class="script">
        <h2>{script.script_id}</h2>
        <div class="metadata">
            <p><strong>Form ID:</strong> {script.form_id}</p>
            <p><strong>Generated:</strong> {script.generated_at}</p>
            <p><strong>Method:</strong> {script.generation_method.value}</p>
            <p><strong>Status:</strong> {script.status.value}</p>
            <p><strong>Duration:</strong> {script.total_duration_seconds}s</p>
            <p><strong>Quality:</strong> {script.quality_score or 'N/A'}</p>
            <p><strong>Usage Count:</strong> {script.usage_count}</p>
            <p><strong>Tags:</strong> {', '.join(script.tags)}</p>
        </div>
        {segments_html}
    </div>
    """


async def _cleanup_old_data_task(persistence_manager: ScriptPersistenceManager, retention_days: int) -> None:
    """Background task for data cleanup"""
    try:
        deleted_count = await persistence_manager.cleanup_old_scripts(retention_days)
        logger.info(f"Cleanup completed: {deleted_count} records removed")
    except Exception as e:
        logger.error(f"Cleanup task failed: {e}")


async def _optimize_database_task(persistence_manager: ScriptPersistenceManager) -> None:
    """Background task for database optimization"""
    try:
        success = await persistence_manager.optimize_storage()
        if success:
            logger.info("Database optimization completed successfully")
        else:
            logger.warning("Database optimization completed with warnings")
    except Exception as e:
        logger.error(f"Database optimization task failed: {e}")


