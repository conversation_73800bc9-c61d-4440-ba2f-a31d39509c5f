"""
运营表单与商品关联API
提供表单与商品关联的REST API接口
"""

from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
from loguru import logger

from ..services.form_product_association import FormProductAssociationService
from ..services.persistence.database_manager import DatabaseManager
from ..core.config import cfg
from ..core.exceptions import ServiceError, ValidationError


# 请求/响应模型
class ProductAssociationRequest(BaseModel):
    """商品关联请求"""
    product_id: int
    use_custom_price: bool = False
    custom_streaming_price: Optional[float] = None


class FormProductInfoResponse(BaseModel):
    """表单商品信息响应"""
    form_id: str
    selected_product_id: Optional[int] = None
    product_sku: Optional[str] = None
    product_name: Optional[str] = None
    product_category: Optional[str] = None
    product_price: Optional[float] = None
    product_stock: Optional[int] = None
    product_description: Optional[str] = None
    product_price_config: Optional[Dict[str, Any]] = None
    product_associated_at: Optional[str] = None
    associated_product_qa: List[Dict[str, Any]] = []


# 创建路由器
router = APIRouter(prefix="/api/form-product-associations", tags=["form-product-associations"])

# 初始化服务
db_manager = DatabaseManager(cfg.database_path)
association_service = FormProductAssociationService(db_manager)


@router.get("/forms/{form_id}/product-info", response_model=FormProductInfoResponse)
async def get_form_product_info(form_id: str):
    """获取表单的商品关联信息"""
    try:
        form_data = association_service.get_form_with_product_info(form_id)
        
        return FormProductInfoResponse(
            form_id=form_data['id'],
            selected_product_id=form_data.get('selected_product_id'),
            product_sku=form_data.get('product_sku'),
            product_name=form_data.get('product_name'),
            product_category=form_data.get('product_category'),
            product_price=form_data.get('product_price'),
            product_stock=form_data.get('product_stock'),
            product_description=form_data.get('product_description'),
            product_price_config=form_data.get('product_price_config'),
            product_associated_at=form_data.get('product_associated_at'),
            associated_product_qa=form_data.get('associated_product_qa', [])
        )
        
    except ValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        logger.error(f"获取表单商品信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/forms/{form_id}/associate-product")
async def associate_form_with_product(form_id: str, request: ProductAssociationRequest):
    """将表单与商品关联"""
    try:
        price_config = {
            "use_custom_price": request.use_custom_price,
            "custom_streaming_price": request.custom_streaming_price
        }
        
        success = association_service.associate_form_with_product(
            form_id, request.product_id, price_config
        )
        
        if success:
            return {"message": "表单已成功关联到商品", "form_id": form_id, "product_id": request.product_id}
        else:
            raise HTTPException(status_code=500, detail="关联操作失败")
            
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except ServiceError as e:
        logger.error(f"关联表单与商品失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/forms/{form_id}/product-association")
async def remove_form_product_association(form_id: str):
    """移除表单与商品的关联"""
    try:
        success = association_service.remove_form_product_association(form_id)
        
        if success:
            return {"message": "表单商品关联已移除", "form_id": form_id}
        else:
            raise HTTPException(status_code=500, detail="移除关联失败")
            
    except ValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        logger.error(f"移除表单商品关联失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/products/{product_id}/forms")
async def get_forms_by_product(product_id: int, limit: int = 50):
    """获取使用指定商品的表单列表"""
    try:
        forms = association_service.get_forms_by_product(product_id, limit)
        
        return {
            "product_id": product_id,
            "total_forms": len(forms),
            "forms": forms
        }
        
    except ServiceError as e:
        logger.error(f"查询商品关联表单失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/products/{product_id}/usage-stats")
async def get_product_usage_stats(product_id: int):
    """获取商品使用统计"""
    try:
        stats = association_service.get_product_usage_stats(product_id)
        
        return {
            "product_id": product_id,
            "stats": stats
        }
        
    except ServiceError as e:
        logger.error(f"获取商品使用统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/summary")
async def get_association_summary():
    """获取关联关系总体统计"""
    try:
        summary = association_service.get_association_summary()
        
        return {
            "summary": summary,
            "timestamp": "2025-08-12T13:40:00Z"  # 当前时间戳
        }
        
    except ServiceError as e:
        logger.error(f"获取关联关系统计失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 简单的数据库连接测试
        db_manager.execute_query("SELECT COUNT(*) FROM operational_forms LIMIT 1")
        
        return {
            "status": "healthy",
            "service": "form-product-associations",
            "timestamp": "2025-08-12T13:40:00Z"
        }
        
    except Exception as e:
        logger.error(f"关联服务健康检查失败: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")