"""状态广播器

用于向所有连接的客户端广播状态更新

Author: Claude Code
Date: 2025-08-10
"""

import asyncio
from typing import Dict, Any, Set, Optional
from loguru import logger
from fastapi import WebSocket


class StateBroadcaster:
    """
    状态广播器
    
    负责向所有活跃的WebSocket客户端广播消息
    """
    
    def __init__(self):
        # 活跃的WebSocket连接
        self._active_connections: Dict[str, WebSocket] = {}
        # 连接锁，防止并发修改
        self._lock = asyncio.Lock()
        
        logger.info("状态广播器初始化完成")
    
    async def register_connection(self, client_id: str, websocket: WebSocket):
        """注册WebSocket连接"""
        async with self._lock:
            self._active_connections[client_id] = websocket
            logger.debug(f"注册连接: {client_id}, 当前活跃连接数: {len(self._active_connections)}")
    
    async def unregister_connection(self, client_id: str):
        """注销WebSocket连接"""
        async with self._lock:
            if client_id in self._active_connections:
                del self._active_connections[client_id]
                logger.debug(f"注销连接: {client_id}, 剩余活跃连接数: {len(self._active_connections)}")
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """向所有活跃客户端广播消息"""
        if not self._active_connections:
            logger.debug("没有活跃连接，跳过广播")
            return
        
        disconnected_clients = []
        
        async with self._lock:
            for client_id, websocket in self._active_connections.items():
                try:
                    await websocket.send_json(message)
                    logger.debug(f"向客户端 {client_id} 广播消息: {message.get('type')}")
                except Exception as e:
                    logger.warning(f"向客户端 {client_id} 广播失败: {e}")
                    disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            await self.unregister_connection(client_id)
    
    async def broadcast_to_client(self, client_id: str, message: Dict[str, Any]):
        """向特定客户端发送消息"""
        async with self._lock:
            websocket = self._active_connections.get(client_id)
            if websocket:
                try:
                    await websocket.send_json(message)
                    logger.debug(f"向客户端 {client_id} 发送消息: {message.get('type')}")
                except Exception as e:
                    logger.warning(f"向客户端 {client_id} 发送失败: {e}")
                    await self.unregister_connection(client_id)
            else:
                logger.debug(f"客户端 {client_id} 不存在或已断开")
    
    async def broadcast_synthesis_complete(self, item_id: str, success: bool):
        """广播合成完成通知"""
        message = {
            "type": "synthesis_complete",
            "item_id": item_id,
            "success": success,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.broadcast_to_all(message)
        logger.info(f"📢 广播合成完成: item_id={item_id}, success={success}")
    
    async def ensure_client_version_sync(self, client_id: str, known_version: Optional[int]) -> bool:
        """确保客户端版本同步（预留接口）"""
        # TODO: 实现版本同步逻辑
        return False
    
    def get_active_connections_count(self) -> int:
        """获取活跃连接数"""
        return len(self._active_connections)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "active_connections": len(self._active_connections),
            "client_ids": list(self._active_connections.keys())
        }