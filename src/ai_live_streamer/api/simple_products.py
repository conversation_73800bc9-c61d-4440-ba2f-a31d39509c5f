"""简化的产品管理API
快速实现产品CRUD和QA管理功能
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
from loguru import logger

from ..services.simple_product_service import SimpleProductService
from ..services.persistence import DatabaseManager
from ..core.config import cfg
from ..core.exceptions import ServiceError, ValidationError


# 请求/响应模型
class ProductCreateRequest(BaseModel):
    sku: str
    name: str
    category: str = "other"
    description: Optional[str] = None
    price: Optional[float] = 0
    stock: Optional[int] = 0


class ProductUpdateRequest(BaseModel):
    name: Optional[str] = None
    category: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    stock: Optional[int] = None


class QACreateRequest(BaseModel):
    question: str
    answer: str
    category: Optional[str] = None
    tags: Optional[List[str]] = []
    confidence_score: Optional[float] = 0.5


# 创建路由器
router = APIRouter(prefix="/api/v1/simple-products", tags=["simple-products"])

# 初始化服务
db_manager = DatabaseManager(cfg.database_path)
product_service = SimpleProductService(db_manager)


@router.get("/", response_model=List[Dict[str, Any]])
async def list_products():
    """获取产品列表"""
    try:
        logger.info("API endpoint hit: list_products")
        products = product_service.list_products()
        logger.info(f"返回 {len(products)} 个产品")
        return products
    except ServiceError as e:
        logger.error(f"获取产品列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/debug")
async def debug_endpoint():
    """调试端点"""
    return {"status": "working", "message": "简化产品API正常工作"}


@router.get("/test")
async def test_simple():
    """简单测试端点"""
    return {"test": "success"}


@router.post("/", status_code=status.HTTP_201_CREATED)
async def create_product(product: ProductCreateRequest):
    """创建新产品"""
    try:
        created_product = product_service.create_product(product.dict())
        return created_product
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except ServiceError as e:
        logger.error(f"创建产品失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{product_id}")
async def get_product(product_id: int):
    """获取产品详情"""
    try:
        product = product_service.get_product(product_id)
        return product
    except ValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        logger.error(f"获取产品失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{product_id}")
async def update_product(product_id: int, update_data: ProductUpdateRequest):
    """更新产品"""
    try:
        # 过滤空值
        update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
        updated_product = product_service.update_product(product_id, update_dict)
        return updated_product
    except ValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        logger.error(f"更新产品失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{product_id}")
async def delete_product(product_id: int):
    """删除产品"""
    try:
        product_service.delete_product(product_id)
        return {"message": "产品删除成功"}
    except ValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        logger.error(f"删除产品失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{product_id}/qa")
async def get_product_qa(product_id: int):
    """获取产品QA列表"""
    try:
        qa_list = product_service.get_product_qa(product_id)
        return qa_list
    except ServiceError as e:
        logger.error(f"获取产品QA失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{product_id}/qa", status_code=status.HTTP_201_CREATED)
async def create_product_qa(product_id: int, qa: QACreateRequest):
    """创建产品QA"""
    try:
        created_qa = product_service.create_product_qa(product_id, qa.dict())
        return created_qa
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except ServiceError as e:
        logger.error(f"创建产品QA失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{product_id}/qa/{qa_id}")
async def update_product_qa(product_id: int, qa_id: int, qa: QACreateRequest):
    """更新产品QA"""
    try:
        updated_qa = product_service.update_product_qa(qa_id, qa.dict())
        return updated_qa
    except ValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        logger.error(f"更新产品QA失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{product_id}/qa/{qa_id}")
async def delete_product_qa(product_id: int, qa_id: int):
    """删除产品QA"""
    try:
        product_service.delete_product_qa(qa_id)
        return {"message": "QA删除成功"}
    except ValidationError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ServiceError as e:
        logger.error(f"删除产品QA失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))