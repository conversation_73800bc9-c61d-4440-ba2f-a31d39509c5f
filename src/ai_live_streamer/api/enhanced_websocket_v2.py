"""增强版WebSocket v2处理器 - 集成QA系统v3"""

import asyncio
import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
from fastapi import WebSocket

from .websocket_v2 import WebSocketV2Handler
from ..qa.ranker import Q<PERSON>anker
from ..qa.enhanced_manager import EnhancedQAManager

logger = logging.getLogger(__name__)


class EnhancedWebSocketV2Handler(WebSocketV2Handler):
    """增强版WebSocket v2处理器，集成QA系统v3功能"""
    
    def __init__(self, qa_ranker: <PERSON><PERSON><PERSON><PERSON>, 
                 enhanced_qa_manager: EnhancedQAManager,
                 **kwargs):
        super().__init__(**kwargs)
        
        # 新增组件
        self.qa_ranker = qa_ranker
        self.enhanced_qa_manager = enhanced_qa_manager
        
        # 问题缓冲区管理
        self.question_buffers: Dict[str, List[Dict]] = {}  # 每个客户端的问题缓冲区
        self.buffer_locks: Dict[str, asyncio.Lock] = {}
        self.last_process_times: Dict[str, float] = {}
        
        # 缓冲区配置
        self.buffer_size = 3
        self.buffer_timeout = 5  # 秒
        
        # 扩展统计
        self._enhanced_stats = {
            'enhanced_qa_requests': 0,
            'batch_processed_questions': 0,
            'ignored_low_priority': 0,
            'cache_hits_enhanced': 0,
            'buffer_timeouts': 0
        }
        
        logger.info("✅ EnhancedWebSocketV2Handler 初始化完成")
        
    async def _handle_qa_request(self, websocket: WebSocket, client_id: str, 
                               data: Dict[str, Any]) -> None:
        """增强版QA请求处理，支持智能排序和批处理"""
        self._enhanced_stats['enhanced_qa_requests'] += 1
        
        try:
            # 基本验证
            question_text = data.get("question", "").strip()
            if not question_text:
                await self._send_error(websocket, "qa_error", "问题内容不能为空")
                return
                
            # 构建问题数据
            question_data = {
                'text': question_text,
                'client_id': client_id,
                'session_id': data.get("session_id", "default"),
                'request_id': data.get("request_id"),
                'timestamp': time.time(),
                'websocket': websocket,
                'user_level': data.get("user_level", "normal"),
                'purchase_history': data.get("purchase_history", 0)
            }
            
            # 检查是否启用缓冲区模式
            use_buffer = data.get("use_buffer", True)
            
            if use_buffer:
                # 添加到缓冲区进行批处理
                await self._add_to_question_buffer(client_id, question_data)
            else:
                # 直接处理单个问题
                await self._process_single_qa_enhanced(question_data)
                
        except Exception as e:
            logger.error(f"增强版QA请求处理失败: {client_id}, 错误: {e}")
            await self._send_error(websocket, "qa_error", f"处理失败: {str(e)}")
            
    async def _add_to_question_buffer(self, client_id: str, question_data: Dict):
        """添加问题到客户端缓冲区"""
        # 确保客户端有缓冲区和锁
        if client_id not in self.question_buffers:
            self.question_buffers[client_id] = []
            self.buffer_locks[client_id] = asyncio.Lock()
            self.last_process_times[client_id] = time.time()
            
        async with self.buffer_locks[client_id]:
            self.question_buffers[client_id].append(question_data)
            
            # 检查是否需要处理缓冲区
            should_process = await self._should_process_buffer(client_id)
            
            if should_process:
                # 异步处理缓冲区
                asyncio.create_task(self._process_question_buffer(client_id))
                
    async def _should_process_buffer(self, client_id: str) -> bool:
        """判断是否应该处理问题缓冲区"""
        buffer = self.question_buffers.get(client_id, [])
        
        # 条件1: 缓冲区达到大小限制
        if len(buffer) >= self.buffer_size:
            return True
            
        # 条件2: 超时且缓冲区非空
        current_time = time.time()
        last_process = self.last_process_times.get(client_id, current_time)
        if buffer and (current_time - last_process) > self.buffer_timeout:
            self._enhanced_stats['buffer_timeouts'] += 1
            return True
            
        return False
        
    async def _process_question_buffer(self, client_id: str):
        """处理客户端的问题缓冲区"""
        async with self.buffer_locks[client_id]:
            buffer = self.question_buffers.get(client_id, [])
            if not buffer:
                return
                
            # 获取待处理的问题并清空缓冲区
            questions_to_process = buffer.copy()
            self.question_buffers[client_id].clear()
            self.last_process_times[client_id] = time.time()
            
        logger.info(f"开始批处理{len(questions_to_process)}个问题: {client_id}")
        
        try:
            # 获取直播上下文
            context = await self._get_enhanced_live_context()
            
            # 使用排序器对问题进行优先级排序
            ranked_questions = self.qa_ranker.rank_questions(
                questions_to_process, 
                context
            )
            
            # 处理前N个高优先级问题
            max_process = min(2, len(ranked_questions))  # 限制并发处理数量
            
            # 并发处理高优先级问题
            tasks = []
            for i in range(max_process):
                question = ranked_questions[i]
                if question['priority_score'] > 0.3:  # 只处理中等以上优先级
                    task = self._process_single_qa_enhanced(question)
                    tasks.append(task)
                else:
                    # 记录被忽略的低优先级问题
                    self._enhanced_stats['ignored_low_priority'] += 1
                    logger.debug(f"忽略低优先级问题 (分数:{question['priority_score']:.3f}): {question['text'][:50]}")
                    
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
            self._enhanced_stats['batch_processed_questions'] += len(questions_to_process)
            
            # 对于没有被处理的低优先级问题，发送忽略响应
            for i in range(max_process, len(ranked_questions)):
                question = ranked_questions[i]
                websocket = question.get('websocket')
                if websocket:
                    await self._send_low_priority_response(websocket, question)
                    
        except Exception as e:
            logger.error(f"批处理问题失败: {client_id}, 错误: {e}")
            
    async def _process_single_qa_enhanced(self, question_data: Dict):
        """使用增强QA管理器处理单个问题"""
        websocket = question_data.get('websocket')
        if not websocket:
            return
            
        try:
            # 调用增强QA管理器
            result = await self.enhanced_qa_manager.handle_qa(question_data)
            
            if result.get('should_respond', False):
                # 发送成功响应
                await self._send_enhanced_qa_response(websocket, question_data, result)
                
                # 如果是缓存命中，更新统计
                if result.get('source') == 'cache':
                    self._enhanced_stats['cache_hits_enhanced'] += 1
                    
            else:
                # 发送忽略响应
                await self._send_ignore_response(websocket, question_data, result)
                
        except Exception as e:
            logger.error(f"增强QA处理失败: {e}")
            await self._send_error(websocket, "enhanced_qa_error", str(e))
            
    async def _send_enhanced_qa_response(self, websocket: WebSocket, 
                                       question_data: Dict, result: Dict):
        """发送增强版QA响应"""
        response = {
            "type": "qa_response",
            "success": True,
            "question": question_data.get('text'),
            "answer": result.get('answer'),
            "source": result.get('source'),
            "confidence": result.get('confidence', 0.0),
            "processing_time": result.get('processing_time', 0.0),
            "server_time": time.time(),
            "enhanced": True,  # 标识为增强版响应
            "metadata": result.get('metadata', {})
        }
        
        # 添加请求ID（如果有）
        if question_data.get('request_id'):
            response['request_id'] = question_data['request_id']
            
        await self._send_message(websocket, response)
        logger.info(f"发送增强QA响应: {result.get('answer', '')[:50]}...")
        
    async def _send_ignore_response(self, websocket: WebSocket, 
                                  question_data: Dict, result: Dict):
        """发送问题被忽略的响应"""
        response = {
            "type": "qa_response",
            "success": False,
            "question": question_data.get('text'),
            "reason": result.get('reason', 'low_relevance'),
            "confidence": result.get('confidence', 0.0),
            "server_time": time.time(),
            "enhanced": True,
            "ignored": True,
            "message": "问题相关性不足，暂不回答"
        }
        
        if question_data.get('request_id'):
            response['request_id'] = question_data['request_id']
            
        await self._send_message(websocket, response)
        logger.debug(f"问题被忽略: {question_data.get('text', '')[:50]}")
        
    async def _send_low_priority_response(self, websocket: WebSocket, question_data: Dict):
        """发送低优先级问题的响应"""
        response = {
            "type": "qa_response",
            "success": False,
            "question": question_data.get('text'),
            "reason": "low_priority",
            "priority_score": question_data.get('priority_score', 0.0),
            "server_time": time.time(),
            "enhanced": True,
            "message": "当前问题优先级较低，请稍后重试"
        }
        
        if question_data.get('request_id'):
            response['request_id'] = question_data['request_id']
            
        await self._send_message(websocket, response)
        
    async def _get_enhanced_live_context(self) -> Dict:
        """获取增强版直播上下文"""
        try:
            # 获取基础上下文
            base_context = await self._get_live_context() if hasattr(self, '_get_live_context') else {}
            
            # 增强上下文信息
            enhanced_context = {
                **base_context,
                'active_clients': len(self._connections),
                'buffer_stats': {
                    'total_buffers': len(self.question_buffers),
                    'pending_questions': sum(len(buf) for buf in self.question_buffers.values())
                },
                'current_topic': '产品介绍',  # TODO: 从实际状态获取
                'product_keywords': ['质量', '价格', '优惠', '特点'],
                'audience_engagement': {
                    'active_users': len(self._connections),
                    'recent_questions': self._enhanced_stats['enhanced_qa_requests']
                }
            }
            
            return enhanced_context
            
        except Exception as e:
            logger.warning(f"获取增强直播上下文失败: {e}")
            return {
                'current_topic': '未知',
                'active_users': 0
            }
            
    def get_enhanced_stats(self) -> Dict[str, Any]:
        """获取增强版统计信息"""
        base_stats = getattr(self, '_stats', {})
        
        enhanced_stats = {
            **base_stats,
            **self._enhanced_stats,
            'qa_ranker_stats': self.qa_ranker.get_stats() if self.qa_ranker else {},
            'enhanced_qa_manager_stats': {},  # 将在异步方法中填充
            'buffer_status': {
                'active_buffers': len(self.question_buffers),
                'total_buffered_questions': sum(len(buf) for buf in self.question_buffers.values()),
                'buffer_size_limit': self.buffer_size,
                'buffer_timeout': self.buffer_timeout
            }
        }
        
        return enhanced_stats
        
    async def get_full_enhanced_stats(self) -> Dict[str, Any]:
        """获取完整的增强统计信息（包括异步数据）"""
        enhanced_stats = self.get_enhanced_stats()
        
        try:
            # 获取增强QA管理器的完整统计
            qa_manager_stats = await self.enhanced_qa_manager.get_full_stats()
            enhanced_stats['enhanced_qa_manager_stats'] = qa_manager_stats
        except Exception as e:
            logger.warning(f"获取QA管理器统计失败: {e}")
            
        return enhanced_stats
        
    async def _cleanup_connection(self, client_id: str):
        """清理连接时也清理缓冲区"""
        # 调用父类清理
        await super()._cleanup_connection(client_id)
        
        # 清理缓冲区相关资源
        try:
            if client_id in self.question_buffers:
                # 处理剩余的缓冲区问题
                remaining_questions = self.question_buffers.get(client_id, [])
                if remaining_questions:
                    logger.info(f"清理剩余的{len(remaining_questions)}个缓冲区问题: {client_id}")
                    # 可选：快速处理剩余问题或简单丢弃
                    
                del self.question_buffers[client_id]
                
            if client_id in self.buffer_locks:
                del self.buffer_locks[client_id]
                
            if client_id in self.last_process_times:
                del self.last_process_times[client_id]
                
        except Exception as e:
            logger.warning(f"清理增强WebSocket资源失败: {client_id}, 错误: {e}")
            
    async def handle_batch_qa_request(self, websocket: WebSocket, client_id: str,
                                    data: Dict[str, Any]) -> None:
        """处理批量QA请求（新增API）"""
        try:
            questions = data.get("questions", [])
            if not questions:
                await self._send_error(websocket, "batch_qa_error", "问题列表不能为空")
                return
                
            if len(questions) > 10:
                await self._send_error(websocket, "batch_qa_error", "批量问题不能超过10个")
                return
                
            # 构建问题数据列表
            question_list = []
            for i, q in enumerate(questions):
                question_data = {
                    'text': q.get('text', '').strip(),
                    'client_id': client_id,
                    'session_id': data.get("session_id", "default"),
                    'request_id': f"{data.get('request_id', 'batch')}_{i}",
                    'timestamp': time.time(),
                    'user_level': data.get("user_level", "normal"),
                    'batch_index': i
                }
                question_list.append(question_data)
                
            # 获取上下文并处理
            context = await self._get_enhanced_live_context()
            results = await self.enhanced_qa_manager.handle_batch_questions(
                question_list, context
            )
            
            # 发送批量响应
            batch_response = {
                "type": "batch_qa_response",
                "success": True,
                "total_questions": len(questions),
                "results": results,
                "server_time": time.time(),
                "enhanced": True
            }
            
            if data.get('request_id'):
                batch_response['request_id'] = data['request_id']
                
            await self._send_message(websocket, batch_response)
            logger.info(f"处理批量QA请求完成: {len(questions)}个问题")
            
        except Exception as e:
            logger.error(f"批量QA请求处理失败: {e}")
            await self._send_error(websocket, "batch_qa_error", str(e))
            
    # 扩展消息处理以支持新的消息类型
    async def _handle_message(self, websocket: WebSocket, client_id: str, message: str):
        """扩展消息处理以支持增强功能"""
        try:
            data = json.loads(message)
            msg_type = data.get("type")
            
            # 处理新增的消息类型
            if msg_type == "batch_qa_request":
                await self.handle_batch_qa_request(websocket, client_id, data)
                return
            elif msg_type == "qa_stats_request":
                stats = await self.get_full_enhanced_stats()
                await self._send_message(websocket, {
                    "type": "qa_stats_response",
                    "stats": stats,
                    "server_time": time.time()
                })
                return
            elif msg_type == "buffer_config_update":
                # 动态更新缓冲区配置
                await self._update_buffer_config(data)
                await self._send_message(websocket, {
                    "type": "buffer_config_updated",
                    "success": True,
                    "new_config": {
                        "buffer_size": self.buffer_size,
                        "buffer_timeout": self.buffer_timeout
                    }
                })
                return
                
            # 对于qa_request，使用增强版处理
            if msg_type == "qa_request":
                await self._handle_qa_request(websocket, client_id, data)
                return
                
            # 其他消息类型交给父类处理
            await super()._handle_message(websocket, client_id, message)
            
        except Exception as e:
            logger.error(f"增强消息处理失败: {e}")
            await self._send_error(websocket, "message_error", str(e))
            
    async def _update_buffer_config(self, data: Dict):
        """动态更新缓冲区配置"""
        new_size = data.get("buffer_size")
        new_timeout = data.get("buffer_timeout")
        
        if new_size is not None and 1 <= new_size <= 10:
            self.buffer_size = new_size
            logger.info(f"更新缓冲区大小: {new_size}")
            
        if new_timeout is not None and 1 <= new_timeout <= 30:
            self.buffer_timeout = new_timeout
            logger.info(f"更新缓冲区超时: {new_timeout}s")