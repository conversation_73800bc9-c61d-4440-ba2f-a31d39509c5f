"""Exception definitions for AI Live Streamer system

Following fail-fast principle - all errors must be raised immediately
without fallback mechanisms or exception hiding.
"""

from typing import Any, Dict, Optional


class CriticalError(Exception):
    """Critical system errors that require immediate shutdown
    
    Used for:
    - Configuration validation failures
    - Core service initialization failures
    - Unrecoverable state corruption
    """
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message)
        self.error_code = error_code
        self.context = context or {}


class ServiceError(Exception):
    """Service layer errors for external dependencies
    
    Used for:
    - API call failures
    - Database connection errors
    - LLM service unavailability
    """
    
    def __init__(
        self, 
        message: str, 
        service_name: str,
        error_code: Optional[str] = None,
        retry_count: int = 0,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message)
        self.service_name = service_name
        self.error_code = error_code
        self.retry_count = retry_count
        self.context = context or {}


class AuthenticationError(ServiceError):
    """Authentication/Authorization related errors for external services
    
    Typical causes:
    - Missing or invalid API key
    - Expired credentials
    - Permission denied (401/403)
    """
    def __init__(
        self,
        message: str,
        service_name: str,
        status_code: int | None = None,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, service_name, "AUTHENTICATION_ERROR", 0, context)
        self.status_code = status_code


class InvalidRequestError(ServiceError):
    """Invalid request/parameters sent to external services (HTTP 400, etc.)"""
    def __init__(
        self,
        message: str,
        service_name: str,
        status_code: int | None = None,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, service_name, "INVALID_REQUEST", 0, context)
        self.status_code = status_code


class AudioError(Exception):
    """Audio processing and playback errors
    
    Used for:
    - TTS synthesis failures
    - Audio format conversion errors
    - Playback interruption issues
    """
    
    def __init__(
        self, 
        message: str, 
        audio_operation: str,
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message)
        self.audio_operation = audio_operation
        self.error_code = error_code
        self.context = context or {}


class ConfigError(CriticalError):
    """Configuration validation and loading errors"""
    
    def __init__(
        self, 
        message: str, 
        config_key: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, "CONFIG_ERROR", context)
        self.config_key = config_key


class ValidationError(Exception):
    """Input validation and data format errors"""
    
    def __init__(
        self, 
        message: str, 
        field_name: Optional[str] = None,
        invalid_value: Any = None,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message)
        self.field_name = field_name
        self.invalid_value = invalid_value
        self.context = context or {}


class TimeoutError(ServiceError):
    """Operation timeout errors"""
    
    def __init__(
        self, 
        message: str, 
        operation: str,
        timeout_seconds: float,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message, operation, "TIMEOUT", 0, context)
        self.timeout_seconds = timeout_seconds