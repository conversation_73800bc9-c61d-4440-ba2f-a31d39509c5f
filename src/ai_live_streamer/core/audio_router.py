"""统一音频路由架构

实现策略模式解决音频路径冲突问题。
消除三重音频路径冲突，确保Q&A音频和主流音频正确路由。
遵循CLAUDE.md规范和Fail-Fast原则。
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, Protocol
from loguru import logger

from ..core.exceptions import ServiceError
from ..core.audio_session import get_session_manager, SessionType, SessionState


class AudioBroadcaster(Protocol):
    """音频广播器协议定义"""
    
    async def broadcast_audio_binary(self, audio_data: bytes) -> bool:
        """广播主流音频数据"""
        ...
    
    async def broadcast_qa_audio_binary(self, audio_data: bytes) -> bool:
        """广播Q&A音频数据"""
        ...


class AudioRoutingStrategy(ABC):
    """音频路由策略基类
    
    定义音频路由的标准接口，使用策略模式实现不同类型音频的路由逻辑。
    """
    
    def __init__(self, broadcaster: AudioBroadcaster):
        """初始化路由策略
        
        Args:
            broadcaster: 音频广播器实例
        """
        self.broadcaster = broadcaster
        self.session_manager = get_session_manager()
    
    @abstractmethod
    async def route_audio(
        self, 
        audio_data: bytes, 
        session_id: str,
        priority: str = "normal",
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """路由音频数据
        
        Args:
            audio_data: 音频数据
            session_id: 会话ID
            priority: 优先级 ("low", "normal", "high")
            context: 路由上下文信息
            
        Returns:
            bool: 是否成功路由
        """
        pass


class MainStreamStrategy(AudioRoutingStrategy):
    """主流音频路由策略
    
    处理主流音频的路由逻辑，会检查会话暂停状态。
    """
    
    async def route_audio(
        self, 
        audio_data: bytes, 
        session_id: str,
        priority: str = "normal",
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """路由主流音频数据
        
        Args:
            audio_data: 音频数据
            session_id: 主流会话ID
            priority: 优先级
            context: 上下文信息
            
        Returns:
            bool: 是否成功路由
        """
        context = context or {}
        
        # 获取会话信息
        session = await self.session_manager.get_session(session_id)
        if not session:
            logger.warning(f"Main stream session {session_id} not found")
            return False
        
        # 检查会话类型
        if session.session_type != SessionType.MAIN_STREAM:
            logger.error(f"Session {session_id} is not a main stream session")
            return False
        
        # 检查会话状态
        if session.state == SessionState.PAUSED_FOR_QA:
            logger.debug(f"Main stream {session_id} is paused for Q&A, skipping audio broadcast")
            return False
        elif session.state != SessionState.ACTIVE:
            logger.warning(f"Main stream {session_id} is not active (state: {session.state.value})")
            return False
        
        # 广播音频
        try:
            success = await self.broadcaster.broadcast_audio_binary(audio_data)
            if success:
                # 更新会话指标
                session.update_metrics(chunks_sent=1, bytes_sent=len(audio_data))
                #logger.debug(f"📡 Main stream audio routed: {len(audio_data)} bytes to session {session_id}")
            return success
        except Exception as e:
            session.update_metrics(errors_count=1)
            logger.error(f"❌ Failed to route main stream audio for session {session_id}: {e}")
            raise ServiceError(
                f"Main stream audio routing failed: {str(e)}",
                "audio_router",
                "MAIN_ROUTING_ERROR"
            )


class QAStreamStrategy(AudioRoutingStrategy):
    """Q&A音频路由策略
    
    处理Q&A音频的路由逻辑，始终通过专用通道，不受主流暂停影响。
    """
    
    async def route_audio(
        self, 
        audio_data: bytes, 
        session_id: str,
        priority: str = "normal",
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """路由Q&A音频数据
        
        Args:
            audio_data: 音频数据
            session_id: Q&A会话ID
            priority: 优先级
            context: 上下文信息
            
        Returns:
            bool: 是否成功路由
        """
        context = context or {}
        
        # 获取会话信息
        session = await self.session_manager.get_session(session_id)
        if not session:
            logger.warning(f"Q&A session {session_id} not found")
            return False
        
        # 检查会话类型
        if session.session_type != SessionType.QA_STREAM:
            logger.error(f"Session {session_id} is not a Q&A session")
            return False
        
        # 检查会话状态
        if session.state != SessionState.ACTIVE:
            logger.warning(f"Q&A session {session_id} is not active (state: {session.state.value})")
            return False
        
        # Q&A音频始终通过专用通道，不受主流暂停影响
        try:
            success = await self.broadcaster.broadcast_qa_audio_binary(audio_data)
            if success:
                # 更新会话指标
                session.update_metrics(chunks_sent=1, bytes_sent=len(audio_data))
                #logger.debug(f"🎤 Q&A audio routed: {len(audio_data)} bytes to session {session_id}")
            return success
        except Exception as e:
            session.update_metrics(errors_count=1)
            logger.error(f"❌ Failed to route Q&A audio for session {session_id}: {e}")
            raise ServiceError(
                f"Q&A audio routing failed: {str(e)}",
                "audio_router",
                "QA_ROUTING_ERROR"
            )


class UnifiedAudioRouter:
    """统一音频路由器
    
    消除音频路径冲突，实现清晰的音频路由策略分离。
    使用策略模式确保不同类型的音频通过正确的路径传输。
    """
    
    def __init__(self, broadcaster: AudioBroadcaster):
        """初始化统一音频路由器
        
        Args:
            broadcaster: 音频广播器实例
        """
        self.broadcaster = broadcaster
        self.session_manager = get_session_manager()
        
        # 初始化路由策略
        self.strategies = {
            "main_stream": MainStreamStrategy(broadcaster),
            "qa_stream": QAStreamStrategy(broadcaster)
        }
        
        # 路由统计
        self.stats = {
            "total_routed": 0,
            "main_stream_count": 0,
            "qa_stream_count": 0,
            "routing_errors": 0,
            "last_activity": time.time()
        }
        
        logger.info("✅ Unified audio router initialized")
    
    async def route_audio(
        self, 
        audio_data: bytes, 
        session_id: str,
        session_type: str,
        priority: str = "normal",
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """统一的音频路由入口
        
        Args:
            audio_data: 音频数据
            session_id: 会话ID
            session_type: 会话类型 ("main_stream" | "qa_stream")
            priority: 优先级 ("low", "normal", "high")
            context: 路由上下文信息
            
        Returns:
            bool: 是否成功路由
            
        Raises:
            ServiceError: 当路由失败时
        """
        if not audio_data:
            logger.warning("Empty audio data provided for routing")
            return False
        
        context = context or {}
        
        # 获取对应的路由策略
        strategy = self.strategies.get(session_type)
        if not strategy:
            self.stats["routing_errors"] += 1
            raise ServiceError(
                f"Unknown session type: {session_type}",
                "audio_router",
                "UNKNOWN_SESSION_TYPE"
            )
        
        try:
            # 执行路由
            success = await strategy.route_audio(
                audio_data=audio_data,
                session_id=session_id,
                priority=priority,
                context=context
            )
            
            # 更新统计
            if success:
                self.stats["total_routed"] += 1
                if session_type == "main_stream":
                    self.stats["main_stream_count"] += 1
                elif session_type == "qa_stream":
                    self.stats["qa_stream_count"] += 1
                self.stats["last_activity"] = time.time()
            
            return success
            
        except Exception as e:
            self.stats["routing_errors"] += 1
            if isinstance(e, ServiceError):
                raise
            logger.error(f"❌ Unexpected error routing audio for session {session_id}: {e}")
            raise ServiceError(
                f"Audio routing failed: {str(e)}",
                "audio_router",
                "ROUTING_ERROR"
            )
    
    async def route_main_stream_audio(
        self, 
        audio_data: bytes, 
        session_id: str,
        priority: str = "normal"
    ) -> bool:
        """便捷方法：路由主流音频
        
        Args:
            audio_data: 音频数据
            session_id: 主流会话ID
            priority: 优先级
            
        Returns:
            bool: 是否成功路由
        """
        return await self.route_audio(
            audio_data=audio_data,
            session_id=session_id,
            session_type="main_stream",
            priority=priority
        )
    
    async def route_qa_audio(
        self, 
        audio_data: bytes, 
        session_id: str,
        priority: str = "high"
    ) -> bool:
        """便捷方法：路由Q&A音频
        
        Args:
            audio_data: 音频数据
            session_id: Q&A会话ID
            priority: 优先级（默认高优先级）
            
        Returns:
            bool: 是否成功路由
        """
        return await self.route_audio(
            audio_data=audio_data,
            session_id=session_id,
            session_type="qa_stream",
            priority=priority
        )
    
    def add_strategy(self, session_type: str, strategy: AudioRoutingStrategy):
        """添加自定义路由策略
        
        Args:
            session_type: 会话类型
            strategy: 路由策略实例
        """
        self.strategies[session_type] = strategy
        logger.info(f"Added custom routing strategy for session type: {session_type}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取路由统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            **self.stats,
            "active_strategies": list(self.strategies.keys()),
            "session_manager_status": self.session_manager.get_status()
        }
    
    async def health_check(self) -> bool:
        """检查路由器健康状态
        
        Returns:
            bool: 是否健康
        """
        try:
            # 检查会话管理器
            if not self.session_manager:
                return False
            
            # 检查广播器
            if not self.broadcaster:
                return False
            
            # 检查策略
            if not self.strategies:
                return False
            
            return True
        except Exception as e:
            logger.error(f"❌ Audio router health check failed: {e}")
            return False


# 全局路由器实例
_audio_router: Optional[UnifiedAudioRouter] = None


def get_audio_router(broadcaster: Optional[AudioBroadcaster] = None) -> UnifiedAudioRouter:
    """获取全局音频路由器实例
    
    Args:
        broadcaster: 音频广播器（首次调用时必需）
        
    Returns:
        UnifiedAudioRouter: 路由器实例
        
    Raises:
        ServiceError: 当广播器未提供且路由器未初始化时
    """
    global _audio_router
    
    if _audio_router is None:
        if broadcaster is None:
            raise ServiceError(
                "Audio router not initialized and no broadcaster provided",
                "audio_router",
                "NOT_INITIALIZED"
            )
        _audio_router = UnifiedAudioRouter(broadcaster)
    
    return _audio_router


def reset_audio_router():
    """重置全局音频路由器（主要用于测试）"""
    global _audio_router
    _audio_router = None