"""QA Prompt Builder

专门负责构建QA相关的prompt，集成模板管理器和上下文信息，
生成适合直播场景的高质量prompt。

Author: Claude Code  
Date: 2025-08-06
"""

from typing import List, Dict, Any, Optional, Tuple
from loguru import logger

from .context import QADynamicContext
from .template_manager import get_template_manager, PromptTemplateManager
from ..core.exceptions import ServiceError


class QAPromptBuilder:
    """QA Prompt构建器
    
    负责根据动态上下文构建完整的QA prompt，
    支持系统prompt和用户prompt的分离，
    预留知识库检索功能接口。
    """
    
    def __init__(self, template_manager: Optional[PromptTemplateManager] = None):
        """初始化Prompt构建器
        
        Args:
            template_manager: 模板管理器实例，为空时使用全局实例
        """
        self.template_manager = template_manager or get_template_manager()
        
        # 知识库检索功能开关（预留接口）
        self.enable_knowledge_retrieval = self.template_manager.is_knowledge_base_enabled()
        
        logger.info(f"✅ QAPromptBuilder initialized (knowledge_base: {self.enable_knowledge_retrieval})")
    
    def build_full_prompt(self, context: QADynamicContext) -> Tuple[str, str]:
        """构建完整的prompt（系统prompt + 用户prompt）
        
        Args:
            context: QA动态上下文
            
        Returns:
            Tuple[str, str]: (system_prompt, user_prompt)
            
        Raises:
            ServiceError: prompt构建失败时抛出
        """
        try:
            logger.debug(f"🔨 Building full prompt for question: {context.question.text[:50]}...")
            
            # 如果启用知识库，先进行检索（预留接口）
            if self.enable_knowledge_retrieval:
                self._enrich_context_with_knowledge(context)
            
            # 构建系统prompt
            system_prompt = self.template_manager.render_system_prompt(context)
            
            # 构建用户prompt  
            user_prompt = self.template_manager.render_user_prompt(context)
            
            # 验证prompt质量
            self._validate_prompt_quality(system_prompt, user_prompt, context)
            
            logger.info(f"✅ Full prompt built successfully (sys: {len(system_prompt)}, user: {len(user_prompt)})")
            
            return system_prompt, user_prompt
            
        except Exception as e:
            logger.error(f"❌ Failed to build full prompt: {e}")
            raise ServiceError(f"Prompt building failed: {e}", "qa_prompt_builder")
    
    def build_system_prompt(self, context: QADynamicContext) -> str:
        """构建系统级prompt
        
        Args:
            context: QA动态上下文
            
        Returns:
            str: 系统prompt
        """
        try:
            system_prompt = self.template_manager.render_system_prompt(context)
            logger.debug(f"✅ System prompt built, length: {len(system_prompt)}")
            return system_prompt
            
        except Exception as e:
            logger.error(f"❌ Failed to build system prompt: {e}")
            raise ServiceError(f"System prompt building failed: {e}", "qa_prompt_builder")
    
    def build_user_prompt(self, context: QADynamicContext) -> str:
        """构建用户问题prompt
        
        Args:
            context: QA动态上下文
            
        Returns:
            str: 用户prompt
        """
        try:
            user_prompt = self.template_manager.render_user_prompt(context)
            logger.debug(f"✅ User prompt built, length: {len(user_prompt)}")
            return user_prompt
            
        except Exception as e:
            logger.error(f"❌ Failed to build user prompt: {e}")
            raise ServiceError(f"User prompt building failed: {e}", "qa_prompt_builder")
    
    def build_simple_prompt(self, context: QADynamicContext) -> str:
        """构建简单的单一prompt（兼容旧版API）
        
        Args:
            context: QA动态上下文
            
        Returns:
            str: 完整prompt
        """
        try:
            system_prompt, user_prompt = self.build_full_prompt(context)
            
            # 合并为单一prompt
            combined_prompt = f"{system_prompt}\n\n{user_prompt}"
            
            logger.debug(f"✅ Simple prompt built, total length: {len(combined_prompt)}")
            return combined_prompt
            
        except Exception as e:
            logger.error(f"❌ Failed to build simple prompt: {e}")
            raise ServiceError(f"Simple prompt building failed: {e}", "qa_prompt_builder")
    
    def _enrich_context_with_knowledge(self, context: QADynamicContext) -> None:
        """使用知识库检索增强上下文（预留接口）
        
        Args:
            context: QA动态上下文（会被就地修改）
            
        Note:
            这是预留接口，将来可以集成RAG功能。
            目前仅做占位符实现，不影响现有功能。
        """
        if not self.enable_knowledge_retrieval:
            return
            
        try:
            # 预留：未来这里将实现知识库检索逻辑
            # 1. 根据问题提取关键词
            # 2. 调用知识库搜索API  
            # 3. 整理搜索结果并添加到context.knowledge_base_info
            
            logger.debug("🔍 Knowledge base retrieval is enabled but not implemented yet")
            
            # 占位符实现：模拟知识库检索
            if not context.knowledge_base_info:
                context.knowledge_base_info = "（知识库检索功能开发中）"
                
        except Exception as e:
            logger.warning(f"⚠️ Knowledge base retrieval failed: {e}")
            # 知识库检索失败不应该影响正常的QA流程
            context.knowledge_base_info = ""
    
    def _validate_prompt_quality(self, system_prompt: str, user_prompt: str, context: QADynamicContext) -> None:
        """验证prompt质量
        
        Args:
            system_prompt: 系统prompt
            user_prompt: 用户prompt  
            context: QA动态上下文
            
        Raises:
            ServiceError: prompt质量不合格时抛出
        """
        # 检查prompt长度
        if len(system_prompt) < 50:
            raise ServiceError("System prompt too short (< 50 chars)", "qa_prompt_builder")
        
        if len(user_prompt) < 20:
            raise ServiceError("User prompt too short (< 20 chars)", "qa_prompt_builder")
        
        # 检查关键信息是否包含
        if not context.question or not context.question.text.strip():
            raise ServiceError("Question text is empty", "qa_prompt_builder")
        
        # 检查是否包含基本的人设信息
        if not context.persona_name and not context.persona_style:
            logger.warning("⚠️ No persona information available, using defaults")
        
        # 检查prompt模板变量是否正确替换
        if '{' in system_prompt or '}' in system_prompt:
            logger.warning("⚠️ System prompt contains unreplaced template variables")
        
        if '{' in user_prompt or '}' in user_prompt:
            logger.warning("⚠️ User prompt contains unreplaced template variables")
        
        logger.debug("✅ Prompt quality validation passed")
    
    def get_fallback_prompt(self, context: QADynamicContext, error_type: str = "error_response") -> str:
        """获取备用prompt（当正常构建失败时使用）
        
        Args:
            context: QA动态上下文
            error_type: 错误类型
            
        Returns:
            str: 备用prompt
        """
        try:
            # 获取模板中的备用回答
            fallback_text = self.template_manager.get_fallback_response(error_type)
            
            # 构建最小化的prompt
            simple_prompt = f"""
你是直播主播{context.persona_name or '主播'}，请用简洁友好的语气回答观众问题。

观众问题：{context.question.text if context.question else ''}

请回答：{fallback_text}
"""
            
            logger.info(f"🆘 Generated fallback prompt for error_type: {error_type}")
            return simple_prompt.strip()
            
        except Exception as e:
            logger.error(f"❌ Failed to generate fallback prompt: {e}")
            # 最终备用方案
            return "请用简洁友好的语气回答观众的问题。"
    
    def reload_templates(self) -> None:
        """重新加载模板（支持运营热更新）"""
        try:
            self.template_manager.reload_templates()
            self.enable_knowledge_retrieval = self.template_manager.is_knowledge_base_enabled()
            logger.info("🔄 Prompt templates reloaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to reload templates: {e}")
            raise ServiceError(f"Template reload failed: {e}", "qa_prompt_builder")
    
    def get_builder_stats(self) -> Dict[str, Any]:
        """获取构建器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "template_file": self.template_manager.template_file,
            "knowledge_base_enabled": self.enable_knowledge_retrieval,
            "available_templates": list(self.template_manager.templates.keys()),
            "template_config": self.template_manager.config
        }