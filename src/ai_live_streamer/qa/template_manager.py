"""Prompt Template Manager

管理QA prompt模板的加载、渲染和配置，
支持外部化模板文件和运营人员自定义。

Author: Claude Code  
Date: 2025-08-06
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger

from .context import QADynamicContext
from ..core.exceptions import ServiceError


class PromptTemplateManager:
    """Prompt模板管理器
    
    负责加载、缓存和渲染QA相关的prompt模板，
    支持配置化管理和热更新。
    """
    
    def __init__(self, template_file: Optional[str] = None):
        """初始化模板管理器
        
        Args:
            template_file: 模板文件路径，为空时使用默认路径
        """
        self.template_file = template_file or self._get_default_template_path()
        self.templates: Dict[str, Any] = {}
        self.config: Dict[str, Any] = {}
        self._load_templates()
    
    def _get_default_template_path(self) -> str:
        """获取默认模板文件路径"""
        current_dir = Path(__file__).parent
        return str(current_dir / "prompts" / "default.yaml")
    
    def _load_templates(self) -> None:
        """加载模板文件
        
        Raises:
            ServiceError: 模板文件加载失败时抛出
        """
        try:
            if not os.path.exists(self.template_file):
                raise FileNotFoundError(f"Template file not found: {self.template_file}")
            
            with open(self.template_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            if not isinstance(data, dict):
                raise ValueError("Template file must contain a YAML dictionary")
            
            self.templates = data
            self.config = data.get('config', {})
            
            logger.info(f"✅ Loaded prompt templates from: {self.template_file}")
            logger.debug(f"Available templates: {list(self.templates.keys())}")
            
        except Exception as e:
            logger.error(f"❌ Failed to load prompt templates: {e}")
            raise ServiceError(f"Template loading failed: {e}")
    
    def reload_templates(self) -> None:
        """重新加载模板文件（支持热更新）"""
        logger.info("🔄 Reloading prompt templates...")
        self._load_templates()
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key: 配置键名
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        return self.config.get(key, default)
    
    def is_knowledge_base_enabled(self) -> bool:
        """检查是否启用知识库检索
        
        Returns:
            bool: 是否启用知识库
        """
        return self.get_config_value('enable_knowledge_base', False)
    
    def render_system_prompt(self, context: QADynamicContext) -> str:
        """渲染系统级prompt
        
        Args:
            context: QA动态上下文
            
        Returns:
            str: 渲染后的系统prompt
            
        Raises:
            ServiceError: 渲染失败时抛出
        """
        try:
            template = self.templates.get('system_prompt', '')
            if not template:
                raise ValueError("system_prompt template not found")
            
            # 准备渲染变量
            render_vars = self._prepare_render_variables(context)
            
            # 渲染模板
            rendered = template.format(**render_vars)
            
            logger.debug(f"✅ System prompt rendered, length: {len(rendered)}")
            return rendered
            
        except Exception as e:
            logger.error(f"❌ Failed to render system prompt: {e}")
            raise ServiceError(f"System prompt rendering failed: {e}")
    
    def render_user_prompt(self, context: QADynamicContext) -> str:
        """渲染用户问题prompt
        
        Args:
            context: QA动态上下文
            
        Returns:
            str: 渲染后的用户prompt
            
        Raises:
            ServiceError: 渲染失败时抛出
        """
        try:
            template = self.templates.get('user_prompt', '')
            if not template:
                raise ValueError("user_prompt template not found")
            
            # 准备渲染变量
            render_vars = self._prepare_render_variables(context)
            
            # 处理知识库部分
            knowledge_section = self._render_knowledge_section(context)
            render_vars['knowledge_section'] = knowledge_section
            
            # 渲染模板
            rendered = template.format(**render_vars)
            
            logger.debug(f"✅ User prompt rendered, length: {len(rendered)}")
            return rendered
            
        except Exception as e:
            logger.error(f"❌ Failed to render user prompt: {e}")
            raise ServiceError(f"User prompt rendering failed: {e}")
    
    def _render_knowledge_section(self, context: QADynamicContext) -> str:
        """渲染知识库部分
        
        Args:
            context: QA动态上下文
            
        Returns:
            str: 渲染后的知识库部分
        """
        if self.is_knowledge_base_enabled() and context.knowledge_base_info:
            # 使用启用知识库的模板
            template = self.templates.get('knowledge_section_enabled', '')
            return template.format(knowledge_base_info=context.knowledge_base_info)
        else:
            # 使用未启用知识库的模板
            return self.templates.get('knowledge_section_disabled', '')
    
    def _prepare_render_variables(self, context: QADynamicContext) -> Dict[str, str]:
        """准备模板渲染变量
        
        Args:
            context: QA动态上下文
            
        Returns:
            Dict[str, str]: 渲染变量字典
        """
        # 获取配置默认值
        default_max_length = self.get_config_value('default_max_response_length', 50)
        default_persona_name = self.get_config_value('default_persona_name', '主播小助手')
        default_persona_style = self.get_config_value('default_persona_style', '亲切友好')
        default_live_topic = self.get_config_value('default_live_topic', '精选好物分享')
        
        # 准备渲染变量
        return {
            # 问题相关
            'question_text': context.question.text if context.question else '',
            
            # 人设相关
            'persona_name': context.persona_name or default_persona_name,
            'persona_style': context.persona_style or default_persona_style,
            
            # 直播相关
            'live_topic': context.live_topic or default_live_topic,
            'current_script_segment': context.current_script_segment or '当前商品介绍',
            
            # 产品相关
            'product_name': context.product_name or '精选商品',
            'sku_id': context.sku_id or 'default',
            
            # 对话历史
            'chat_history': context.format_chat_history_for_prompt(
                limit=self.get_config_value('chat_history_rounds', 3)
            ),
            
            # 配置相关
            'max_response_length': str(context.max_response_length or default_max_length),
            
            # 知识库相关
            'knowledge_base_info': context.knowledge_base_info or '',
        }
    
    def get_fallback_response(self, response_type: str) -> str:
        """获取备用回答
        
        Args:
            response_type: 回答类型 ('empty_question', 'timeout_response', 'error_response')
            
        Returns:
            str: 备用回答文本
        """
        fallbacks = self.templates.get('fallback_responses', {})
        return fallbacks.get(response_type, "抱歉，我现在遇到了一些问题，请稍后再试。")
    
    def validate_templates(self) -> bool:
        """验证模板完整性
        
        Returns:
            bool: 模板是否有效
        """
        required_templates = ['system_prompt', 'user_prompt']
        
        for template_name in required_templates:
            if template_name not in self.templates:
                logger.error(f"❌ Missing required template: {template_name}")
                return False
            
            if not self.templates[template_name].strip():
                logger.error(f"❌ Empty template: {template_name}")
                return False
        
        logger.info("✅ All templates are valid")
        return True


# 全局模板管理器实例
_template_manager: Optional[PromptTemplateManager] = None


def get_template_manager(template_file: Optional[str] = None) -> PromptTemplateManager:
    """获取全局模板管理器实例
    
    Args:
        template_file: 模板文件路径（仅首次初始化时有效）
        
    Returns:
        PromptTemplateManager: 模板管理器实例
    """
    global _template_manager
    if _template_manager is None:
        _template_manager = PromptTemplateManager(template_file)
    return _template_manager


def reload_templates() -> None:
    """重新加载全局模板（用于运营热更新）"""
    global _template_manager
    if _template_manager:
        _template_manager.reload_templates()
    else:
        logger.warning("⚠️ Template manager not initialized, creating new instance")
        _template_manager = PromptTemplateManager()