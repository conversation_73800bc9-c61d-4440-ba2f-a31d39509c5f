"""音频流代理服务

负责管理多个WebSocket客户端连接，接收CosyVoice合成的音频流，
并实时广播给所有连接的客户端。支持连接管理、错误处理和性能监控。
"""

import asyncio
import json
from typing import Set, Dict, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger
import time

from .tts_engines.cosyvoice_unified_engine import CosyVoiceUnifiedEngine
from ..core.exceptions import ServiceError
from ..exceptions.client_connection_errors import NoClientsConnectedError, AllClientsDisconnectedError
from ..monitoring.audio_metrics import get_audio_metrics
from ..monitoring.ghost_playback_metrics import get_ghost_playback_metrics
from ..utils.websocket_utils import is_websocket_connected, safe_close_websocket
from ..core.config import cfg
from ..core.audio_session import get_session_manager, SessionType, SessionState
from ..core.audio_router import get_audio_router
# from ..core.qa_coordinator import get_qa_coordinator  # Removed in simplified architecture


class AudioStreamingProxy:
    """音频流代理服务
    
    功能：
    - 管理多个WebSocket客户端连接
    - 代理CosyVoice音频流到所有客户端
    - 连接状态管理和错误处理
    - 性能监控和统计
    """
    
    def __init__(self):
        """初始化音频流代理"""
        self.clients: Set[WebSocket] = set()
        
        # 集成新的会话管理架构
        self.session_manager = get_session_manager()
        self.audio_router = get_audio_router(broadcaster=self)  # 传入自己作为广播器
        # self.qa_coordinator = get_qa_coordinator(audio_router=self.audio_router)  # Removed in simplified architecture
        
        # 移除全局状态变量，现在由会话管理器管理
        # self.is_streaming = False  # 移除：由会话管理器管理
        # self.current_session_id: Optional[str] = None  # 移除：由会话管理器管理
        # self.is_paused = False  # 移除：由会话级暂停状态管理
        # self.paused_for_qa = False  # 移除：由会话级暂停状态管理
        
        # 统计信息
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "total_audio_chunks": 0,
            "total_bytes_sent": 0,
            "streaming_sessions": 0,
            "last_activity": None
        }
        
        # 配置
        self.max_clients = 10
        self.connection_timeout = 300  # 5分钟连接超时
        
        # 🔥 心跳机制配置
        self.heartbeat_interval = 15  # 15秒心跳间隔
        self.heartbeat_timeout = 45  # 45秒心跳超时
        self._client_heartbeats: Dict[int, float] = {}  # 客户端ID -> 最后心跳时间
        self._heartbeat_monitor_task: Optional[asyncio.Task] = None
        
        # 监控指标
        self.metrics = get_audio_metrics()
        self.ghost_metrics = get_ghost_playback_metrics()
        
        # 加载配置
        self.config = cfg
        
        logger.info("✅ Audio streaming proxy initialized with new session management")
        
    async def add_client(self, websocket: WebSocket):
        """添加客户端连接 - 增强日志版本
        
        Args:
            websocket: WebSocket连接
            
        Raises:
            ServiceError: 连接数超过限制
        """
        if len(self.clients) >= self.max_clients:
            await websocket.close(code=1013, reason="Too many connections")
            raise ServiceError(
                f"Too many connections, limit: {self.max_clients}", 
                "audio_streaming_proxy", 
                "CONNECTION_LIMIT_EXCEEDED"
            )
        
        # 获取客户端详细信息
        client_info = "unknown"
        if hasattr(websocket, 'client') and websocket.client:
            client_info = f"{websocket.client.host}:{websocket.client.port}"
        
        self.clients.add(websocket)
        self.stats["total_connections"] += 1
        self.stats["active_connections"] = len(self.clients)
        self.stats["last_activity"] = time.time()
        
        # 🔥 初始化心跳记录
        client_id = id(websocket)
        self._client_heartbeats[client_id] = time.time()
        
        # 更新监控指标
        self.metrics.update_connection_count(len(self.clients))
        
        # 🔥 启动心跳监控任务（如果还没启动）
        if self._heartbeat_monitor_task is None:
            self._heartbeat_monitor_task = asyncio.create_task(self._monitor_heartbeats())
            logger.info("💓 Heartbeat monitor started")
        
        # 增强日志：包含客户端来源信息
        logger.info(f"✅ Audio client registered from {client_info}, total active: {len(self.clients)}")
        
        # 🔥 记录客户端连接
        self.ghost_metrics.record_client_connection()
        
        # 🔥 如果是从0到1的客户端连接，通知playlist_manager恢复
        if len(self.clients) == 1:
            logger.info("🔔 First client connected, notifying playlist manager to resume")
            try:
                from ..core.dependencies import get_playlist_manager
                playlist_manager = get_playlist_manager()
                if playlist_manager and playlist_manager.is_paused():
                    pause_info = playlist_manager.get_pause_info()
                    if pause_info.get("reason") == playlist_manager.PAUSE_REASON_NO_CLIENTS:
                        await playlist_manager.resume()
                        logger.info("✅ Playlist manager resumed after client reconnection")
            except Exception as e:
                logger.error(f"Failed to notify playlist manager about client connection: {e}")
        
        # 获取当前会话状态
        main_session = await self.session_manager.get_main_session()
        is_streaming = main_session is not None and main_session.state == SessionState.ACTIVE
        current_session_id = main_session.session_id if main_session else None
        
        # 🔧 V2 Protocol Fix: 移除V1遗留的欢迎消息，避免与websocket_v2的connection_established消息竞争
        # 职责分离：audio_streaming_proxy专注音频数据传输，应用层握手由websocket_v2处理
        # 原V1消息格式：{"type": "event", "event": "connected", "data": {...}}
        # 现在由websocket_v2.py发送正确的V2格式：{"type": "connection_established", ...}
        logger.debug(f"🔄 V2模式：跳过发送欢迎消息，由websocket_v2处理握手协议")
        
    async def remove_client(self, websocket: WebSocket):
        """移除客户端连接
        
        Args:
            websocket: WebSocket连接
        """
        # 记录断开前的客户端数量
        clients_before = len(self.clients)
        
        self.clients.discard(websocket)
        self.stats["active_connections"] = len(self.clients)
        
        # 🔥 清理心跳记录
        client_id = id(websocket)
        self._client_heartbeats.pop(client_id, None)
        
        # 更新监控指标
        self.metrics.update_connection_count(len(self.clients))
        
        logger.info(f"Client disconnected, total active: {len(self.clients)}")
        
        # 🔥 记录客户端掉线
        self.ghost_metrics.record_client_dropout()
        
        # 🔥 检测是否是最后一个客户端断开
        if clients_before > 0 and len(self.clients) == 0:
            logger.warning("⚠️ All clients have disconnected - no active connections")
            # 开始幽灵播放监控
            self.ghost_metrics.start_ghost_playback()
            # 这里可以触发事件通知其他组件
            await self._notify_all_clients_disconnected()
        
    async def broadcast_audio_binary(self, audio_data: bytes) -> bool:
        """广播主流二进制音频数据
        
        直接发送二进制数据，不进行编码转换以减少延迟。
        移除全局暂停检查，暂停逻辑现在由音频路由器在会话级别管理。
        
        Args:
            audio_data: 原始音频数据
            
        Returns:
            bool: 是否成功广播
            
        Raises:
            NoClientsConnectedError: 当没有客户端连接时抛出
        """
        if not self.clients:
            logger.debug(f"No WebSocket clients connected, skipping broadcast of {len(audio_data)} bytes")
            # 🔥 记录浪费的音频块
            self.ghost_metrics.record_wasted_audio_chunk(len(audio_data))
            # 🔥 方案B：抛出异常而不是返回False
            raise NoClientsConnectedError(
                f"No clients connected to receive audio ({len(audio_data)} bytes)"
            )
            
        # 移除全局暂停检查 - 现在由音频路由器管理会话级暂停
        # if self.is_paused:  # 移除：暂停逻辑由路由器管理
        #     logger.debug(f"Audio streaming paused, skipping broadcast of {len(audio_data)} bytes")
        #     return
            
        # 更新统计
        self.stats["total_audio_chunks"] += 1
        self.stats["total_bytes_sent"] += len(audio_data)
        self.stats["last_activity"] = time.time()
        
        # 更新监控指标
        self.metrics.record_audio_chunk_sent(len(audio_data))
        
        # 统计信息
        chunk_number = self.stats["total_audio_chunks"]
        total_mb_sent = self.stats["total_bytes_sent"] / 1024 / 1024
        
        # 批量日志记录 - 调整为非常低频率，只在重要节点记录
        # 只在整百数时记录简要信息
        pass  # 移除所有常规chunk日志
        
        # 广播到所有客户端
        disconnected = set()
        successful_sends = 0
        failed_sends = 0
        
        broadcast_start_time = time.time()
        
        for client in self.clients:
            try:
                # 直接发送数据，让 send_bytes 自己处理连接错误
                await client.send_bytes(audio_data)
                # 记录成功接收
                self.metrics.record_audio_chunk_received()
                successful_sends += 1
            except WebSocketDisconnect:
                disconnected.add(client)
                self.metrics.record_connection_drop()
                failed_sends += 1
                logger.info(f"❌ Client {id(client)} disconnected during audio broadcast")
            except Exception as e:
                # 检查是否是 ClientConnection 相关错误
                if "'ClientConnection' object has no attribute 'closed'" in str(e):
                    logger.error(f"❌ ClientConnection attribute error for client {id(client)}: {e}")
                    # Fail-Fast: 这是一个严重的内部错误，应该抛出异常
                    raise ServiceError(
                        f"WebSocket connection state check failed: {e}",
                        "audio_streaming_proxy",
                        "CONNECTION_STATE_ERROR"
                    )
                else:
                    logger.warning(f"❌ Failed to send audio to client {id(client)}: {e}")
                    disconnected.add(client)
                    self.metrics.record_audio_chunk_failed()
                    failed_sends += 1
                
        broadcast_duration_ms = (time.time() - broadcast_start_time) * 1000
        
        # 清理断开的连接
        for client in disconnected:
            await self.remove_client(client)
            
        # 详细的传输统计 - 减少日志频率，主要展示异常
        if disconnected:  # 有断开连接时详细记录
            logger.info(f"📡 Audio broadcast stats for chunk #{chunk_number}:")
            logger.info(f"   ✅ Successful: {successful_sends}/{len(self.clients) + len(disconnected)} clients")
            logger.info(f"   ❌ Failed: {failed_sends} clients")
            logger.info(f"   🧹 Cleaned up: {len(disconnected)} disconnected clients")
            logger.info(f"   ⏱️ Broadcast time: {broadcast_duration_ms:.1f}ms")
            logger.info(f"   📊 Total sent: {total_mb_sent:.2f}MB in {chunk_number} chunks")
        elif chunk_number % 500 == 0:  # 减少到每100个chunk记录一次总体状态
            logger.info(f"📡 Audio streaming progress: chunk #{chunk_number}, {total_mb_sent:.2f}MB sent to {len(self.clients)} clients")
        elif failed_sends > 0:  # 有发送失败时记录
            logger.warning(f"📡 Chunk #{chunk_number}: ❌ {failed_sends} failed sends, ✅ {successful_sends} successful")
        
        return successful_sends > 0  # 返回是否有成功的广播
            
    async def broadcast_event(self, event_type: str, data: Dict[str, Any] = None):
        """广播事件消息
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        message = {
            "type": "event",
            "event": event_type,
            "data": data or {},
            "timestamp": time.time()
        }
        
        disconnected = set()
        
        for client in self.clients:
            try:
                await self._send_to_client(client, message)
            except Exception:
                disconnected.add(client)
                
        # 清理断开的连接
        for client in disconnected:
            await self.remove_client(client)
    
    async def broadcast_clear_audio_queue(self, reason: str = "qa_interrupt", qa_session_id: str = None):
        """广播清空音频队列事件
        
        通知所有客户端立即清空音频播放队列，为Q&A音频让路。
        
        Args:
            reason: 清空原因
            qa_session_id: 引起清空的Q&A会话ID
        """
        logger.info(f"📢 Broadcasting clear audio queue event: reason={reason}, qa_session_id={qa_session_id}")
        
        await self.broadcast_event("clear_audio_queue", {
            "reason": reason,
            "qa_session_id": qa_session_id,
            "priority": "urgent",
            "immediate": True,
            "instructions": {
                "clear_main_stream_buffer": True,
                "prepare_for_qa": True,
                "switch_to_qa_mode": True
            }
        })
        
        logger.info(f"✅ Clear audio queue event broadcasted to {len(self.clients)} clients")
    
    async def broadcast_qa_mode_start(self, qa_session_id: str):
        """广播Q&A模式开始事件
        
        Args:
            qa_session_id: Q&A会话ID
        """
        await self.broadcast_event("qa_mode_start", {
            "qa_session_id": qa_session_id,
            "priority": "high",
            "mode": "qa_priority",
            "instructions": {
                "pause_main_stream": True,
                "enable_qa_priority": True,
                "clear_existing_queue": True
            }
        })
        
        logger.debug(f"🎤 Q&A mode start event broadcasted: {qa_session_id}")
    
    async def broadcast_qa_mode_end(self, qa_session_id: str):
        """广播Q&A模式结束事件
        
        Args:
            qa_session_id: Q&A会话ID
        """
        await self.broadcast_event("qa_mode_end", {
            "qa_session_id": qa_session_id,
            "priority": "normal",
            "mode": "main_stream",
            "instructions": {
                "resume_main_stream": True,
                "disable_qa_priority": True,
                "restore_normal_mode": True
            }
        })
        
        logger.debug(f"📻 Q&A mode end event broadcasted: {qa_session_id}")
            
    async def _send_to_client(self, client: WebSocket, message: Dict[str, Any]):
        """向单个客户端发送消息
        
        Args:
            client: WebSocket客户端
            message: 消息内容
        """
        try:
            await client.send_text(json.dumps(message))
        except Exception as e:
            logger.warning(f"Failed to send message to client {id(client)}: {e}")
            raise
            
    async def start_streaming(self, texts: list[str], voice_config: dict, session_id: str = None):
        """开始流式合成和广播
        
        使用新的会话管理架构，通过音频路由器进行音频传输。
        
        Args:
            texts: 要合成的文本列表
            voice_config: 音色配置
            session_id: 会话ID
        """
        session_id = session_id or f"session_{int(time.time())}"
        
        # 检查是否已有活跃的主流会话
        existing_main_session = await self.session_manager.get_main_session()
        if existing_main_session and existing_main_session.state == SessionState.ACTIVE:
            logger.warning(f"Already have active main session {existing_main_session.session_id}, terminating it")
            await self.session_manager.terminate_session(existing_main_session.session_id)
        
        # 创建新的主流会话
        try:
            main_session = await self.session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.MAIN_STREAM,
                config=voice_config
            )
            
            self.stats["streaming_sessions"] += 1
        
            # 激活主流会话
            await main_session.transition_to(SessionState.ACTIVE)
            
            # 初始化CosyVoice引擎并关联到会话
            logger.info(f"🎵 Initializing CosyVoice Unified engine with config: {voice_config}")
            tts_engine = CosyVoiceUnifiedEngine(voice_config)
            main_session.tts_engine = tts_engine
            
            logger.info(f"🚀 Starting audio streaming session {session_id} for {len(self.clients)} clients")
            
            # 广播开始事件
            await self.broadcast_event("streaming_started", {
                "session_id": session_id,
                "voice": voice_config.get("voice"),
                "format": voice_config.get("format"),
                "sample_rate": voice_config.get("sample_rate"),
                "text_count": len(texts)
            })
            
            # 开始流式合成并通过音频路由器广播
            audio_chunk_count = 0
            total_bytes_sent = 0
            
            logger.info(f"🎤 Beginning TTS synthesis and streaming for {len(texts)} text segments")
            
            async for audio_chunk in tts_engine.stream_synthesis_batch(texts):
                # 通过音频路由器路由主流音频
                success = await self.audio_router.route_main_stream_audio(
                    audio_data=audio_chunk,
                    session_id=session_id
                )
                
                if success:
                    audio_chunk_count += 1
                    total_bytes_sent += len(audio_chunk)
                
                # 定期发送进度事件 - 减少频率以降低日志噪音
                if audio_chunk_count % 500 == 0:
                    total_mb_sent = total_bytes_sent / 1024 / 1024
                    logger.info(f"🎵 Streaming progress: {audio_chunk_count} chunks sent, {total_mb_sent:.1f}MB total")
                    await self.broadcast_event("streaming_progress", {
                        "chunks_sent": audio_chunk_count,
                        "bytes_sent": total_bytes_sent,
                        "session_id": session_id
                    })
            
            # 广播结束事件
            await self.broadcast_event("streaming_finished", {
                "session_id": session_id,
                "total_chunks": audio_chunk_count,
                "total_bytes": total_bytes_sent
            })
            
            logger.info(f"✅ Streaming session {session_id} completed successfully!")
            logger.info(f"📊 Final stats: {audio_chunk_count} chunks, {total_bytes_sent} bytes sent")
            
        except Exception as e:
            logger.error(f"Streaming error in session {session_id}: {e}")
            
            # 广播错误事件
            await self.broadcast_event("streaming_error", {
                "session_id": session_id,
                "error": str(e),
                "error_type": type(e).__name__
            })
            
            raise ServiceError(f"Audio streaming failed: {str(e)}", "audio_streaming_proxy", "STREAMING_ERROR")
            
        finally:
            # 根据会话类型决定清理策略 - 修复致命缺陷：防止主会话被误终止
            session = await self.session_manager.get_session(session_id)
            if session:
                if session.session_type == SessionType.QA_STREAM:
                    # Q&A会话：立即终止和清理
                    logger.info(f"🗑️ Terminating QA session {session_id} after audio streaming completed")
                    await self.session_manager.terminate_session(session_id)
                elif session.session_type == SessionType.MAIN_STREAM:
                    # 主会话：仅标记分段完成，不终止会话
                    logger.info(f"✅ Main stream segment completed for session {session_id} - session preserved")
                    # 主会话由外部生命周期管理器控制终止，这里不能销毁
                    # 这是修复"No active main session to resume"问题的关键
                else:
                    logger.warning(f"⚠️ Unknown session type for {session_id}: {session.session_type}")
            else:
                logger.warning(f"⚠️ Session {session_id} not found in finally block - may have been terminated elsewhere")
    
    async def stop_streaming(self, session_id: str = None):
        """停止指定的流式会话
        
        Args:
            session_id: 要停止的会话ID，如果为None则停止当前活跃的主流会话
        """
        if session_id:
            # 停止指定会话
            session = await self.session_manager.get_session(session_id)
            if not session:
                logger.warning(f"Session {session_id} not found for stopping")
                return
            
            logger.info(f"Stopping streaming session {session_id}")
            
            # 广播停止事件
            await self.broadcast_event("streaming_stopped", {
                "session_id": session_id
            })
            
            # 终止会话
            await self.session_manager.terminate_session(session_id)
        else:
            # 停止当前活跃的主流会话
            main_session = await self.session_manager.get_main_session()
            if main_session:
                await self.stop_streaming(main_session.session_id)
            else:
                logger.debug("No active main session to stop")
        
    def _get_qa_voice_config(self) -> dict:
        """获取QA音频的正确配置
        
        从config.yml加载CosyVoice WebSocket配置，确保与主流使用相同的参数
        
        Returns:
            dict: 音色配置字典
        """
        try:
            # 从配置文件获取CosyVoice WebSocket配置
            ws_config = self.config.get_yaml_config("audio_streaming.cosyvoice_websocket", {})
            
            # 获取默认音色（确保是有效的v2音色ID）
            default_voice = ws_config.get("default_voice", "longanran")
            
            # 获取格式配置，应用与主引擎相同的转换逻辑
            default_format = ws_config.get("default_format", "opus")
            # 转换opus为mp3以保证MediaSource兼容性（与CosyVoiceUnifiedEngine一致）
            if default_format == "opus":
                actual_format = "mp3"
                logger.debug("Converting QA audio format from opus to mp3 for MediaSource compatibility")
            else:
                actual_format = default_format
            
            # 使用与主配置相同的采样率
            sample_rate = ws_config.get("default_sample_rate", 24000)
            
            voice_config = {
                "voice": default_voice,
                "format": actual_format,
                "sample_rate": sample_rate,
                "model": "cosyvoice-v2"  # 确保使用v2模型
            }
            
            logger.info(f"📝 QA voice config loaded: voice={default_voice}, format={actual_format}, sample_rate={sample_rate}")
            return voice_config
            
        except Exception as e:
            logger.error(f"❌ Error loading QA voice config, using fallback: {e}")
            # 失败时使用已知有效的v2配置
            fallback_config = {
                "voice": "longanran",  # 有效的v2音色
                "format": "mp3",       # MediaSource兼容格式
                "sample_rate": 24000,  # v2标准采样率
                "model": "cosyvoice-v2"
            }
            logger.warning(f"⚠️ Using fallback QA voice config: {fallback_config}")
            return fallback_config
    
    async def stream_qa_audio(self, qa_text: str, user_id: str = "system", priority: str = "high") -> str:
        """流式播放Q&A音频
        
        使用新的Q&A协调器统一处理Q&A音频流。
        
        Args:
            qa_text: Q&A回答文本
            user_id: 用户ID
            priority: 优先级（high/medium/low）
            
        Returns:
            Q&A会话ID
        """
        if not self.clients:
            logger.warning("No WebSocket clients connected for Q&A audio")
            return None
        
        try:
            # 使用Q&A协调器统一处理 - Removed in simplified architecture
            # qa_result = await self.qa_coordinator.handle_qa_request(
            #     qa_text=qa_text,
            #     user_id=user_id,
            
            # Simplified QA handling - return placeholder for now
            class SimpleResult:
                success = False
                error_message = "QA coordination removed in simplified architecture"
                
            qa_result = SimpleResult()
            
            if qa_result.success:
                logger.info(f"✅ Q&A audio streaming completed: {qa_result.qa_id}")
                return qa_result.session_id
            else:
                logger.error(f"❌ Q&A audio streaming failed: {qa_result.error_message}")
                return None
        
        except Exception as e:
            logger.error(f"❌ Q&A audio streaming failed: {e}")
            return None
    
    async def broadcast_qa_audio_binary(self, audio_data: bytes) -> bool:
        """广播Q&A音频二进制数据
        
        与主流音频广播类似，但专门用于Q&A音频，不受主流暂停状态影响。
        这是音频路由器接口的实现。
        
        Args:
            audio_data: Q&A音频数据
            
        Returns:
            bool: 是否成功广播
        """
        if not self.clients:
            logger.debug(f"No WebSocket clients for QA audio broadcast: {len(audio_data)} bytes")
            return False
            
        # 更新统计
        self.stats["total_audio_chunks"] += 1
        self.stats["total_bytes_sent"] += len(audio_data)
        self.stats["last_activity"] = time.time()
        
        # 记录QA音频发送
        self.metrics.record_audio_chunk_sent(len(audio_data))
        
        # 广播到所有客户端（与普通音频相同的广播机制）
        disconnected = set()
        successful_sends = 0
        failed_sends = 0
        
        for client in self.clients:
            try:
                # 发送音频数据，客户端通过事件知道这是QA音频
                await client.send_bytes(audio_data)
                self.metrics.record_audio_chunk_received()
                successful_sends += 1
            except WebSocketDisconnect:
                disconnected.add(client)
                self.metrics.record_connection_drop()
                failed_sends += 1
                logger.info(f"❌ Client {id(client)} disconnected during QA audio broadcast")
            except Exception as e:
                logger.warning(f"❌ Failed to send QA audio to client {id(client)}: {e}")
                disconnected.add(client)
                self.metrics.record_audio_chunk_failed()
                failed_sends += 1
        
        # 清理断开的连接
        for client in disconnected:
            await self.remove_client(client)
            
        # 日志记录
        if failed_sends > 0:
            logger.warning(f"🎤 QA audio broadcast: ✅ {successful_sends} success, ❌ {failed_sends} failed")
        # else:
        #     logger.debug(f"🎤 QA audio broadcast: ✅ {successful_sends} clients received chunk")
        
        return successful_sends > 0  # 返回是否有成功的广播
        
    async def validate_session_token(self, session_token: str) -> bool:
        """验证会话Token
        
        Args:
            session_token: 要验证的会话Token
            
        Returns:
            bool: Token是否有效
        """
        try:
            from .auth.session_token_manager import get_session_token_manager
            
            session_manager = get_session_token_manager()
            session_data = await session_manager.validate_session_token(session_token)
            
            return session_data is not None
            
        except Exception as e:
            logger.error(f"Error validating session token: {e}")
            return False

    async def handle_client_message(self, websocket: WebSocket, message: dict):
        """处理客户端消息
        
        Args:
            websocket: 发送消息的客户端
            message: 消息内容
        """
        try:
            message_type = message.get("type")
            action = message.get("action")
            data = message.get("data", {})
            
            if message_type == "auth":
                # 处理认证消息
                await self._handle_auth_message(websocket, data)
            elif message_type == "control":
                await self._handle_control_message(websocket, action, data)
            elif message_type == "ping":
                # 响应心跳
                client_id = id(websocket)
                self._client_heartbeats[client_id] = time.time()
                await self._send_to_client(websocket, {
                    "type": "pong",
                    "timestamp": time.time()
                })
                logger.trace(f"💓 Heartbeat from client {client_id}")
            elif message_type == "pong":
                # 静默处理pong消息
                pass
            elif message_type == "audio_receive_ack":
                # 处理客户端音频接收确认
                await self._handle_audio_receive_ack(websocket, data)
            elif message_type == "qa_audio_played":
                # 处理Q&A音频播放完成确认 (修复关键问题1)
                await self._handle_qa_audio_played_confirmation(websocket, message)
            else:
                logger.warning(f"Unknown message type from client {id(websocket)}: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling client message: {e}")
            
            await self._send_to_client(websocket, {
                "type": "error",
                "error": str(e),
                "error_type": type(e).__name__
            })
    
    async def _handle_auth_message(self, websocket: WebSocket, data: dict):
        """处理认证消息
        
        Args:
            websocket: 发送消息的客户端
            data: 认证数据
        """
        try:
            session_token = data.get("session_token")
            
            if not session_token:
                await self._send_to_client(websocket, {
                    "type": "auth_response",
                    "success": False,
                    "error": "Missing session token"
                })
                return
            
            # 验证会话Token
            is_valid = await self.validate_session_token(session_token)
            
            if is_valid:
                # 将已认证标记添加到客户端
                # 注意：这里可以考虑在websocket对象上添加属性来标记认证状态
                logger.info(f"Client {id(websocket)} authenticated successfully")
                
                await self._send_to_client(websocket, {
                    "type": "auth_response",
                    "success": True,
                    "message": "Authentication successful"
                })
            else:
                logger.warning(f"Client {id(websocket)} authentication failed")
                
                await self._send_to_client(websocket, {
                    "type": "auth_response",
                    "success": False,
                    "error": "Invalid or expired session token"
                })
                
        except Exception as e:
            logger.error(f"Error handling auth message: {e}")
            
            await self._send_to_client(websocket, {
                "type": "auth_response",
                "success": False,
                "error": "Authentication error"
            })
    
    async def _handle_control_message(self, websocket: WebSocket, action: str, data: dict):
        """处理控制消息
        
        Args:
            websocket: 发送消息的客户端
            action: 控制动作
            data: 控制数据
        """
        if action == "start_streaming":
            # 客户端请求开始流式播放
            texts = data.get("texts", [])
            voice_config = data.get("voice_config", {})
            session_id = data.get("session_id")
            
            if not texts:
                raise ServiceError("No texts provided for streaming", "audio_streaming_proxy", "INVALID_REQUEST")
                
            await self.start_streaming(texts, voice_config, session_id)
            
        elif action == "stop_streaming":
            # 客户端请求停止流式播放
            await self.stop_streaming()
            
        elif action == "get_status":
            # 客户端请求状态信息
            await self._send_to_client(websocket, {
                "type": "status",
                "data": self.get_status()
            })
            
        else:
            logger.warning(f"Unknown control action from client {id(websocket)}: {action}")
    
    async def _handle_qa_audio_played_confirmation(self, websocket: WebSocket, message: dict):
        """处理Q&A音频播放完成确认 (修复关键问题1)
        
        当前端发送qa_audio_played消息时，转发给QA协调器进行确认处理。
        
        Args:
            websocket: 发送确认的客户端
            message: 确认消息
        """
        try:
            qa_session_id = message.get("session_id")
            if not qa_session_id:
                logger.warning("⚠️ Q&A audio played confirmation missing session_id")
                return
            
            # 获取QA协调器实例并调用确认方法 - Removed in simplified architecture
            # from ..core.qa_coordinator import get_qa_coordinator
            # qa_coordinator = get_qa_coordinator()
            
            # confirmation_success = qa_coordinator.confirm_qa_audio_played(qa_session_id)
            confirmation_success = False  # Simplified: no QA coordination
            
            if confirmation_success:
                logger.info(f"✅ Q&A audio played confirmation processed: {qa_session_id}")
                
                # 发送确认回执给客户端
                await self._send_to_client(websocket, {
                    "type": "qa_confirmation_received",
                    "session_id": qa_session_id,
                    "status": "success"
                })
            else:
                logger.warning(f"⚠️ Q&A audio played confirmation failed: {qa_session_id}")
                
                # 发送失败回执给客户端
                await self._send_to_client(websocket, {
                    "type": "qa_confirmation_received", 
                    "session_id": qa_session_id,
                    "status": "failed",
                    "reason": "no_pending_confirmation"
                })
                
        except Exception as e:
            logger.error(f"❌ Error handling Q&A audio played confirmation: {e}")
            
            # 发送错误回执
            await self._send_to_client(websocket, {
                "type": "error",
                "error": f"Failed to process Q&A confirmation: {str(e)}",
                "error_type": "QA_CONFIRMATION_ERROR"
            })
    
    async def _handle_audio_receive_ack(self, websocket: WebSocket, data: dict):
        """处理客户端音频接收确认
        
        Args:
            websocket: 发送确认的客户端
            data: 确认数据
        """
        try:
            client_id = id(websocket)
            chunks_received = data.get("chunks_received", 0)
            bytes_received = data.get("bytes_received", 0)
            queue_size = data.get("queue_size", 0)
            is_playing = data.get("is_playing", False)
            media_source_state = data.get("media_source_state", "unknown")
            audio_element_ready = data.get("audio_element_ready", 0)
            
            # # 减少ACK日志频度，仅在特殊情况下记录详细信息
            # if chunks_received % 500 == 0 or not is_playing or queue_size > 200 or media_source_state != 'open':
            #     logger.info(f"📡 Audio receive ACK from client {client_id}:")
            #     logger.info(f"   📊 Received: {chunks_received} chunks, {bytes_received / 1024:.1f}KB")
            #     logger.info(f"   📦 Queue size: {queue_size}")
            #     logger.info(f"   🔊 Playing: {is_playing}, MediaSource: {media_source_state}, Audio ready: {audio_element_ready}")
            # else:
            #     logger.debug(f"📡 ACK from client {client_id}: {chunks_received} chunks, playing: {is_playing}")  # 移除此频繁的DEBUG日志
            
            # 更新客户端接收统计（可以存储到客户端属性中）
            if not hasattr(websocket, 'receive_stats'):
                websocket.receive_stats = {}
            
            websocket.receive_stats.update({
                'chunks_received': chunks_received,
                'bytes_received': bytes_received,
                'last_ack_time': time.time(),
                'is_playing': is_playing,
                'queue_size': queue_size,
                'media_source_state': media_source_state,
                'audio_element_ready': audio_element_ready
            })
            
            # 检查客户端状态并提供建议
            if not is_playing and chunks_received > 0:
                logger.warning(f"⚠️ Client {client_id} received {chunks_received} chunks but not playing")
            
            if queue_size > 50:  # 队列过大可能表示播放问题
                logger.warning(f"⚠️ Client {client_id} has large audio queue: {queue_size}")
            
            if media_source_state not in ['open', 'initializing']:
                logger.warning(f"⚠️ Client {client_id} MediaSource not open: {media_source_state}")
            # elif media_source_state == 'initializing':
            #     logger.debug(f"🔧 Client {client_id} MediaSource initializing (normal during startup)")
                
        except Exception as e:
            logger.error(f"Error handling audio receive ACK from client {id(websocket)}: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取代理状态
        
        使用新的会话管理架构获取状态信息。
        注意：这是同步方法，不能调用异步方法。
        
        Returns:
            状态信息字典
        """
        # 使用同步方式获取会话状态（避免 asyncio.run 错误）
        main_session = None
        is_streaming = False
        current_session_id = None
        
        if hasattr(self, 'session_manager'):
            # 直接访问内部状态，避免异步调用
            main_session_id = self.session_manager.active_main_session
            if main_session_id and main_session_id in self.session_manager.sessions:
                main_session = self.session_manager.sessions[main_session_id]
                is_streaming = main_session.state == SessionState.ACTIVE
                current_session_id = main_session.session_id
        
        status = {
            "active_connections": len(self.clients),
            "is_streaming": is_streaming,
            "current_session_id": current_session_id,
            "stats": self.stats.copy(),
            "uptime_seconds": time.time() - (self.stats.get("start_time", time.time()))
        }
        
        # 添加会话管理器状态
        if hasattr(self, 'session_manager'):
            status["session_manager"] = self.session_manager.get_status()
        
        # 添加音频路由器状态
        if hasattr(self, 'audio_router'):
            status["audio_router"] = self.audio_router.get_stats()
        
        # 添加Q&A协调器状态 - Removed in simplified architecture
        # if hasattr(self, 'qa_coordinator'):
        #     status["qa_coordinator"] = self.qa_coordinator.get_stats()
        
        # 🔥 添加幽灵播放监控指标
        if hasattr(self, 'ghost_metrics'):
            ghost_metrics = self.ghost_metrics.get_metrics()
            status["ghost_playback"] = ghost_metrics["ghost_playback"]
            status["wasted_resources"] = ghost_metrics["wasted_resources"]
            
            # 检查是否有告警
            alert_summary = self.ghost_metrics.get_alert_summary()
            if alert_summary:
                status["alerts"] = alert_summary
        
        return status
    
    async def get_status_async(self) -> Dict[str, Any]:
        """获取代理状态（异步版本）
        
        使用新的会话管理架构获取状态信息。
        可以安全地调用异步方法。
        
        Returns:
            状态信息字典
        """
        # 使用异步方法获取主流会话状态
        main_session = await self.session_manager.get_main_session() if hasattr(self, 'session_manager') else None
        is_streaming = main_session is not None and main_session.state == SessionState.ACTIVE
        current_session_id = main_session.session_id if main_session else None
        
        status = {
            "active_connections": len(self.clients),
            "is_streaming": is_streaming,
            "current_session_id": current_session_id,
            "stats": self.stats.copy(),
            "uptime_seconds": time.time() - (self.stats.get("start_time", time.time()))
        }
        
        # 添加会话管理器状态
        if hasattr(self, 'session_manager'):
            status["session_manager"] = self.session_manager.get_status()
        
        # 添加音频路由器状态
        if hasattr(self, 'audio_router'):
            status["audio_router"] = self.audio_router.get_stats()
        
        # 添加Q&A协调器状态 - Removed in simplified architecture
        # if hasattr(self, 'qa_coordinator'):
        #     status["qa_coordinator"] = self.qa_coordinator.get_stats()
        
        # 🔥 添加幽灵播放监控指标
        if hasattr(self, 'ghost_metrics'):
            ghost_metrics = self.ghost_metrics.get_metrics()
            status["ghost_playback"] = ghost_metrics["ghost_playback"]
            status["wasted_resources"] = ghost_metrics["wasted_resources"]
            
            # 检查是否有告警
            alert_summary = self.ghost_metrics.get_alert_summary()
            if alert_summary:
                status["alerts"] = alert_summary
        
        return status
    
    async def cleanup(self):
        """清理所有资源"""
        logger.info("Cleaning up audio streaming proxy")
        
        # 🔥 停止心跳监控任务
        if self._heartbeat_monitor_task:
            self._heartbeat_monitor_task.cancel()
            try:
                await self._heartbeat_monitor_task
            except asyncio.CancelledError:
                pass
            self._heartbeat_monitor_task = None
            logger.info("💓 Heartbeat monitor stopped")
        
        # 停止流式播放
        await self.stop_streaming()
        
        # 关闭所有客户端连接
        close_tasks = []
        for client in self.clients.copy():
            close_tasks.append(self._close_client(client))
            
        if close_tasks:
            await asyncio.gather(*close_tasks, return_exceptions=True)
        
        self.clients.clear()
        self._client_heartbeats.clear()
        self.stats["active_connections"] = 0
        
        logger.info("Audio streaming proxy cleanup completed")
        
    async def _close_client(self, client: WebSocket):
        """安全关闭客户端连接"""
        success = await safe_close_websocket(client, code=1001, reason="Server shutdown")
        if success:
            logger.debug(f"Client {id(client)} closed successfully")
        else:
            logger.debug(f"Error closing client {id(client)}")
    
    def is_connected(self) -> bool:
        """检查是否有客户端连接
        
        Returns:
            bool: 是否有活跃的WebSocket客户端连接
        """
        return len(self.clients) > 0
    
    async def check_connection_health(self) -> Dict[str, Any]:
        """🔥 MEDIUM PRIORITY FIX: 检查音频流连接健康状态
        
        执行全面的连接健康检查，包括WebSocket连接状态、
        会话管理器状态和TTS引擎连接状态。
        
        Returns:
            Dict containing detailed health status
        """
        health_status = {
            "overall_healthy": True,
            "timestamp": time.time(),
            "checks": {}
        }
        
        try:
            # 检查1: WebSocket客户端连接健康状态
            client_health = await self._check_websocket_clients_health()
            health_status["checks"]["websocket_clients"] = client_health
            if not client_health["healthy"]:
                health_status["overall_healthy"] = False
            
            # 检查2: 会话管理器状态
            session_health = await self._check_session_manager_health()
            health_status["checks"]["session_manager"] = session_health
            if not session_health["healthy"]:
                health_status["overall_healthy"] = False
            
            # 检查3: TTS引擎连接状态
            tts_health = await self._check_tts_engine_health()
            health_status["checks"]["tts_engine"] = tts_health
            if not tts_health["healthy"]:
                health_status["overall_healthy"] = False
            
            # 检查4: 音频路由器健康状态
            router_health = self._check_audio_router_health()
            health_status["checks"]["audio_router"] = router_health
            if not router_health["healthy"]:
                health_status["overall_healthy"] = False
            
            return health_status
            
        except Exception as e:
            logger.error(f"❌ Error during connection health check: {e}")
            health_status["overall_healthy"] = False
            health_status["error"] = str(e)
            return health_status
    
    async def _check_websocket_clients_health(self) -> Dict[str, Any]:
        """检查WebSocket客户端连接健康状态"""
        try:
            active_clients = []
            stale_clients = []
            total_clients = len(self.clients)
            
            # 检查每个WebSocket连接的实际状态
            clients_to_remove = set()
            for client in self.clients.copy():
                try:
                    # 使用工具函数检查连接状态
                    if is_websocket_connected(client):
                        active_clients.append({
                            "state": str(client.client_state.value),
                            "application_state": str(client.application_state.value) if hasattr(client, 'application_state') else "unknown"
                        })
                    else:
                        stale_clients.append("disconnected")
                        clients_to_remove.add(client)
                except Exception as client_error:
                    logger.warning(f"⚠️ Error checking client health: {client_error}")
                    stale_clients.append(f"error: {str(client_error)}")
                    clients_to_remove.add(client)
            
            # 清理断开的连接
            if clients_to_remove:
                for client in clients_to_remove:
                    self.clients.discard(client)
                self.stats["active_connections"] = len(self.clients)
                logger.info(f"🧹 Cleaned up {len(clients_to_remove)} stale WebSocket connections")
            
            return {
                "healthy": len(active_clients) > 0 and len(stale_clients) == 0,
                "total_clients": total_clients,
                "active_clients": len(active_clients),
                "stale_clients": len(stale_clients),
                "client_details": active_clients[:5],  # 限制详情数量
                "stale_details": stale_clients[:5] if stale_clients else []
            }
            
        except Exception as e:
            logger.error(f"❌ Error checking WebSocket client health: {e}")
            return {
                "healthy": False,
                "error": str(e),
                "total_clients": len(self.clients)
            }
    
    async def _check_session_manager_health(self) -> Dict[str, Any]:
        """检查会话管理器健康状态"""
        try:
            if not hasattr(self, 'session_manager') or not self.session_manager:
                return {
                    "healthy": False,
                    "error": "Session manager not initialized"
                }
            
            # 获取主流会话状态
            main_session = await self.session_manager.get_main_session()
            active_sessions = await self.session_manager.get_active_sessions()
            
            return {
                "healthy": True,
                "has_main_session": main_session is not None,
                "main_session_state": main_session.state.value if main_session else None,
                "active_session_count": len(active_sessions),
                "session_ids": [s.session_id for s in active_sessions[:5]]  # 限制数量
            }
            
        except Exception as e:
            logger.error(f"❌ Error checking session manager health: {e}")
            return {
                "healthy": False,
                "error": str(e)
            }
    
    async def _check_tts_engine_health(self) -> Dict[str, Any]:
        """检查TTS引擎连接健康状态"""
        try:
            # 获取主流会话的TTS引擎
            main_session = await self.session_manager.get_main_session() if hasattr(self, 'session_manager') else None
            
            if not main_session or not hasattr(main_session, 'tts_engine') or not main_session.tts_engine:
                return {
                    "healthy": True,  # 没有TTS引擎不一定是错误状态
                    "has_tts_engine": False,
                    "reason": "No active TTS engine (normal when not streaming)"
                }
            
            tts_engine = main_session.tts_engine
            tts_healthy = True
            tts_status = "unknown"
            
            # 检查TTS引擎的WebSocket连接状态
            if hasattr(tts_engine, 'websocket') and tts_engine.websocket:
                try:
                    if is_websocket_connected(tts_engine.websocket):
                        tts_status = "connected"
                    else:
                        tts_status = "disconnected"
                        tts_healthy = False
                except Exception:
                    tts_status = "connection_check_failed"
                    tts_healthy = False
            else:
                tts_status = "no_websocket"
                tts_healthy = False
            
            return {
                "healthy": tts_healthy,
                "has_tts_engine": True,
                "connection_status": tts_status,
                "engine_type": type(tts_engine).__name__
            }
            
        except Exception as e:
            logger.error(f"❌ Error checking TTS engine health: {e}")
            return {
                "healthy": False,
                "error": str(e)
            }
    
    def _check_audio_router_health(self) -> Dict[str, Any]:
        """检查音频路由器健康状态"""
        try:
            if not hasattr(self, 'audio_router') or not self.audio_router:
                return {
                    "healthy": False,
                    "error": "Audio router not initialized"
                }
            
            # 检查音频路由器的基本状态
            router_healthy = True
            status_info = {}
            
            # 检查路由器是否有必要的组件
            if hasattr(self.audio_router, 'broadcaster'):
                status_info["has_broadcaster"] = self.audio_router.broadcaster is not None
            else:
                status_info["has_broadcaster"] = False
                router_healthy = False
            
            return {
                "healthy": router_healthy,
                "router_type": type(self.audio_router).__name__,
                **status_info
            }
            
        except Exception as e:
            logger.error(f"❌ Error checking audio router health: {e}")
            return {
                "healthy": False,
                "error": str(e)
            }

    async def pause_for_qa(self, qa_session_id: str) -> bool:
        """为Q&A暂停直播音频流
        
        Args:
            qa_session_id: Q&A会话ID
            
        Returns:
            bool: 是否成功暂停
        """
        try:
            if not self.is_streaming:
                logger.debug("音频流未运行，无需暂停")
                return True
                
            logger.info(f"🎵 Pausing live audio stream for Q&A: {qa_session_id}")
            
            # 设置暂停状态
            self.is_paused = True
            self.paused_for_qa = True
            
            # 广播暂停事件
            await self.broadcast_event("live_audio_paused", {
                "reason": "qa_session",
                "qa_session_id": qa_session_id,
                "timestamp": time.time()
            })
            
            logger.info(f"✅ Live audio stream paused for Q&A: {qa_session_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error pausing audio stream for Q&A {qa_session_id}: {e}")
            return False

    async def resume_after_qa(self, qa_session_id: str) -> bool:
        """Q&A完成后恢复直播音频流
        
        Args:
            qa_session_id: Q&A会话ID
            
        Returns:
            bool: 是否成功恢复
        """
        try:
            if not self.paused_for_qa:
                logger.debug("音频流未因Q&A暂停，无需恢复")
                return True
                
            logger.info(f"🎵 Resuming live audio stream after Q&A: {qa_session_id}")
            
            # 清除暂停状态
            self.is_paused = False
            self.paused_for_qa = False
            
            # 广播恢复事件
            await self.broadcast_event("live_audio_resumed", {
                "reason": "qa_completed",
                "qa_session_id": qa_session_id,
                "timestamp": time.time()
            })
            
            logger.info(f"✅ Live audio stream resumed after Q&A: {qa_session_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error resuming audio stream after Q&A {qa_session_id}: {e}")
            return False
    
    def _is_client_connected(self, client: WebSocket) -> bool:
        """检查客户端是否仍然连接
        
        Args:
            client: WebSocket连接
            
        Returns:
            bool: 连接状态
        """
        return is_websocket_connected(client)
    
    async def _notify_all_clients_disconnected(self):
        """通知所有客户端都已断开连接
        
        这个方法在最后一个客户端断开时被调用，
        可以用来通知 playlist_manager 暂停播放。
        """
        try:
            logger.info("🔔 Notifying system: all clients have disconnected")
            
            # 获取 playlist_manager 并通知暂停
            try:
                from ..core.dependencies import get_playlist_manager
                playlist_manager = get_playlist_manager()
                if playlist_manager:
                    await playlist_manager.pause_due_to_no_clients()
                    logger.info("✅ Playlist manager notified about client disconnection")
            except ImportError:
                logger.warning("Failed to import playlist manager dependencies")
            except Exception as e:
                logger.error(f"Failed to notify playlist manager: {e}")
            
        except Exception as e:
            logger.error(f"Failed to notify about all clients disconnected: {e}")
    
    async def stream_chunk(self, audio_data: bytes) -> bool:
        """流式传输音频块 - 兼容接口
        
        这是为了兼容现有代码的接口，内部调用 broadcast_audio_binary。
        
        Args:
            audio_data: 音频数据
            
        Returns:
            bool: 是否成功
            
        Raises:
            NoClientsConnectedError: 当没有客户端连接时抛出
        """
        return await self.broadcast_audio_binary(audio_data)
    
    async def _monitor_heartbeats(self):
        """监控客户端心跳
        
        定期检查客户端心跳，断开超时的连接。
        """
        logger.info("💓 Heartbeat monitor task started")
        
        try:
            # 等待一小段时间，确保客户端完全连接
            await asyncio.sleep(1.0)
            logger.debug("💓 Heartbeat monitor initial delay completed")
            
            while True:
                await asyncio.sleep(self.heartbeat_interval)
                
                # 检查所有客户端的心跳
                current_time = time.time()
                timeout_clients = []
                
                for client in self.clients.copy():
                    client_id = id(client)
                    last_heartbeat = self._client_heartbeats.get(client_id, 0)
                    
                    if current_time - last_heartbeat > self.heartbeat_timeout:
                        timeout_clients.append(client)
                        logger.warning(f"💔 Client {client_id} heartbeat timeout "
                                     f"({current_time - last_heartbeat:.1f}s)")
                
                # 断开超时的客户端
                for client in timeout_clients:
                    try:
                        # 记录心跳超时
                        from ..exceptions.client_connection_errors import ClientHeartbeatTimeoutError
                        error = ClientHeartbeatTimeoutError(
                            client_id=str(id(client)),
                            timeout_seconds=self.heartbeat_timeout,
                            last_heartbeat_time=self._client_heartbeats.get(id(client))
                        )
                        logger.error(f"Disconnecting client due to heartbeat timeout: {error}")
                        
                        # 断开连接
                        await safe_close_websocket(client, code=1001, reason="Heartbeat timeout")
                        await self.remove_client(client)
                    except Exception as e:
                        logger.error(f"Error disconnecting timeout client: {e}")
                
                # 定期发送心跳请求
                if len(self.clients) > 0:
                    await self.broadcast_event("ping", {"timestamp": current_time})
                    logger.trace(f"💓 Sent heartbeat ping to {len(self.clients)} clients")
                
        except asyncio.CancelledError:
            logger.info("💓 Heartbeat monitor task cancelled")
            raise
        except Exception as e:
            logger.error(f"💔 Heartbeat monitor error: {e}")
            # 重启监控任务
            self._heartbeat_monitor_task = asyncio.create_task(self._monitor_heartbeats())


# 全局代理实例
audio_proxy = AudioStreamingProxy()


def get_audio_proxy() -> AudioStreamingProxy:
    """获取全局音频流代理实例
    
    Returns:
        AudioStreamingProxy实例
    """
    return audio_proxy


async def cleanup_audio_proxy():
    """清理全局音频流代理"""
    await audio_proxy.cleanup()