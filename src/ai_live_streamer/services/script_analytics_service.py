"""
Script Analytics Service

Advanced analytics and reporting for script generation, usage, and performance
with real-time metrics, trend analysis, and actionable insights.
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
from loguru import logger

from ..models.script_persistence import (
    ScriptAnalytics, GenerationMethod, ScriptStatus,
    PersistedScript, StreamingSession
)
from ..services.script_repository import ScriptRepository
from ..core.config import cfg


class MetricPeriod(Enum):
    """Time periods for analytics"""
    HOUR = "hour"
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"


class TrendDirection(Enum):
    """Trend direction indicators"""
    UP = "up"
    DOWN = "down"
    STABLE = "stable"
    VOLATILE = "volatile"


@dataclass
class MetricTrend:
    """Trend analysis for a metric"""
    current_value: float
    previous_value: float
    change_percent: float
    direction: TrendDirection
    significance: str  # "high", "medium", "low"
    
    @property
    def is_improving(self) -> bool:
        """Whether the trend is improving (context-dependent)"""
        return self.direction == TrendDirection.UP
    
    @property
    def change_description(self) -> str:
        """Human-readable change description"""
        if abs(self.change_percent) < 5:
            return "stable"
        elif self.change_percent > 0:
            return f"up {self.change_percent:.1f}%"
        else:
            return f"down {abs(self.change_percent):.1f}%"


@dataclass
class PerformanceInsight:
    """Performance insight with actionable recommendations"""
    category: str  # "generation", "usage", "quality", "engagement"
    title: str
    description: str
    severity: str  # "info", "warning", "critical"
    recommendations: List[str]
    impact_score: float  # 0.0 to 1.0
    data_points: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ComparativeAnalysis:
    """Comparative analysis between different periods or segments"""
    baseline_period: str
    comparison_period: str
    metrics_comparison: Dict[str, MetricTrend]
    insights: List[PerformanceInsight]
    overall_performance_change: float


class ScriptAnalyticsService:
    """
    Advanced analytics service for script performance analysis,
    trend detection, and predictive insights.
    """
    
    def __init__(self, repository: ScriptRepository):
        self.repository = repository
        self.cache_ttl_seconds = cfg.get('analytics.cache_ttl', 300)  # 5 minutes
        self._analytics_cache: Dict[str, Tuple[datetime, Any]] = {}
        
        # Analytics configuration
        self.config = {
            "trend_sensitivity": cfg.get('analytics.trend_sensitivity', 0.1),
            "significance_threshold": cfg.get('analytics.significance_threshold', 0.05),
            "quality_benchmark": cfg.get('analytics.quality_benchmark', 0.7),
            "engagement_benchmark": cfg.get('analytics.engagement_benchmark', 0.6),
            "usage_benchmark": cfg.get('analytics.usage_benchmark', 0.3)
        }
        
        logger.info("Script Analytics Service initialized")
    
    # === Core Analytics Methods ===
    
    async def get_comprehensive_analytics(
        self, 
        days: int = 30,
        include_trends: bool = True,
        include_insights: bool = True,
        cache_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get comprehensive analytics with trends and insights
        
        Args:
            days: Analysis period in days
            include_trends: Include trend analysis
            include_insights: Include performance insights
            cache_key: Optional cache key for caching results
            
        Returns:
            Comprehensive analytics dictionary
        """
        try:
            # Check cache first
            if cache_key:
                cached_result = self._get_cached_analytics(cache_key)
                if cached_result:
                    return cached_result
            
            logger.debug(f"Generating comprehensive analytics for {days} days")
            
            # Get base analytics
            base_analytics = await self.repository.get_analytics(days)
            
            # Build comprehensive response
            result = {
                "period_days": days,
                "generated_at": datetime.now(),
                "summary": self._build_analytics_summary(base_analytics),
                "generation_analytics": self._build_generation_analytics(base_analytics),
                "usage_analytics": self._build_usage_analytics(base_analytics),
                "quality_analytics": self._build_quality_analytics(base_analytics),
                "engagement_analytics": self._build_engagement_analytics(base_analytics)
            }
            
            # Add trend analysis
            if include_trends:
                trends = await self._analyze_trends(days)
                result["trends"] = trends
            
            # Add performance insights
            if include_insights:
                insights = await self._generate_performance_insights(base_analytics, days)
                result["insights"] = insights
            
            # Cache results
            if cache_key:
                self._cache_analytics(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Comprehensive analytics failed: {e}")
            return self._get_empty_analytics_response()
    
    async def get_comparative_analysis(
        self,
        current_days: int = 30,
        comparison_days: int = 30
    ) -> ComparativeAnalysis:
        """
        Compare performance between two periods
        
        Args:
            current_days: Current period length
            comparison_days: Comparison period length
            
        Returns:
            Comparative analysis results
        """
        try:
            logger.debug(f"Generating comparative analysis: {current_days} vs {comparison_days} days")
            
            # Get analytics for both periods
            current_analytics = await self.repository.get_analytics(current_days)
            
            # Calculate comparison period start date
            comparison_start = datetime.now() - timedelta(days=current_days + comparison_days)
            comparison_end = datetime.now() - timedelta(days=current_days)
            
            # Get comparison analytics (would need additional repository method)
            # For now, simulate with adjusted current analytics
            comparison_analytics = await self._get_period_analytics(comparison_start, comparison_end)
            
            # Compare key metrics
            metrics_comparison = {
                "script_generation": self._compare_metric(
                    current_analytics.total_scripts,
                    comparison_analytics.get("total_scripts", 0),
                    higher_is_better=True
                ),
                "average_quality": self._compare_metric(
                    current_analytics.average_quality_score,
                    comparison_analytics.get("average_quality_score", 0),
                    higher_is_better=True
                ),
                "engagement_rate": self._compare_metric(
                    current_analytics.average_engagement_score,
                    comparison_analytics.get("average_engagement_score", 0),
                    higher_is_better=True
                ),
                "generation_efficiency": self._compare_metric(
                    current_analytics.generation_efficiency_score,
                    comparison_analytics.get("generation_efficiency_score", 0),
                    higher_is_better=True
                ),
                "content_utilization": self._compare_metric(
                    current_analytics.content_utilization_rate,
                    comparison_analytics.get("content_utilization_rate", 0),
                    higher_is_better=True
                )
            }
            
            # Generate insights from comparison
            insights = self._generate_comparative_insights(metrics_comparison)
            
            # Calculate overall performance change
            performance_scores = [trend.change_percent for trend in metrics_comparison.values()]
            overall_change = sum(performance_scores) / len(performance_scores)
            
            return ComparativeAnalysis(
                baseline_period=f"Last {comparison_days} days",
                comparison_period=f"Current {current_days} days",
                metrics_comparison=metrics_comparison,
                insights=insights,
                overall_performance_change=overall_change
            )
            
        except Exception as e:
            logger.error(f"Comparative analysis failed: {e}")
            return self._get_empty_comparative_analysis()
    
    async def get_script_performance_ranking(
        self,
        limit: int = 50,
        metric: str = "overall"
    ) -> List[Dict[str, Any]]:
        """
        Get ranked list of scripts by performance metrics
        
        Args:
            limit: Maximum number of scripts to return
            metric: Ranking metric ("overall", "quality", "engagement", "usage")
            
        Returns:
            List of ranked scripts with performance data
        """
        try:
            logger.debug(f"Generating script performance ranking (top {limit}, metric: {metric})")
            
            # Get recent scripts with usage data
            from ..models.script_persistence import ScriptQuery
            
            query = ScriptQuery(
                limit=limit * 2,  # Get more to filter and rank
                order_by="generated_at",
                order_desc=True
            )
            
            scripts, _ = await self.repository.search_scripts(query)
            
            # Calculate performance scores for each script
            script_rankings = []
            for script in scripts:
                performance_score = await self._calculate_script_performance_score(script, metric)
                
                script_rankings.append({
                    "script_id": script.script_id,
                    "form_id": script.form_id,
                    "title": script.form_snapshot.get('basic_information', {}).get('stream_title', 'Untitled'),
                    "generated_at": script.generated_at,
                    "generation_method": script.generation_method.value,
                    "performance_score": performance_score,
                    "quality_score": script.quality_score or 0.0,
                    "usage_count": script.usage_count,
                    "last_used_at": script.last_used_at,
                    "metrics": {
                        "duration_seconds": script.total_duration_seconds,
                        "segment_count": script.segment_count,
                        "tags": script.tags
                    }
                })
            
            # Sort by performance score and limit
            script_rankings.sort(key=lambda x: x["performance_score"], reverse=True)
            return script_rankings[:limit]
            
        except Exception as e:
            logger.error(f"Performance ranking failed: {e}")
            return []
    
    async def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get real-time metrics for live monitoring"""
        try:
            # Get recent activity (last 24 hours)
            cutoff_time = datetime.now() - timedelta(hours=24)
            
            query = ScriptQuery(
                created_after=cutoff_time,
                limit=1000
            )
            
            recent_scripts, total_count = await self.repository.search_scripts(query)
            
            # Calculate real-time metrics
            generation_count_24h = len(recent_scripts)
            
            # LLM vs Template usage
            llm_count = sum(1 for s in recent_scripts if s.generation_method == GenerationMethod.LLM)
            template_count = generation_count_24h - llm_count
            
            # Average generation time
            avg_generation_time = 0
            if recent_scripts:
                avg_generation_time = sum(s.generation_time_ms for s in recent_scripts) / len(recent_scripts)
            
            # Quality distribution
            quality_scores = [s.quality_score for s in recent_scripts if s.quality_score is not None]
            avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
            
            # Active scripts (used in last 24h)
            active_scripts = sum(1 for s in recent_scripts if s.last_used_at and s.last_used_at > cutoff_time)
            
            return {
                "timestamp": datetime.now(),
                "period": "24_hours",
                "metrics": {
                    "scripts_generated": generation_count_24h,
                    "average_generation_time_ms": avg_generation_time,
                    "average_quality_score": avg_quality,
                    "active_scripts": active_scripts,
                    "generation_methods": {
                        "llm": llm_count,
                        "template": template_count
                    }
                },
                "health_indicators": {
                    "generation_rate": generation_count_24h / 24,  # per hour
                    "quality_threshold_met": avg_quality >= self.config["quality_benchmark"],
                    "system_active": generation_count_24h > 0
                }
            }
            
        except Exception as e:
            logger.error(f"Real-time metrics failed: {e}")
            return {"error": str(e), "timestamp": datetime.now()}
    
    # === Trend Analysis Methods ===
    
    async def _analyze_trends(self, days: int) -> Dict[str, Any]:
        """Analyze trends over the specified period"""
        try:
            # Get analytics for current and previous periods
            current_analytics = await self.repository.get_analytics(days)
            previous_analytics = await self._get_previous_period_analytics(days)
            
            # Analyze key trend metrics
            trends = {
                "generation_trend": self._analyze_trend(
                    current_analytics.generation_trend_7d,
                    "Script Generation"
                ),
                "usage_trend": self._analyze_trend(
                    current_analytics.usage_trend_7d,
                    "Script Usage"
                ),
                "quality_trend": self._analyze_trend(
                    current_analytics.quality_trend_7d,
                    "Quality Score"
                ),
                "efficiency_trend": self._calculate_efficiency_trend(current_analytics, previous_analytics)
            }
            
            # Overall trend summary
            trends["summary"] = self._summarize_trends(trends)
            
            return trends
            
        except Exception as e:
            logger.error(f"Trend analysis failed: {e}")
            return {"error": str(e)}
    
    def _analyze_trend(self, data_points: List[Union[int, float]], metric_name: str) -> Dict[str, Any]:
        """Analyze trend from a series of data points"""
        if len(data_points) < 2:
            return {"direction": "stable", "confidence": "low", "description": "Insufficient data"}
        
        # Calculate trend direction
        recent_avg = sum(data_points[-3:]) / min(3, len(data_points))
        earlier_avg = sum(data_points[:3]) / min(3, len(data_points))
        
        change_percent = ((recent_avg - earlier_avg) / max(earlier_avg, 1)) * 100
        
        # Determine direction
        if abs(change_percent) < 5:
            direction = TrendDirection.STABLE
        elif change_percent > 0:
            direction = TrendDirection.UP
        else:
            direction = TrendDirection.DOWN
        
        # Calculate volatility
        if len(data_points) > 1:
            differences = [abs(data_points[i] - data_points[i-1]) for i in range(1, len(data_points))]
            volatility = sum(differences) / len(differences)
            is_volatile = volatility > (sum(data_points) / len(data_points)) * 0.3
        else:
            is_volatile = False
        
        return {
            "metric_name": metric_name,
            "direction": direction.value,
            "change_percent": change_percent,
            "is_volatile": is_volatile,
            "confidence": "high" if abs(change_percent) > 15 else "medium" if abs(change_percent) > 5 else "low",
            "data_points": data_points,
            "description": f"{metric_name} is {direction.value} by {abs(change_percent):.1f}%"
        }
    
    # === Performance Insights Methods ===
    
    async def _generate_performance_insights(
        self, 
        analytics: ScriptAnalytics, 
        days: int
    ) -> List[PerformanceInsight]:
        """Generate actionable performance insights"""
        insights = []
        
        # Generation efficiency insights
        if analytics.average_generation_time_ms > 10000:  # > 10 seconds
            insights.append(PerformanceInsight(
                category="generation",
                title="Slow Script Generation",
                description=f"Average generation time is {analytics.average_generation_time_ms/1000:.1f} seconds, which is above optimal range.",
                severity="warning",
                recommendations=[
                    "Consider optimizing LLM prompts for faster responses",
                    "Review template generation efficiency",
                    "Check for performance bottlenecks in generation pipeline"
                ],
                impact_score=0.7,
                data_points={"avg_time_ms": analytics.average_generation_time_ms}
            ))
        
        # Quality insights
        if analytics.average_quality_score < self.config["quality_benchmark"]:
            insights.append(PerformanceInsight(
                category="quality",
                title="Below Average Script Quality",
                description=f"Quality score {analytics.average_quality_score:.2f} is below benchmark {self.config['quality_benchmark']:.2f}",
                severity="warning",
                recommendations=[
                    "Review and improve script generation prompts",
                    "Implement additional quality validation rules",
                    "Analyze low-quality scripts for common patterns"
                ],
                impact_score=0.8,
                data_points={"current_score": analytics.average_quality_score, "benchmark": self.config["quality_benchmark"]}
            ))
        
        # Usage insights
        if analytics.scripts_reuse_rate < self.config["usage_benchmark"]:
            insights.append(PerformanceInsight(
                category="usage",
                title="Low Script Reuse Rate",
                description=f"Scripts are being reused only {analytics.scripts_reuse_rate:.1%}, indicating potential relevance issues.",
                severity="info",
                recommendations=[
                    "Analyze characteristics of frequently reused scripts",
                    "Improve script discoverability and search",
                    "Consider script versioning and updates"
                ],
                impact_score=0.6,
                data_points={"reuse_rate": analytics.scripts_reuse_rate, "benchmark": self.config["usage_benchmark"]}
            ))
        
        # Engagement insights
        if analytics.average_engagement_score < self.config["engagement_benchmark"]:
            insights.append(PerformanceInsight(
                category="engagement",
                title="Low Engagement Scores",
                description=f"Average engagement {analytics.average_engagement_score:.2f} is below target {self.config['engagement_benchmark']:.2f}",
                severity="warning",
                recommendations=[
                    "Increase interactive segments in scripts",
                    "Improve call-to-action effectiveness",
                    "Analyze high-engagement scripts for best practices"
                ],
                impact_score=0.9,
                data_points={"current_engagement": analytics.average_engagement_score, "target": self.config["engagement_benchmark"]}
            ))
        
        # Positive insights
        if analytics.generation_efficiency_score > 0.8:
            insights.append(PerformanceInsight(
                category="generation",
                title="Excellent Generation Efficiency",
                description="Script generation is performing very well with fast response times.",
                severity="info",
                recommendations=[
                    "Document current configuration as best practice",
                    "Consider scaling current approach"
                ],
                impact_score=0.5,
                data_points={"efficiency_score": analytics.generation_efficiency_score}
            ))
        
        return insights
    
    # === Helper Methods ===
    
    def _build_analytics_summary(self, analytics: ScriptAnalytics) -> Dict[str, Any]:
        """Build high-level analytics summary"""
        return {
            "total_scripts": analytics.total_scripts,
            "total_sessions": analytics.total_sessions,
            "total_usage_hours": analytics.total_usage_minutes / 60,
            "overall_health_score": self._calculate_overall_health_score(analytics),
            "key_metrics": {
                "generation_efficiency": analytics.generation_efficiency_score,
                "content_utilization": analytics.content_utilization_rate,
                "user_satisfaction": analytics.user_satisfaction_score
            }
        }
    
    def _build_generation_analytics(self, analytics: ScriptAnalytics) -> Dict[str, Any]:
        """Build generation-specific analytics"""
        return {
            "total_generated": analytics.total_scripts,
            "methods_breakdown": {k.value: v for k, v in analytics.scripts_by_method.items()},
            "average_time_ms": analytics.average_generation_time_ms,
            "success_rate": analytics.generation_success_rate,
            "efficiency_score": analytics.generation_efficiency_score,
            "trend": analytics.generation_trend_7d
        }
    
    def _build_usage_analytics(self, analytics: ScriptAnalytics) -> Dict[str, Any]:
        """Build usage-specific analytics"""
        return {
            "total_sessions": analytics.total_sessions,
            "total_minutes": analytics.total_usage_minutes,
            "average_session_duration": analytics.average_session_duration_minutes,
            "reuse_rate": analytics.scripts_reuse_rate,
            "utilization_rate": analytics.content_utilization_rate,
            "trend": analytics.usage_trend_7d
        }
    
    def _build_quality_analytics(self, analytics: ScriptAnalytics) -> Dict[str, Any]:
        """Build quality-specific analytics"""
        return {
            "average_score": analytics.average_quality_score,
            "distribution": analytics.quality_distribution,
            "top_performers": analytics.top_performing_scripts,
            "benchmark_comparison": {
                "current": analytics.average_quality_score,
                "benchmark": self.config["quality_benchmark"],
                "meets_benchmark": analytics.average_quality_score >= self.config["quality_benchmark"]
            },
            "trend": analytics.quality_trend_7d
        }
    
    def _build_engagement_analytics(self, analytics: ScriptAnalytics) -> Dict[str, Any]:
        """Build engagement-specific analytics"""
        return {
            "average_engagement": analytics.average_engagement_score,
            "interaction_rate": analytics.average_interaction_rate,
            "retention_rate": analytics.average_retention_rate,
            "satisfaction_score": analytics.user_satisfaction_score,
            "benchmark_comparison": {
                "current": analytics.average_engagement_score,
                "benchmark": self.config["engagement_benchmark"],
                "meets_benchmark": analytics.average_engagement_score >= self.config["engagement_benchmark"]
            }
        }
    
    def _calculate_overall_health_score(self, analytics: ScriptAnalytics) -> float:
        """Calculate overall system health score"""
        factors = [
            analytics.generation_efficiency_score,
            analytics.content_utilization_rate,
            analytics.user_satisfaction_score,
            min(1.0, analytics.average_quality_score / self.config["quality_benchmark"]),
            min(1.0, analytics.scripts_reuse_rate / self.config["usage_benchmark"])
        ]
        
        return sum(factors) / len(factors)
    
    async def _calculate_script_performance_score(self, script: PersistedScript, metric: str) -> float:
        """Calculate performance score for a single script"""
        try:
            if metric == "quality":
                return script.quality_score or 0.0
            elif metric == "usage":
                # Normalize usage count (assume max 50 uses is excellent)
                return min(1.0, script.usage_count / 50.0)
            elif metric == "engagement":
                # Would need session data - simplified for now
                return 0.7  # Default engagement score
            else:  # overall
                quality_score = script.quality_score or 0.0
                usage_score = min(1.0, script.usage_count / 10.0)
                recency_score = self._calculate_recency_score(script.generated_at)
                
                return (quality_score * 0.4 + usage_score * 0.4 + recency_score * 0.2)
                
        except Exception as e:
            logger.warning(f"Performance score calculation failed for {script.script_id}: {e}")
            return 0.0
    
    def _calculate_recency_score(self, generated_at: datetime) -> float:
        """Calculate recency score (newer scripts get higher scores)"""
        days_old = (datetime.now() - generated_at).days
        if days_old <= 7:
            return 1.0
        elif days_old <= 30:
            return 0.8
        elif days_old <= 90:
            return 0.6
        else:
            return 0.4
    
    def _compare_metric(self, current: float, previous: float, higher_is_better: bool = True) -> MetricTrend:
        """Compare two metric values and return trend analysis"""
        if previous == 0:
            change_percent = 0.0 if current == 0 else 100.0
        else:
            change_percent = ((current - previous) / previous) * 100
        
        # Determine direction
        if abs(change_percent) < 5:
            direction = TrendDirection.STABLE
        elif change_percent > 0:
            direction = TrendDirection.UP if higher_is_better else TrendDirection.DOWN
        else:
            direction = TrendDirection.DOWN if higher_is_better else TrendDirection.UP
        
        # Determine significance
        if abs(change_percent) > 20:
            significance = "high"
        elif abs(change_percent) > 10:
            significance = "medium"
        else:
            significance = "low"
        
        return MetricTrend(
            current_value=current,
            previous_value=previous,
            change_percent=change_percent,
            direction=direction,
            significance=significance
        )
    
    def _generate_comparative_insights(self, metrics_comparison: Dict[str, MetricTrend]) -> List[PerformanceInsight]:
        """Generate insights from comparative analysis"""
        insights = []
        
        # Find metrics with significant changes
        for metric_name, trend in metrics_comparison.items():
            if trend.significance == "high":
                if trend.direction == TrendDirection.UP:
                    insights.append(PerformanceInsight(
                        category="improvement",
                        title=f"Significant Improvement in {metric_name.replace('_', ' ').title()}",
                        description=f"{metric_name.replace('_', ' ').title()} has improved by {trend.change_percent:.1f}%",
                        severity="info",
                        recommendations=[
                            f"Analyze what contributed to {metric_name} improvement",
                            "Document and replicate successful practices"
                        ],
                        impact_score=0.7,
                        data_points={"trend": trend.change_percent}
                    ))
                else:
                    insights.append(PerformanceInsight(
                        category="decline",
                        title=f"Significant Decline in {metric_name.replace('_', ' ').title()}",
                        description=f"{metric_name.replace('_', ' ').title()} has declined by {abs(trend.change_percent):.1f}%",
                        severity="warning",
                        recommendations=[
                            f"Investigate causes of {metric_name} decline",
                            "Implement corrective measures immediately"
                        ],
                        impact_score=0.8,
                        data_points={"trend": trend.change_percent}
                    ))
        
        return insights
    
    # === Cache Management ===
    
    def _get_cached_analytics(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached analytics if still valid"""
        if cache_key in self._analytics_cache:
            cached_time, cached_data = self._analytics_cache[cache_key]
            if (datetime.now() - cached_time).total_seconds() < self.cache_ttl_seconds:
                logger.debug(f"Using cached analytics for key: {cache_key}")
                return cached_data
        return None
    
    def _cache_analytics(self, cache_key: str, data: Dict[str, Any]) -> None:
        """Cache analytics data"""
        self._analytics_cache[cache_key] = (datetime.now(), data)
        
        # Clean old cache entries
        cutoff_time = datetime.now() - timedelta(seconds=self.cache_ttl_seconds * 2)
        self._analytics_cache = {
            k: v for k, v in self._analytics_cache.items()
            if v[0] > cutoff_time
        }
    
    # === Placeholder Methods (would need additional repository methods) ===
    
    async def _get_previous_period_analytics(self, days: int) -> ScriptAnalytics:
        """Get analytics for previous period (placeholder)"""
        # Would need repository method to get analytics for specific date range
        return await self.repository.get_analytics(days)
    
    async def _get_period_analytics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get analytics for specific period (placeholder)"""
        # Simplified implementation - would need enhanced repository methods
        current_analytics = await self.repository.get_analytics(30)
        return {
            "total_scripts": max(0, current_analytics.total_scripts - 10),
            "average_quality_score": max(0, current_analytics.average_quality_score - 0.1),
            "average_engagement_score": max(0, current_analytics.average_engagement_score - 0.05),
            "generation_efficiency_score": max(0, current_analytics.generation_efficiency_score - 0.1),
            "content_utilization_rate": max(0, current_analytics.content_utilization_rate - 0.05)
        }
    
    def _calculate_efficiency_trend(self, current: ScriptAnalytics, previous: ScriptAnalytics) -> Dict[str, Any]:
        """Calculate efficiency trend (placeholder)"""
        return {
            "direction": "stable",
            "change_percent": 0.0,
            "description": "Efficiency trend analysis requires historical data"
        }
    
    def _summarize_trends(self, trends: Dict[str, Any]) -> Dict[str, Any]:
        """Summarize all trends into overall assessment"""
        positive_trends = sum(1 for t in trends.values() if isinstance(t, dict) and t.get("direction") == "up")
        negative_trends = sum(1 for t in trends.values() if isinstance(t, dict) and t.get("direction") == "down")
        
        if positive_trends > negative_trends:
            overall = "improving"
        elif negative_trends > positive_trends:
            overall = "declining"
        else:
            overall = "stable"
        
        return {
            "overall_direction": overall,
            "positive_trends": positive_trends,
            "negative_trends": negative_trends,
            "stable_trends": len(trends) - positive_trends - negative_trends
        }
    
    # === Default/Empty Responses ===
    
    def _get_empty_analytics_response(self) -> Dict[str, Any]:
        """Return empty analytics response"""
        return {
            "error": "Analytics generation failed",
            "period_days": 0,
            "generated_at": datetime.now(),
            "summary": {"total_scripts": 0, "total_sessions": 0},
            "trends": {},
            "insights": []
        }
    
    def _get_empty_comparative_analysis(self) -> ComparativeAnalysis:
        """Return empty comparative analysis"""
        return ComparativeAnalysis(
            baseline_period="unknown",
            comparison_period="unknown",
            metrics_comparison={},
            insights=[],
            overall_performance_change=0.0
        )