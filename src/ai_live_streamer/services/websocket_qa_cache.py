"""WebSocket关联QA缓存管理器 - 智能去重和状态同步

实现WebSocket生命周期绑定的QA请求缓存，用于：
1. 请求去重：防止相同问题重复处理
2. 状态同步：实时推送QA处理状态到前端
3. 结果缓存：已完成的QA结果可快速返回

Author: Claude Code
Date: 2025-01-08 (QA修复专用)
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger
import hashlib
import json


class QARequestStatus(Enum):
    """QA请求状态枚举"""
    PENDING = "pending"           # 待处理
    PROCESSING = "processing"     # 处理中
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"            # 处理失败
    EXPIRED = "expired"          # 已过期


@dataclass
class CachedQARequest:
    """缓存的QA请求"""
    request_id: str
    question_hash: str
    question_text: str
    session_id: str
    websocket_clients: Set[str] = field(default_factory=set)  # 关联的WebSocket客户端
    status: QARequestStatus = QARequestStatus.PENDING
    
    # 时间戳
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    completed_at: Optional[float] = None
    
    # 处理结果
    answer: Optional[str] = None
    qa_id: Optional[str] = None
    trace_id: Optional[str] = None
    error_message: Optional[str] = None
    
    # 处理指标
    processing_metrics: Dict[str, Any] = field(default_factory=dict)
    
    # 配置
    ttl_seconds: int = 300  # 5分钟TTL
    
    def is_expired(self) -> bool:
        """检查是否已过期"""
        return time.time() - self.created_at > self.ttl_seconds
    
    def add_websocket_client(self, client_id: str):
        """添加关联的WebSocket客户端"""
        self.websocket_clients.add(client_id)
        self.updated_at = time.time()
    
    def remove_websocket_client(self, client_id: str):
        """移除关联的WebSocket客户端"""
        self.websocket_clients.discard(client_id)
        self.updated_at = time.time()
    
    def update_status(self, status: QARequestStatus, **kwargs):
        """更新请求状态"""
        self.status = status
        self.updated_at = time.time()
        
        if status == QARequestStatus.COMPLETED:
            self.completed_at = time.time()
        
        # 更新其他字段
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_status_dict(self) -> Dict[str, Any]:
        """转换为状态字典（用于WebSocket推送）"""
        return {
            "request_id": self.request_id,
            "question_hash": self.question_hash,
            "question_text": self.question_text,
            "session_id": self.session_id,
            "status": self.status.value,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "completed_at": self.completed_at,
            "answer": self.answer,
            "qa_id": self.qa_id,
            "trace_id": self.trace_id,
            "error_message": self.error_message,
            "processing_metrics": self.processing_metrics,
            "client_count": len(self.websocket_clients)
        }


class WebSocketQACache:
    """WebSocket关联的QA缓存管理器"""
    
    def __init__(self):
        """初始化QA缓存管理器"""
        # 缓存存储
        self.cached_requests: Dict[str, CachedQARequest] = {}  # request_id -> CachedQARequest
        self.question_hash_index: Dict[str, str] = {}  # question_hash -> request_id
        self.session_index: Dict[str, Set[str]] = {}  # session_id -> Set[request_id]
        self.client_index: Dict[str, Set[str]] = {}   # client_id -> Set[request_id]
        
        # WebSocket连接管理
        self.active_websockets: Dict[str, Any] = {}  # client_id -> WebSocket连接
        
        # 清理任务
        self.cleanup_task: Optional[asyncio.Task] = None
        self.cleanup_interval = 60  # 60秒清理一次
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "expired_cleaned": 0,
            "websocket_notifications": 0
        }
        
        logger.info("📋 WebSocketQACache initialized")
    
    def start_cleanup_task(self):
        """启动清理任务"""
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("🧹 QA缓存清理任务已启动")
    
    async def stop_cleanup_task(self):
        """停止清理任务"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            self.cleanup_task = None
            logger.info("🧹 QA缓存清理任务已停止")
    
    def generate_question_hash(self, session_id: str, question_text: str) -> str:
        """生成问题哈希（用于去重）"""
        content = f"{session_id}:{question_text.strip().lower()}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def register_websocket_client(self, client_id: str, websocket: Any):
        """注册WebSocket客户端"""
        self.active_websockets[client_id] = websocket
        logger.debug(f"📱 WebSocket客户端已注册: {client_id}")
    
    def unregister_websocket_client(self, client_id: str):
        """注销WebSocket客户端"""
        # 从活跃WebSocket中移除
        self.active_websockets.pop(client_id, None)
        
        # 从所有缓存请求中移除此客户端
        if client_id in self.client_index:
            request_ids = list(self.client_index[client_id])
            for request_id in request_ids:
                if request_id in self.cached_requests:
                    self.cached_requests[request_id].remove_websocket_client(client_id)
            self.client_index.pop(client_id, None)
        
        logger.debug(f"📱 WebSocket客户端已注销: {client_id}")
    
    async def submit_qa_request(
        self,
        session_id: str,
        question_text: str,
        client_id: str,
        request_id: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """提交QA请求（带智能去重）
        
        Args:
            session_id: 会话ID
            question_text: 问题文本
            client_id: WebSocket客户端ID
            request_id: 请求ID（可选）
            **kwargs: 其他参数
            
        Returns:
            Dict containing request info and cache status
        """
        self.stats["total_requests"] += 1
        
        # 生成问题哈希
        question_hash = self.generate_question_hash(session_id, question_text)
        
        # 检查是否存在缓存的请求
        if question_hash in self.question_hash_index:
            existing_request_id = self.question_hash_index[question_hash]
            cached_request = self.cached_requests.get(existing_request_id)
            
            if cached_request and not cached_request.is_expired():
                # 缓存命中
                self.stats["cache_hits"] += 1
                
                # 将当前客户端添加到现有请求的关联客户端中
                cached_request.add_websocket_client(client_id)
                self._update_client_index(client_id, existing_request_id)
                
                # 立即向客户端推送当前状态
                await self._notify_websocket_client(client_id, cached_request)
                
                logger.info(f"📋 QA请求缓存命中: {question_hash} -> {existing_request_id}")
                
                return {
                    "cache_hit": True,
                    "request_id": existing_request_id,
                    "question_hash": question_hash,
                    "status": cached_request.status.value,
                    "answer": cached_request.answer,
                    "created_at": cached_request.created_at,
                    "client_count": len(cached_request.websocket_clients)
                }
        
        # 缓存未命中，创建新请求
        self.stats["cache_misses"] += 1
        
        if not request_id:
            request_id = f"qa_{int(time.time() * 1000)}_{question_hash}"
        
        cached_request = CachedQARequest(
            request_id=request_id,
            question_hash=question_hash,
            question_text=question_text,
            session_id=session_id,
            **kwargs
        )
        
        # 添加当前客户端
        cached_request.add_websocket_client(client_id)
        
        # 更新索引
        self.cached_requests[request_id] = cached_request
        self.question_hash_index[question_hash] = request_id
        self._update_session_index(session_id, request_id)
        self._update_client_index(client_id, request_id)
        
        logger.info(f"📋 新QA请求已缓存: {request_id}, hash: {question_hash}")
        
        return {
            "cache_hit": False,
            "request_id": request_id,
            "question_hash": question_hash,
            "status": cached_request.status.value,
            "created_at": cached_request.created_at,
            "client_count": 1
        }
    
    async def update_qa_request_status(
        self,
        request_id: str,
        status: QARequestStatus,
        **kwargs
    ):
        """更新QA请求状态并通知相关WebSocket客户端"""
        if request_id not in self.cached_requests:
            logger.warning(f"⚠️ 尝试更新不存在的QA请求: {request_id}")
            return
        
        cached_request = self.cached_requests[request_id]
        cached_request.update_status(status, **kwargs)
        
        # 通知所有关联的WebSocket客户端
        await self._notify_all_clients(cached_request)
        
        logger.info(f"📋 QA请求状态已更新: {request_id} -> {status.value}")
    
    def get_cached_request(self, request_id: str) -> Optional[CachedQARequest]:
        """获取缓存的QA请求"""
        return self.cached_requests.get(request_id)
    
    def get_session_requests(self, session_id: str) -> List[CachedQARequest]:
        """获取指定会话的所有QA请求"""
        if session_id not in self.session_index:
            return []
        
        requests = []
        for request_id in self.session_index[session_id]:
            if request_id in self.cached_requests:
                requests.append(self.cached_requests[request_id])
        
        # 按创建时间排序
        requests.sort(key=lambda r: r.created_at, reverse=True)
        return requests
    
    async def _notify_websocket_client(self, client_id: str, cached_request: CachedQARequest):
        """通知特定WebSocket客户端"""
        if client_id not in self.active_websockets:
            return
        
        try:
            websocket = self.active_websockets[client_id]
            message = {
                "type": "qa_status_update",
                "data": cached_request.to_status_dict(),
                "timestamp": time.time()
            }
            
            await websocket.send_text(json.dumps(message))
            self.stats["websocket_notifications"] += 1
            
            logger.debug(f"📱 已通知WebSocket客户端: {client_id}, status: {cached_request.status.value}")
            
        except Exception as e:
            logger.warning(f"⚠️ WebSocket通知失败: {client_id}, error: {e}")
            # 移除无效的WebSocket连接
            self.unregister_websocket_client(client_id)
    
    async def _notify_all_clients(self, cached_request: CachedQARequest):
        """通知所有关联的WebSocket客户端"""
        notification_tasks = []
        
        for client_id in list(cached_request.websocket_clients):
            task = self._notify_websocket_client(client_id, cached_request)
            notification_tasks.append(task)
        
        if notification_tasks:
            await asyncio.gather(*notification_tasks, return_exceptions=True)
    
    def _update_session_index(self, session_id: str, request_id: str):
        """更新会话索引"""
        if session_id not in self.session_index:
            self.session_index[session_id] = set()
        self.session_index[session_id].add(request_id)
    
    def _update_client_index(self, client_id: str, request_id: str):
        """更新客户端索引"""
        if client_id not in self.client_index:
            self.client_index[client_id] = set()
        self.client_index[client_id].add(request_id)
    
    async def _cleanup_loop(self):
        """清理过期缓存的循环任务"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_expired_requests()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 缓存清理异常: {e}")
    
    async def _cleanup_expired_requests(self):
        """清理过期的QA请求"""
        expired_request_ids = []
        
        for request_id, cached_request in self.cached_requests.items():
            if cached_request.is_expired():
                expired_request_ids.append(request_id)
        
        for request_id in expired_request_ids:
            await self._remove_cached_request(request_id)
            self.stats["expired_cleaned"] += 1
        
        if expired_request_ids:
            logger.info(f"🧹 已清理 {len(expired_request_ids)} 个过期的QA请求")
    
    async def _remove_cached_request(self, request_id: str):
        """移除缓存的QA请求"""
        if request_id not in self.cached_requests:
            return
        
        cached_request = self.cached_requests[request_id]
        
        # 通知客户端请求已过期
        await self.update_qa_request_status(request_id, QARequestStatus.EXPIRED)
        
        # 从索引中移除
        question_hash = cached_request.question_hash
        if question_hash in self.question_hash_index:
            self.question_hash_index.pop(question_hash)
        
        session_id = cached_request.session_id
        if session_id in self.session_index:
            self.session_index[session_id].discard(request_id)
            if not self.session_index[session_id]:
                self.session_index.pop(session_id)
        
        for client_id in cached_request.websocket_clients:
            if client_id in self.client_index:
                self.client_index[client_id].discard(request_id)
                if not self.client_index[client_id]:
                    self.client_index.pop(client_id)
        
        # 移除主缓存
        self.cached_requests.pop(request_id)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            **self.stats,
            "active_requests": len(self.cached_requests),
            "active_websockets": len(self.active_websockets),
            "question_hashes": len(self.question_hash_index),
            "sessions": len(self.session_index),
            "cache_hit_rate": (
                self.stats["cache_hits"] / max(self.stats["total_requests"], 1) * 100
            )
        }
    
    async def cleanup(self):
        """清理所有资源"""
        await self.stop_cleanup_task()
        
        # 清理所有缓存
        self.cached_requests.clear()
        self.question_hash_index.clear()
        self.session_index.clear()
        self.client_index.clear()
        self.active_websockets.clear()
        
        logger.info("🧹 WebSocketQACache cleanup completed")


# 全局缓存实例
_global_qa_cache: Optional[WebSocketQACache] = None


def get_qa_cache() -> WebSocketQACache:
    """获取全局QA缓存实例"""
    global _global_qa_cache
    
    if _global_qa_cache is None:
        _global_qa_cache = WebSocketQACache()
        _global_qa_cache.start_cleanup_task()
        logger.info("🚀 全局WebSocketQACache已创建")
    
    return _global_qa_cache