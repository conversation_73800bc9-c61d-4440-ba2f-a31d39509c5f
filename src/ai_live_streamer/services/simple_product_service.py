"""简化的产品服务
用于快速实现产品管理功能
"""

import sqlite3
import json
from typing import List, Optional, Dict, Any
from datetime import datetime
from loguru import logger

from ..services.persistence.database_manager import DatabaseManager
from ..core.exceptions import ServiceError, ValidationError


class SimpleProductService:
    """简化的产品服务类"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        # 确保数据库schema已初始化
        self.db_manager.initialize_schema()
        
    def list_products(self) -> List[Dict[str, Any]]:
        """获取产品列表"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                SELECT id, sku, name, category, description, price, stock, 
                       created_at, updated_at
                FROM products ORDER BY created_at DESC
                """)
                
                products = []
                for row in cursor.fetchall():
                    products.append({
                        'id': row[0],
                        'sku': row[1], 
                        'name': row[2],
                        'category': row[3],
                        'description': row[4],
                        'price': row[5],
                        'stock': row[6],
                        'created_at': row[7],
                        'updated_at': row[8]
                    })
                
                return products
                
        except sqlite3.Error as e:
            logger.error(f"获取产品列表失败: {e}")
            raise ServiceError(f"获取产品列表失败: {e}")
    
    def create_product(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新产品"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查SKU是否已存在
                cursor.execute("SELECT id FROM products WHERE sku = ?", (product_data.get('sku', ''),))
                if cursor.fetchone():
                    raise ValidationError(f"SKU {product_data.get('sku')} 已存在")
                
                # 插入产品
                cursor.execute("""
                INSERT INTO products (sku, name, category, description, price, stock)
                VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    product_data.get('sku', '').upper(),
                    product_data.get('name', ''),
                    product_data.get('category', 'other'),
                    product_data.get('description', ''),
                    product_data.get('price', 0),
                    product_data.get('stock', 0)
                ))
                
                product_id = cursor.lastrowid
                conn.commit()
                
                # 返回创建的产品
                return self.get_product(product_id)
                
        except sqlite3.Error as e:
            logger.error(f"创建产品失败: {e}")
            raise ServiceError(f"创建产品失败: {e}")
    
    def get_product(self, product_id: int) -> Dict[str, Any]:
        """获取产品详情"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                SELECT id, sku, name, category, description, price, stock, 
                       created_at, updated_at
                FROM products WHERE id = ?
                """, (product_id,))
                
                row = cursor.fetchone()
                if not row:
                    raise ValidationError(f"产品ID {product_id} 不存在")
                
                return {
                    'id': row[0],
                    'sku': row[1],
                    'name': row[2], 
                    'category': row[3],
                    'description': row[4],
                    'price': row[5],
                    'stock': row[6],
                    'created_at': row[7],
                    'updated_at': row[8]
                }
                
        except sqlite3.Error as e:
            logger.error(f"获取产品失败: {e}")
            raise ServiceError(f"获取产品失败: {e}")
    
    def update_product(self, product_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新产品"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建更新语句
                update_fields = []
                values = []
                
                for field in ['name', 'category', 'description', 'price', 'stock']:
                    if field in update_data:
                        update_fields.append(f"{field} = ?")
                        values.append(update_data[field])
                
                if not update_fields:
                    raise ValidationError("没有提供要更新的字段")
                
                values.append(product_id)
                
                cursor.execute(f"""
                UPDATE products 
                SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """, values)
                
                if cursor.rowcount == 0:
                    raise ValidationError(f"产品ID {product_id} 不存在")
                
                conn.commit()
                return self.get_product(product_id)
                
        except sqlite3.Error as e:
            logger.error(f"更新产品失败: {e}")
            raise ServiceError(f"更新产品失败: {e}")
    
    def delete_product(self, product_id: int) -> bool:
        """删除产品"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("DELETE FROM products WHERE id = ?", (product_id,))
                
                if cursor.rowcount == 0:
                    raise ValidationError(f"产品ID {product_id} 不存在")
                
                return True
                
        except sqlite3.Error as e:
            logger.error(f"删除产品失败: {e}")
            raise ServiceError(f"删除产品失败: {e}")
    
    def get_product_qa(self, product_id: int) -> List[Dict[str, Any]]:
        """获取产品QA列表"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                SELECT id, product_id, question, answer, category, tags,
                       hit_count, confidence_score, created_at, updated_at
                FROM product_qa WHERE product_id = ?
                ORDER BY created_at DESC
                """, (product_id,))
                
                qa_list = []
                for row in cursor.fetchall():
                    qa_list.append({
                        'id': row[0],
                        'product_id': row[1],
                        'question': row[2],
                        'answer': row[3],
                        'category': row[4],
                        'tags': json.loads(row[5]) if row[5] else [],
                        'hit_count': row[6],
                        'confidence_score': row[7],
                        'created_at': row[8],
                        'updated_at': row[9]
                    })
                
                return qa_list
                
        except sqlite3.Error as e:
            logger.error(f"获取产品QA失败: {e}")
            raise ServiceError(f"获取产品QA失败: {e}")
    
    def create_product_qa(self, product_id: int, qa_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建产品QA"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 验证产品存在
                cursor.execute("SELECT id FROM products WHERE id = ?", (product_id,))
                if not cursor.fetchone():
                    raise ValidationError(f"产品ID {product_id} 不存在")
                
                # 插入QA
                cursor.execute("""
                INSERT INTO product_qa (product_id, question, answer, category, tags, confidence_score)
                VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    product_id,
                    qa_data.get('question', ''),
                    qa_data.get('answer', ''),
                    qa_data.get('category', ''),
                    json.dumps(qa_data.get('tags', [])),
                    qa_data.get('confidence_score', 0.5)
                ))
                
                qa_id = cursor.lastrowid
                
                # 返回创建的QA
                cursor.execute("""
                SELECT id, product_id, question, answer, category, tags,
                       hit_count, confidence_score, created_at, updated_at
                FROM product_qa WHERE id = ?
                """, (qa_id,))
                
                row = cursor.fetchone()
                return {
                    'id': row[0],
                    'product_id': row[1],
                    'question': row[2],
                    'answer': row[3],
                    'category': row[4],
                    'tags': json.loads(row[5]) if row[5] else [],
                    'hit_count': row[6],
                    'confidence_score': row[7],
                    'created_at': row[8],
                    'updated_at': row[9]
                }
                
        except sqlite3.Error as e:
            logger.error(f"创建产品QA失败: {e}")
            raise ServiceError(f"创建产品QA失败: {e}")
    
    def update_product_qa(self, qa_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新产品QA"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建更新语句
                update_fields = []
                values = []
                
                for field in ['question', 'answer', 'category', 'confidence_score']:
                    if field in update_data:
                        if field == 'tags':
                            update_fields.append(f"{field} = ?")
                            values.append(json.dumps(update_data[field]))
                        else:
                            update_fields.append(f"{field} = ?")
                            values.append(update_data[field])
                
                if not update_fields:
                    raise ValidationError("没有提供要更新的字段")
                
                values.append(qa_id)
                
                cursor.execute(f"""
                UPDATE product_qa 
                SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """, values)
                
                if cursor.rowcount == 0:
                    raise ValidationError(f"QA ID {qa_id} 不存在")
                
                conn.commit()
                
                # 返回更新后的QA
                cursor.execute("""
                SELECT id, product_id, question, answer, category, tags,
                       hit_count, confidence_score, created_at, updated_at
                FROM product_qa WHERE id = ?
                """, (qa_id,))
                
                row = cursor.fetchone()
                return {
                    'id': row[0],
                    'product_id': row[1],
                    'question': row[2],
                    'answer': row[3],
                    'category': row[4],
                    'tags': json.loads(row[5]) if row[5] else [],
                    'hit_count': row[6],
                    'confidence_score': row[7],
                    'created_at': row[8],
                    'updated_at': row[9]
                }
                
        except sqlite3.Error as e:
            logger.error(f"更新产品QA失败: {e}")
            raise ServiceError(f"更新产品QA失败: {e}")
    
    def delete_product_qa(self, qa_id: int) -> bool:
        """删除产品QA"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("DELETE FROM product_qa WHERE id = ?", (qa_id,))
                
                if cursor.rowcount == 0:
                    raise ValidationError(f"QA ID {qa_id} 不存在")
                
                conn.commit()
                return True
                
        except sqlite3.Error as e:
            logger.error(f"删除产品QA失败: {e}")
            raise ServiceError(f"删除产品QA失败: {e}")