#!/usr/bin/env python3
"""
MainContentPlayer V3 - 极简重构版

基于新架构的核心设计原则：
- 单一职责：专注播放控制和QA协调，不再处理复杂并发
- 拥抱简单：让架构本身解决复杂问题
- while循环控制：保持播放控制能力（避免for循环的过度简化风险）
- 统一引擎：使用CosyVoiceUnifiedEngine，告别WebSocket复杂性
- 句子间隙 = 天然的安全中断点

代码量：从923行 → 150-200行
核心理念：句子边界就是for循环的自然边界，优先级调度就是简单的if-else

Author: Claude Code  
Date: 2025-08-06 (重构版)
"""

import asyncio
import time
from typing import List, Optional, Union, AsyncGenerator, Callable, Awaitable, Dict, Any
from datetime import datetime

from loguru import logger

# V1 imports removed - IMainContentPlayer interface removed
# from ..core.live_stream_controller import IMainContentPlayer
from ..exceptions.client_connection_errors import NoClientsConnectedError
from ..core.config import cfg
from ..services.tts_engines.cosyvoice_unified_engine import CosyVoiceUnifiedEngine
from ..core.semantic_sentence_splitter import create_semantic_sentence_splitter, SemanticSentence
from ..core.simple_qa_handler import SimpleQAHandler
from ..models.playback_commands import PlaybackCommand, InsertSentencesCommand, PauseCommand, ResumeCommand, InsertionPolicy
from ..models.enhanced_playback_commands import EnhancedPlaybackCommand, SmartPlaybackQueue, QAInsertCommand
from ..models.player_state import PlayerStateManager, PlayerState, PlaybackPhase, get_monotonic_time_ms
from ..models.playable_interface import IPlayableItem, PlaybackStatus
from .playback_progress_tracker import PlaybackProgressTracker, create_playback_tracker


class MainContentPlayer:
    """主内容播放器 - 极简重构版
    
    核心职责（简化后）：
    1. 使用语义分析器将脚本分解为句子
    2. 在句子间隙检查QA请求（天然的安全中断点）
    3. 使用统一引擎进行TTS合成和音频广播
    4. 提供简单清晰的播放控制（play/pause/resume/stop）
    
    架构优势：
    - 句子边界 = while循环的自然边界（100%可靠的中断点）
    - 优先级调度 = 简单的if-else（QA优先于正常播放）
    - 无复杂状态机、锁机制、并发任务管理
    """
    
    def __init__(self):
        """简化初始化 - 只关注核心组件"""
        # 核心组件
        self.semantic_splitter = create_semantic_sentence_splitter()
        
        # 使用统一引擎配置
        tts_config = cfg.get_yaml_config('tts_engines.cosyvoice_unified', {})
        self.tts_engine = CosyVoiceUnifiedEngine(tts_config)
        
        # QA处理器（使用已重构的版本）
        from ..services.llm_adapters.litellm_adapter import LiteLLMAdapter
        try:
            # 获取LLM配置
            llm_config = cfg.get_yaml_config('ai_models.litellm', {})
            if not llm_config:
                # 使用默认配置
                llm_config = {
                    'model': cfg.ai_model_provider or 'qwen-plus',
                    'api_base': 'http://localhost:4000', 
                    'timeout_seconds': 30
                }
            llm_adapter = LiteLLMAdapter(llm_config)
            self.qa_handler = SimpleQAHandler(llm_adapter)
        except Exception as e:
            logger.warning(f"Failed to create QA handler: {e}")
            self.qa_handler = None
        
        # 音频流代理
        self.audio_proxy = self._get_audio_proxy()
        
        # 🔧 NEW: 形式化状态机管理器
        self.state_manager = PlayerStateManager()
        
        # 播放时序配置
        self.timing_config = self._load_timing_config()
        
        # 🚀 双进度管理：播放进度追踪器
        self.progress_tracker = create_playback_tracker(self.timing_config)
        logger.info("✅ PlaybackProgressTracker initialized for dual-progress management")
        
        # 简单状态控制（保持兼容性，逐步迁移到state_manager）
        self._playing_state = False
        self.is_paused = False
        self.should_stop = False
        
        # 播放内容和进度 - 决策者模式：不再维护句子列表
        # self.sentences: List[SemanticSentence] = []  # DEPRECATED: 由 playlist_manager 管理
        # self.current_index = 0  # DEPRECATED: 由 playlist_manager 管理
        self.playlist_manager = None  # 将在运行时注入
        
        # 事件驱动：安全中断点通知机制（保持兼容性）
        self.safe_point_reached_event = asyncio.Event()
        
        # 句子完成标志（架构升级的核心）
        self._current_sentence_completed = False
        
        # 🚀 增强Command处理架构 - 支持预演-提交模式
        self._legacy_command_queue: Optional[asyncio.Queue] = None  # 兼容旧命令
        self._smart_command_queue: Optional[SmartPlaybackQueue] = None  # 新增强命令队列
        self._command_processor_task: Optional[asyncio.Task] = None
        self._enhanced_processor_task: Optional[asyncio.Task] = None
        self._command_processor_running = False
        
        logger.info("✅ MainContentPlayer V4 initialized - State Machine Architecture with Interruptible Timing")
    
    def _load_timing_config(self) -> Dict[str, Any]:
        """加载播放时序估算配置
        
        注意：这些参数仅用于估算和显示，不用于实际播放等待！
        对于流式播放：TTS完成 = 播放完成，无需等待
        """
        timing_config = cfg.get_yaml_config('playback_timing', {})
        
        # 设置默认值 - 仅用于估算
        defaults = {
            'network_latency_ms': 150,     # 估算网络传输时间
            'client_buffer_ms': 400,       # 估算客户端缓冲时间
            'wait_check_interval_ms': 100, # 非流式播放的检查间隔
            'max_wait_timeout_ms': 30000,  # 安全上限
            'fallback_duration_per_char_ms': 80  # 时长估算
        }
        
        # 合并配置和默认值
        for key, default_value in defaults.items():
            if key not in timing_config:
                timing_config[key] = default_value
        
        logger.debug(f"📋 播放时序估算配置加载完成: {timing_config}")
        return timing_config
    
    def is_at_safe_interruption_point(self) -> bool:
        """检查播放器当前是否处于可以安全中断的句子间隙
        
        🔧 UPGRADED: 现在使用形式化状态机判断，更加准确可靠
        
        Returns:
            bool: 是否处于安全中断点
        """
        # 使用状态机的安全点判断（更精确）
        is_safe_by_state_machine = self.state_manager.is_at_safe_interruption_point()
        
        # 保持与原有事件机制的兼容性
        is_safe_by_event = self.safe_point_reached_event.is_set()
        
        # 以状态机为准，但记录不一致情况用于调试
        if is_safe_by_state_machine != is_safe_by_event:
            logger.debug(f"🔍 安全点判断不一致: state_machine={is_safe_by_state_machine}, event={is_safe_by_event}")
        
        return is_safe_by_state_machine
    
    def _get_audio_proxy(self):
        """获取音频流代理"""
        try:
            from ..services.audio_streaming_proxy import get_audio_proxy
            return get_audio_proxy()
        except Exception as e:
            logger.warning(f"Failed to get audio proxy: {e}")
            return None
    
    def _sync_state_with_state_machine(self, new_player_state: PlayerState, trigger: str = "sync") -> None:
        """同步旧状态变量与状态机
        
        Args:
            new_player_state: 新的播放器状态
            trigger: 状态转换触发原因
        """
        # 更新状态机
        self.state_manager.set_state(new_player_state, trigger)
        
        # 同步旧状态变量（保持向后兼容性）
        if new_player_state == PlayerState.IDLE:
            self._playing_state = False
            self.is_paused = False
            self.should_stop = False
            self.safe_point_reached_event.set()
        elif new_player_state in [PlayerState.GENERATING_AUDIO, PlayerState.STREAMING_AUDIO]:
            self._playing_state = True
            self.is_paused = False
            self.safe_point_reached_event.clear()
        elif new_player_state == PlayerState.WAITING_PLAYBACK:
            # ⚠️ 注意：WAITING_PLAYBACK 状态在流式播放中不应被使用
            # 保留此分支仅为向后兼容，但应避免进入此状态
            logger.warning(f"⚠️ 进入 WAITING_PLAYBACK 状态，但这在流式播放中通常是错误的")
            self._playing_state = True
            self.is_paused = False
            self.safe_point_reached_event.clear()
        elif new_player_state == PlayerState.PAUSED:
            # _playing_state 保持不变
            self.is_paused = True
            # 暂停时设置安全点
            self.safe_point_reached_event.set()
        elif new_player_state == PlayerState.STOPPED:
            self._playing_state = False
            self.is_paused = False
            self.should_stop = True
            self.safe_point_reached_event.set()
        
        # 更新状态机的句子进度 - 决策者模式：由 playlist_manager 管理进度
        # self.state_manager.update_sentence_progress(self.current_index, self.current_index - 1 if self.current_index > 0 else -1)
    
    async def _legacy_play_v1_deprecated(self, content: Union[List[str], AsyncGenerator[str, None], str], on_finished: Optional[Callable[[], Awaitable[None]]] = None) -> None:
        """播放内容 - 核心播放循环（简化版）
        
        🔧 架构修复：播放器现在是纯粹的执行者，不再负责内容准备。
        内容应该已经通过 playlist_manager.initialize_from_script() 初始化。
        
        Args:
            content: 保留参数仅为向后兼容，实际内容从 playlist_manager 获取
            on_finished: 播放完成时的回调函数（异步）
        """
        if self._playing_state:
            logger.warning("Player is already playing")
            return
        
        logger.info("🎬 Starting playback as pure executor")
        
        try:
            # 🔧 架构修复：移除内容预处理，播放器应该只是执行者
            # 内容应该已经在上层（control.py 或 static_content_controller.py）初始化
            # await self._prepare_content(content)  # REMOVED - violates single responsibility
            
            # 确保 playlist_manager 已注入
            if not hasattr(self, 'playlist_manager') or self.playlist_manager is None:
                from ..core.dependencies import get_playlist_manager
                self.playlist_manager = get_playlist_manager()
                logger.info("📋 Playlist manager dependency injected")
            
            # 验证播放列表已初始化
            playlist_info = await self.playlist_manager.get_playlist_info()
            if playlist_info.total_items == 0:
                logger.error("❌ Playlist is empty - content must be initialized before calling play()")
                raise RuntimeError("Playlist not initialized. Call playlist_manager.initialize_from_script() first.")
            
            logger.info(f"✅ Playlist ready with {playlist_info.total_items} items")
            
            # 2. 初始化引擎
            await self.tts_engine.initialize()
            
            # 3. 重置播放状态 - 🔧 使用状态机管理
            # self.current_index = 0  # DEPRECATED: 由 playlist_manager 管理
            self._sync_state_with_state_machine(PlayerState.IDLE, "play_start")
            
            # 🚀 重置播放进度追踪器
            self.progress_tracker.reset()
            logger.debug("📊 Progress tracker reset for new playback session")
            
            # 确保命令处理器正在运行
            if not self._command_processor_running:
                raise RuntimeError("Command processor not started - call start() before play()")
            
            logger.info("🎵 Ready to start playback with decision-executor pattern")
            
            # 4. 核心播放循环 - 简单而强大
            await self._main_playback_loop()
            
        except Exception as e:
            logger.error(f"❌ Playback failed: {e}")
            raise
        finally:
            # 5. 清理和状态重置（命令处理器保持运行）- 🔧 使用状态机管理
            self._sync_state_with_state_machine(PlayerState.IDLE, "play_complete")
            logger.info("🏁 Playbook completed - command processor remains active")
            
            # 7. 调用播放完成回调（如果提供的话）
            if on_finished:
                try:
                    await on_finished()
                    logger.debug("✅ on_finished callback executed successfully")
                except Exception as e:
                    logger.error(f"❌ on_finished callback failed: {e}")
    
    async def _prepare_content(self, content: Union[str, List[str], AsyncGenerator[str, None]]) -> None:
        """内容预处理和传递给决策者
        
        🔧 DEPRECATED: 此方法已弃用。播放器现在是纯粹的执行者，
        内容准备应该在上层（control.py 或 controller）完成。
        保留此方法仅为向后兼容。
        """
        logger.warning("⚠️ _prepare_content() is deprecated. Content should be initialized via playlist_manager before calling play().")
        # 获取 playlist_manager
        if not hasattr(self, 'playlist_manager') or self.playlist_manager is None:
            from ..core.dependencies import get_playlist_manager
            self.playlist_manager = get_playlist_manager()
        
        sentences = []
        
        if isinstance(content, str):
            # 字符串内容 - 使用语义分析器（推荐方式）
            logger.info(f"📝 Processing text content with semantic analysis ({len(content)} chars)")
            sentences = self.semantic_splitter.split_sentences_semantic(content)
            logger.info(f"✅ Split into {len(sentences)} semantic sentences")
            
        elif isinstance(content, list):
            # 列表内容 - 每个元素作为一个句子
            logger.info(f"📝 Processing list content ({len(content)} items)")
            for item in content:
                if isinstance(item, str) and item.strip():
                    # 对每个列表项进行语义分析
                    sub_sentences = self.semantic_splitter.split_sentences_semantic(item)
                    sentences.extend(sub_sentences)
            logger.info(f"✅ Processed into {len(sentences)} total sentences")
            
        else:
            # 异步生成器暂时不支持（按计划，以后再实现）
            raise NotImplementedError("AsyncGenerator support will be added in future versions")
        
        # 初始化播放列表
        await self.playlist_manager.initialize_from_script(sentences)
        logger.info(f"📋 Playlist initialized with {len(sentences)} sentences")
    
    async def _main_playback_loop(self) -> None:
        """核心播放循环 - 决策者-执行者模式"""
        logger.debug("🎵 Starting decision-executor playback loop")
        
        # 注入 playlist_manager 依赖
        if not hasattr(self, 'playlist_manager') or self.playlist_manager is None:
            from ..core.dependencies import get_playlist_manager
            self.playlist_manager = get_playlist_manager()
            logger.info("📋 Playlist manager injected for decision-executor pattern")
        
        last_playback_status = None
        
        # 🔧 状态机驱动的播放循环
        while self.state_manager.get_current_state() != PlayerState.STOPPED:
            
            # 暂停检查 - 使用状态机
            if self.state_manager.get_current_state() == PlayerState.PAUSED:
                self._sync_state_with_state_machine(PlayerState.PAUSED, "pause_wait")
                await asyncio.sleep(0.1)
                continue
            
            # 停止检查 - 使用状态机
            if (self.should_stop or 
                self.state_manager.get_current_state() == PlayerState.STOPPED):
                logger.info("⏹️ Stop requested, ending playback")
                break
            
            # 🎯 从决策者获取下一个播放项
            next_item = await self.playlist_manager.get_next_item_to_play(last_playback_status)
            
            if next_item is None:
                logger.info("🏁 No more items to play - playlist ended")
                break
            
            # 安全中断点
            self._sync_state_with_state_machine(PlayerState.IDLE, "item_gap")
            logger.info(f"🎬 Playing item: {next_item.item_id} (type: {next_item.item_type})")
            
            # 播放项目
            try:
                await self._play_item(next_item)
                
                # 通知播放完成
                await self.playlist_manager.notify_playback_completed(next_item)
                last_playback_status = PlaybackStatus(next_item.item_id, 'completed')
                
            except NoClientsConnectedError as e:
                # 🔥 处理无客户端连接的情况
                logger.warning(f"⚠️ No clients connected, pausing playlist: {e}")
                
                # 通知 playlist_manager 暂停
                await self.playlist_manager.pause_due_to_no_clients()
                
                # 通知播放失败（特殊原因）
                await self.playlist_manager.notify_playback_failed(next_item, e)
                last_playback_status = PlaybackStatus(next_item.item_id, 'failed', error=e)
                
                # 等待一段时间再检查，避免忙等待
                await asyncio.sleep(1.0)
                
            except Exception as e:
                logger.error(f"❌ Failed to play item {next_item.item_id}: {e}")
                
                # 通知播放失败
                await self.playlist_manager.notify_playback_failed(next_item, e)
                last_playback_status = PlaybackStatus(next_item.item_id, 'failed', error=e)
            
            # 返回安全点状态
            self._sync_state_with_state_machine(PlayerState.IDLE, "item_complete")
            logger.trace(f"✅ Safe point reached after item {next_item.item_id}")
        
        # 播放结束
        self.safe_point_reached_event.set()
        logger.trace("🎵 Playback loop completed - all content played")
    
    async def _play_item(self, item: IPlayableItem) -> None:
        """播放任何类型的可播放项 - 纯执行者"""
        logger.info(f"🎵 Playing item: {item.item_id} ({item.item_type})")
        
        # 🔥 热修复：连续广播失败计数器
        consecutive_broadcast_failures = 0
        MAX_CONSECUTIVE_FAILURES = 5  # 最大连续失败次数
        
        try:
            audio_chunk_count = 0
            total_audio_duration_ms = 0  # 累计音频时长
            
            # 🔧 状态转换：开始TTS合成
            self._sync_state_with_state_machine(PlayerState.GENERATING_AUDIO, "tts_start")
            logger.debug(f"🎙️ Starting TTS synthesis for item {item.item_id}")
            
            # 使用新引擎的流式合成API - 支持中断
            async for audio_chunk in self.tts_engine.synthesize_streaming(item.content):
                # 中断检查：支持播放过程中的暂停/停止响应
                current_state = self.state_manager.get_current_state()
                if (self.should_stop or 
                    current_state == PlayerState.STOPPED or 
                    current_state == PlayerState.PAUSED):
                    logger.debug("⏸️ Playback interrupted during streaming")
                    raise InterruptedError("Playback interrupted")
                
                # 实时广播音频数据
                if (audio_chunk and audio_chunk.audio_stream and 
                    audio_chunk.audio_stream.audio_data and self.audio_proxy):
                    
                    # 🔧 状态转换：开始音频流传输
                    if audio_chunk_count == 0:
                        self._sync_state_with_state_machine(PlayerState.STREAMING_AUDIO, "audio_streaming_start")
                        logger.info(f"🔊 Started audio streaming for item {item.item_id}")
                    
                    # 累计音频时长
                    chunk_duration_ms = audio_chunk.audio_stream.duration_ms or 0
                    total_audio_duration_ms += chunk_duration_ms
                    
                    # 传递给音频代理（WebSocket广播）
                    success = await self.audio_proxy.broadcast_audio_binary(
                        audio_chunk.audio_stream.audio_data
                    )
                    
                    if not success:
                        # 🔥 热修复：记录连续失败次数
                        consecutive_broadcast_failures += 1
                        logger.warning(f"Failed to broadcast audio chunk for item {item.item_id} "
                                     f"(consecutive failures: {consecutive_broadcast_failures}/{MAX_CONSECUTIVE_FAILURES})")
                        
                        # 🔥 热修复：达到阈值后立即停止
                        if consecutive_broadcast_failures >= MAX_CONSECUTIVE_FAILURES:
                            logger.error(f"❌ Reached maximum consecutive broadcast failures ({MAX_CONSECUTIVE_FAILURES}), "
                                       f"stopping playback for item {item.item_id}")
                            raise RuntimeError(f"Broadcast failed {MAX_CONSECUTIVE_FAILURES} times consecutively - "
                                             "likely no clients connected")
                    else:
                        # 成功广播，重置失败计数器
                        consecutive_broadcast_failures = 0
                    
                    audio_chunk_count += 1
                    
                    # 🔧 音频块完成
                    if audio_chunk.audio_stream.is_final:
                        logger.debug(f"🎵 Final audio chunk received for item {item.item_id}")
                        # 记录播放进度
                        self.progress_tracker.record_sentence_dispatch(
                            sentence_index=0,  # 项目不再有索引概念
                            dispatch_time_ms=get_monotonic_time_ms(),
                            audio_duration_ms=total_audio_duration_ms,
                            qa_inserted=item.item_type.startswith("qa_")
                        )
                        logger.info(f"📊 Playback progress recorded: item {item.item_id}, duration={total_audio_duration_ms}ms")
            
            # 播放成功完成
            self._sync_state_with_state_machine(PlayerState.IDLE, "streaming_complete")
            logger.info(f"✅ Item {item.item_id} played successfully (chunks: {audio_chunk_count})")
            
        except InterruptedError:
            # 播放被中断，但这不算失败
            logger.info(f"⏸️ Item {item.item_id} playback interrupted")
            raise
        except NoClientsConnectedError as e:
            # 🔥 捕获无客户端连接异常，这是熔断机制的关键
            logger.error(f"❌ No clients connected to receive audio for item {item.item_id}: {e}")
            # 抛出特定异常，让主循环知道是因为没有客户端
            raise
        except Exception as e:
            logger.error(f"❌ Failed to play item {item.item_id}: {e}")
            raise
    
    async def _play_current_sentence(self) -> None:
        """播放当前句子 - 内部管理完成状态"""
        if self.current_index >= len(self.sentences):
            return
        
        sentence = self.sentences[self.current_index]
        logger.info(f"🎬 准备播放句子 {self.current_index + 1}/{len(self.sentences)}: {sentence.text[:50]}...")
        
        # 标记句子播放是否完成
        self._current_sentence_completed = False
        
        try:
            audio_chunk_count = 0
            total_audio_duration_ms = 0  # 累计音频时长
            
            # 🔧 状态转换：开始TTS合成
            self._sync_state_with_state_machine(PlayerState.GENERATING_AUDIO, "tts_start")
            logger.debug(f"🎙️ 开始TTS合成和音频流播放...")
            
            # 使用新引擎的流式合成API - 支持中断
            async for audio_chunk in self.tts_engine.synthesize_streaming(sentence.text):
                # 中断检查：支持句子播放过程中的暂停/停止响应
                current_state = self.state_manager.get_current_state()
                if (self.should_stop or 
                    current_state == PlayerState.STOPPED or 
                    current_state == PlayerState.PAUSED):
                    logger.debug("⏸️ Playback interrupted during sentence streaming")
                    return  # 提前返回，_current_sentence_completed 保持 False
                
                # 实时广播音频数据
                if (audio_chunk and audio_chunk.audio_stream and 
                    audio_chunk.audio_stream.audio_data and self.audio_proxy):
                    
                    # 🔧 状态转换：开始音频流传输
                    if audio_chunk_count == 0:
                        self._sync_state_with_state_machine(PlayerState.STREAMING_AUDIO, "audio_streaming_start")
                        logger.info(f"🔊 开始音频流播放句子 {self.current_index + 1}")
                    
                    await self.audio_proxy.broadcast_audio_binary(
                        audio_chunk.audio_stream.audio_data
                    )
                    audio_chunk_count += 1
                    
                    # 累计音频时长
                    if hasattr(audio_chunk.audio_stream, 'duration_ms'):
                        total_audio_duration_ms += audio_chunk.audio_stream.duration_ms
            
            # 🚀 双进度管理：记录播放进度（不等待）
            # 这是解决"TTS完成≠播放完成"的关键
            
            # 检查是否为QA插入的句子（可通过句子的特殊标记判断）
            is_qa_insertion = hasattr(sentence, 'is_qa_insertion') and sentence.is_qa_insertion
            
            if total_audio_duration_ms > 0:
                self.progress_tracker.record_sentence_dispatch(
                    index=self.current_index,
                    text=sentence.text,
                    audio_duration_ms=total_audio_duration_ms,
                    is_qa_insertion=is_qa_insertion
                )
                logger.info(f"📊 播放进度已记录: 句子 #{self.current_index}, "
                           f"音频时长={total_audio_duration_ms}ms, QA插入={is_qa_insertion}")
            else:
                # 如果没有获取到音频时长，使用字符数估算
                logger.warning(f"⚠️ 未获取到音频时长，使用字符数估算")
                self.progress_tracker.record_sentence_dispatch(
                    index=self.current_index,
                    text=sentence.text,
                    audio_duration_ms=None,  # 将使用 fallback
                    is_qa_insertion=is_qa_insertion
                )
            
            # 🔧 状态转换：流式传输完成，回到空闲状态准备下一句
            self._sync_state_with_state_machine(PlayerState.IDLE, "streaming_complete")
            
            # ⚠️ 关键改动：发送完成≠播放完成
            # 这里标记的是"发送完成"，而不是"播放完成"
            self._current_sentence_completed = True  # 表示可以发送下一句
            logger.info(f"✅ 句子 {self.current_index + 1} 流式传输完成 (音频块数: {audio_chunk_count}, 发送进度完成)")
            
            # 真正的播放完成时间由 progress_tracker 估算
            estimated_completion = self.progress_tracker.get_estimated_completion_time(self.current_index)
            if estimated_completion:
                logger.debug(f"⏰ 预计播放完成时间: {estimated_completion:.0f}ms")
            
        except Exception as e:
            logger.error(f"❌ Failed to play sentence {self.current_index}: {e}")
            raise
    
    async def _wait_for_real_playback_completion(self, audio_duration_ms: int) -> None:
        """⚠️ 已废弃：播放完成等待机制 - 仅用于非流式播放场景
        
        注意：此方法对流式播放是错误的！
        - 流式播放：TTS完成 = 播放完成，无需等待
        - 非流式播放：可能需要等待客户端播放完成（未来功能）
        
        Args:
            audio_duration_ms: 音频实际时长(毫秒)
        """
        logger.warning("⚠️ _wait_for_real_playback_completion() 被调用，但对流式播放这是错误的！")
        
        # 🚨 注意：以下代码被注释掉，因为在流式播放中这是错误的
        # 保留代码仅供将来非流式播放场景参考
        
        # # 从配置加载时序参数
        # network_latency_ms = self.timing_config['network_latency_ms']
        # client_buffer_ms = self.timing_config['client_buffer_ms']
        # check_interval_ms = self.timing_config['wait_check_interval_ms']
        # max_timeout_ms = self.timing_config['max_wait_timeout_ms']
        # 
        # # 计算总等待时间
        # total_wait_ms = audio_duration_ms + network_latency_ms + client_buffer_ms
        # total_wait_ms = min(total_wait_ms, max_timeout_ms)
        # 
        # logger.debug(f"🕐 播放等待估算: 音频时长={audio_duration_ms}ms, 网络延迟={network_latency_ms}ms, "
        #             f"客户端缓冲={client_buffer_ms}ms, 总估算={total_wait_ms}ms")
        
        # ⚠️ 对于流式播放，以下操作是错误的：
        # 1. 不应该设置状态机音频时间（基于错误的等待时间）
        # 2. 不应该进入 WAITING_PLAYBACK 状态
        # 3. 不应该实际等待任何时间
        
        return  # 立即返回，不进行任何等待
        
        # 📝 以下是被移除的错误等待逻辑，保留作为参考：
        # 
        # try:
        #     while remaining_ms > 0:
        #         # 检查停止信号和暂停状态...
        #         # await asyncio.sleep() 等待逻辑...
        #         # 这对流式播放是完全错误的！
        #     
        #     # 清除音频时间信息和状态管理...
        # except Exception as e:
        #     # 异常处理...
        # 
        # ✅ 正确的流式播放逻辑：TTS流完成 = 播放完成，无需任何等待
    
    async def play_interrupt(self, text: str) -> None:
        """播放中断内容 - 向后兼容接口，内部使用Command实现
        
        DEPRECATED: 此方法已弃用，建议使用 submit_command 配合 InsertSentencesCommand
        
        Args:
            text: 要播放的文本内容
        """
        logger.warning("⚠️ play_interrupt is deprecated, use submit_command with InsertSentencesCommand")
        
        if not text or not text.strip():
            logger.warning("play_interrupt called with empty text")
            return
        
        # 使用Command模式实现，改为AFTER_SENTENCE策略确保播放完成
        command = InsertSentencesCommand([text], InsertionPolicy.AFTER_SENTENCE)
        await self.submit_command(command)
    
    async def pause(self) -> None:
        """暂停播放 - 使用Command实现"""
        if not self._playing_state:
            logger.warning("Cannot pause: not playing")
            return
        
        command = PauseCommand()
        await self.submit_command(command)
    
    async def resume(self) -> None:
        """恢复播放 - 使用Command实现"""
        if not self._playing_state:
            logger.warning("Cannot resume: not playing")
            return
        
        if not self.is_paused:
            logger.debug("Already playing")
            return
        
        command = ResumeCommand()
        await self.submit_command(command)
    
    async def stop(self) -> None:
        """停止播放 - 🔧 使用状态机管理"""
        logger.info("🛑 Stopping playback")
        self.should_stop = True
        self._sync_state_with_state_machine(PlayerState.STOPPED, "stop_command")
        # 🚀 不重置进度追踪器，保持历史记录用于分析
    
    def is_playing(self) -> bool:
        """是否正在播放 - 🚀 基于双进度判断"""
        # 状态机判断（发送状态）
        current_state = self.state_manager.get_current_state()
        is_sending = current_state in [
            PlayerState.GENERATING_AUDIO, 
            PlayerState.STREAMING_AUDIO
        ]
        
        # 播放进度判断（用户听到的内容）
        playing_index = self.progress_tracker.get_current_playing_index()
        # 决策者模式：不再跟踪总句子数
        # has_content_playing = playing_index < len(self.sentences)
        has_content_playing = self._playing_state  # 简化判断
        
        # 只要还在发送或用户还在听，就认为在播放
        return is_sending or has_content_playing
    
    def get_status(self) -> dict:
        """获取播放状态信息 - 🚀 双进度管理版本"""
        # 获取状态机的详细状态
        detailed_status = self.state_manager.get_detailed_status()
        
        # 获取播放进度信息
        playback_stats = self.progress_tracker.get_playback_stats()
        playing_index = playback_stats["current_playing_index"]
        
        # 决策者模式：状态信息简化
        legacy_status = {
            "is_playing": self.is_playing(),
            "is_paused": self.state_manager.get_current_state() == PlayerState.PAUSED,
            "current_index": -1,  # DEPRECATED: 由 playlist_manager 管理
            "total_sentences": -1,  # DEPRECATED: 由 playlist_manager 管理
            "progress_percent": 0,  # DEPRECATED: 由 playlist_manager 管理
            "remaining_sentences": -1,  # DEPRECATED: 由 playlist_manager 管理
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 合并状态机和双进度信息
        return {
            **legacy_status,
            "state_machine": detailed_status,
            "playback_progress": {
                "sending_index": -1,  # DEPRECATED: 由 playlist_manager 管理
                "playing_index": playing_index,       # 用户听到哪了
                "sending_complete": False,  # DEPRECATED: 由 playlist_manager 管理
                "playback_complete": False,  # DEPRECATED: 由 playlist_manager 管理
                "completed_sentences": playback_stats["completed_sentences"],
                "total_playback_duration_ms": playback_stats["total_duration_ms"],
                "is_paused": playback_stats["is_paused"]
            },
            "enhanced_info": {
                "player_state": detailed_status["player_state"],
                "playback_phase": detailed_status["playback_phase"],
                "user_perceived_index": playing_index,  # 基于播放进度
                "at_safe_interruption_point": detailed_status["at_safe_point"],
                "can_accept_qa_insertion": detailed_status["can_accept_qa"]
            }
        }
    
    async def start(self):
        """启动播放器和命令处理器 - 幂等操作 - 🚀 增强版本支持智能命令队列"""
        if self._command_processor_task is None:
            self._command_processor_running = True
            
            # 创建传统命令队列（向后兼容）
            if not hasattr(self, '_legacy_command_queue') or self._legacy_command_queue is None:
                self._legacy_command_queue = asyncio.Queue(maxsize=1000)
            
            # 🚀 创建智能命令队列（支持优先级和预演-提交）
            if self._smart_command_queue is None:
                self._smart_command_queue = SmartPlaybackQueue(maxsize=1000)
            
            # 启动传统命令处理器
            self._command_processor_task = asyncio.create_task(
                self._process_commands_single_threaded()
            )
            
            # 🚀 启动增强命令处理器
            self._enhanced_processor_task = asyncio.create_task(
                self._process_enhanced_commands()
            )
            
            # 初始化状态机为空闲状态
            self._sync_state_with_state_machine(PlayerState.IDLE, "player_start")
            logger.info("✅ MainContentPlayer started with dual command processors (legacy + enhanced)")
        else:
            logger.debug("⚠️ MainContentPlayer already started, ignoring duplicate start()")
    
    async def _start_command_processor(self):
        """启动命令处理器 - DEPRECATED: 使用start()方法"""
        logger.warning("⚠️ _start_command_processor is deprecated, use start() method")
        await self.start()
    
    async def stop(self):
        """停止播放器和命令处理器 - 幂等操作 - 🚀 增强版本支持双处理器"""
        if self._command_processor_task is not None or self._enhanced_processor_task is not None:
            # 设置状态机为停止状态
            self._sync_state_with_state_machine(PlayerState.STOPPED, "player_shutdown")
            
            self._command_processor_running = False
            
            # 停止智能命令队列处理
            if self._smart_command_queue:
                self._smart_command_queue.stop_processing()
            
            # 停止传统命令处理器
            if self._command_processor_task:
                self._command_processor_task.cancel()
                try:
                    await self._command_processor_task
                except asyncio.CancelledError:
                    pass
                self._command_processor_task = None
            
            # 停止增强命令处理器
            if self._enhanced_processor_task:
                self._enhanced_processor_task.cancel()
                try:
                    await self._enhanced_processor_task
                except asyncio.CancelledError:
                    pass
                self._enhanced_processor_task = None
            
            # 重置状态机
            self.state_manager.reset()
            logger.info("🛑 MainContentPlayer stopped with dual command processors and state machine reset")
        else:
            logger.debug("⚠️ MainContentPlayer already stopped, ignoring duplicate stop()")
    
    async def _stop_command_processor(self):
        """停止命令处理器 - DEPRECATED: 使用stop()方法"""
        logger.warning("⚠️ _stop_command_processor is deprecated, use stop() method") 
        await self.stop()
    
    async def _process_commands_single_threaded(self):
        """单线程命令处理器 - 架构上消除竞态条件（传统命令）"""
        logger.debug("🎯 Legacy command processor thread started")
        
        try:
            while self._command_processor_running:
                try:
                    # 使用短超时避免阻塞关闭
                    command = await asyncio.wait_for(
                        self._legacy_command_queue.get(), 
                        timeout=0.1
                    )
                    
                    logger.debug(f"🎯 Executing legacy command: {type(command).__name__}")
                    await command.execute(self)
                    
                except asyncio.TimeoutError:
                    # 超时是正常的，继续循环
                    continue
                except Exception as e:
                    logger.error(f"❌ Legacy command execution error: {e}")
                    # 继续运行，不因单个命令错误而停止整个处理器
                    continue
        except Exception as e:
            logger.critical(f"💀 Legacy command processor crashed unexpectedly: {e}")
            raise
        finally:
            logger.debug("🎯 Legacy command processor thread stopped")
    
    async def _process_enhanced_commands(self):
        """增强命令处理器 - 🚀 支持优先级和预演-提交模式"""
        logger.debug("🚀 Enhanced command processor thread started")
        
        try:
            if self._smart_command_queue:
                await self._smart_command_queue.process_commands(self)
        except Exception as e:
            logger.critical(f"💀 Enhanced command processor crashed unexpectedly: {e}")
            raise
        finally:
            logger.debug("🚀 Enhanced command processor thread stopped")
    
    async def submit_command(self, command: PlaybackCommand):
        """外部提交命令的唯一接口 - 有界队列背压机制（传统命令）"""
        if not self._command_processor_running:
            raise RuntimeError("Command processor not running - call start() first")
        
        try:
            # 非阻塞put，如果队列满则立即抛出异常
            self._legacy_command_queue.put_nowait(command)
            logger.debug(f"📨 Legacy command submitted: {type(command).__name__}")
        except asyncio.QueueFull:
            logger.error(f"❌ Legacy command queue full, rejecting command: {type(command).__name__}")
            raise RuntimeError(f"Command queue full, cannot submit {type(command).__name__}") from None
    
    async def submit_enhanced_command(self, command: EnhancedPlaybackCommand):
        """提交增强命令 - 🚀 支持优先级和预演-提交模式"""
        if not self._command_processor_running or self._smart_command_queue is None:
            raise RuntimeError("Enhanced command processor not running - call start() first")
        
        try:
            await self._smart_command_queue.submit_command(command)
            logger.info(f"🚀 Enhanced command submitted: {command.command_id} ({type(command).__name__}, priority: {command.priority.value})")
        except Exception as e:
            logger.error(f"❌ Failed to submit enhanced command: {e}")
            raise RuntimeError(f"Failed to submit enhanced command: {e}") from None
    
    async def insert_qa_sentences(
        self,
        sentences: List[str], 
        insertion_strategy: str = "safe_after_current",
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """QA句子插入接口 - 🎯 解决并发冲突的核心方法
        
        Args:
            sentences: 要插入的句子列表
            insertion_strategy: 插入策略
            metadata: QA元数据
            
        Returns:
            插入结果信息
        """
        if not sentences:
            raise ValueError("sentences cannot be empty")
        
        # 创建QA插入命令
        qa_command = QAInsertCommand(
            sentences=sentences,
            insertion_strategy=insertion_strategy,
            metadata=metadata or {}
        )
        
        # 提交到增强命令队列
        await self.submit_enhanced_command(qa_command)
        
        # 等待命令执行完成（可选）
        # 注意：在实际应用中可以异步等待，这里为了简化直接返回命令信息
        
        return {
            "command_id": qa_command.command_id,
            "sentences_count": len(sentences),
            "insertion_strategy": insertion_strategy,
            "priority": qa_command.priority.value,
            "submitted_at": qa_command.created_at,
            "metadata": metadata
        }
    
    async def _insert_sentences_with_policy(self, sentences: List[str], policy: InsertionPolicy):
        """根据策略插入句子到播放队列"""
        insertion_start = datetime.now()
        logger.info(f"🎯 开始QA插入处理 - 时间戳: {insertion_start.strftime('%H:%M:%S.%f')[:-3]}")
        
        if not sentences:
            logger.warning("⚠️ 插入的句子列表为空，无需处理")
            return
        
        # 记录当前播放状态和位置
        current_status = self.get_status()
        logger.info(f"📊 当前播放状态 - 播放中: {current_status['is_playing']}, "
                   f"暂停: {current_status['is_paused']}, "
                   f"当前句子: {current_status['current_index'] + 1}/{current_status['total_sentences']}")
        
        # 将文本转换为SemanticSentence对象
        new_sentences = []
        for i, text in enumerate(sentences, 1):
            if text.strip():
                logger.debug(f"🔍 处理第{i}个插入句子: {text[:30]}...")
                # 进行语义分割，或直接作为单个句子
                semantic_sentences = self.semantic_splitter.split_sentences_semantic(text.strip())
                new_sentences.extend(semantic_sentences)
                logger.debug(f"   → 分割成 {len(semantic_sentences)} 个语义句子")
        
        if not new_sentences:
            logger.warning("⚠️ 语义分割后没有有效句子，跳过插入")
            return
        
        logger.info(f"🎯 准备插入 {len(new_sentences)} 个语义句子，使用策略: {policy.value}")
        
        # 计算插入位置前先记录原始状态
        original_total = len(self.sentences)
        remaining_before_insert = original_total - self.current_index
        
        # 🚀 基于播放进度的智能插入位置计算（双进度管理核心）
        current_state = self.state_manager.get_current_state()
        current_phase = self.state_manager.get_current_phase()
        
        # 获取当前播放进度（用户正在听的句子）
        playing_index = self.progress_tracker.get_current_playing_index()
        qa_ready_time = time.time() * 1000  # QA准备完成的时间
        
        if policy == InsertionPolicy.AFTER_SENTENCE:
            # 🎯 关键改动：基于播放进度而不是发送进度
            # 找到用户听完当前句子后的插入点
            insert_position = self.progress_tracker.find_qa_insertion_point(qa_ready_time)
            
            # 确保插入位置不会回退到已发送的位置之前
            if insert_position <= self.current_index:
                # 如果计算的插入位置已经被发送，插入到下一个未发送的位置
                insert_position = self.current_index + 1
                logger.info(f"📍 AFTER_SENTENCE插入策略 - 调整到发送队列末尾 {insert_position} (避免回退)")
            else:
                logger.info(f"📍 AFTER_SENTENCE插入策略 - 基于播放进度插入到位置 {insert_position} "
                           f"(用户正在听第 {playing_index + 1} 句)")
            
        elif policy == InsertionPolicy.AFTER_PARAGRAPH:
            # 段落结束后插入，但也要考虑播放进度
            paragraph_end = self._find_next_paragraph_end()
            playing_based_position = self.progress_tracker.find_qa_insertion_point(qa_ready_time)
            
            # 取两者的最大值，确保在段落结束且用户听完后插入
            insert_position = max(paragraph_end, playing_based_position)
            
            # 确保不回退
            if insert_position <= self.current_index:
                insert_position = self.current_index + 1
            
            delay_sentences = insert_position - playing_index
            logger.info(f"📍 AFTER_PARAGRAPH插入策略 - 插入到位置 {insert_position} "
                       f"(用户听完后延后 {delay_sentences} 个句子)")
            
        else:
            # 默认：基于播放进度
            insert_position = self.progress_tracker.find_qa_insertion_point(qa_ready_time)
            if insert_position <= self.current_index:
                insert_position = self.current_index + 1
            logger.info(f"📍 DEFAULT插入策略 - 基于播放进度插入到位置 {insert_position}")
        
        # 计算延迟影响
        sentences_delayed = insert_position - self.current_index
        logger.info(f"⏰ QA插播影响分析 - 将在 {sentences_delayed} 个句子后播放 (剩余 {remaining_before_insert} → {remaining_before_insert + len(new_sentences)} 个句子)")
        
        # 执行插入
        for i, sentence in enumerate(new_sentences):
            self.sentences.insert(insert_position + i, sentence)
            # 🚀 记录QA插入到进度追踪器（预先记录，用于后续播放时识别）
            # 注意：这里只是标记，实际的时间记录会在播放时进行
        
        # 插入完成后的详细日志
        insertion_end = datetime.now()
        processing_time = (insertion_end - insertion_start).total_seconds() * 1000
        
        logger.info(f"✅ QA插播完成 - 插入 {len(new_sentences)} 个句子到位置 {insert_position}")
        logger.info(f"🔢 播放队列更新 - 总句子: {original_total} → {len(self.sentences)} (+{len(new_sentences)})")
        logger.info(f"⚡ QA插入处理耗时: {processing_time:.1f}ms")
    
    def _find_next_paragraph_end(self) -> int:
        """查找下一个段落结束位置（简化实现）"""
        # 从当前位置往后找，寻找句子文本中的段落分隔符
        for i in range(self.current_index + 1, len(self.sentences)):
            sentence = self.sentences[i]
            # 简单策略：句子以句号结尾且较短，可能是段落结尾
            if (sentence.text.strip().endswith(('。', '.', '！', '!', '？', '?')) and 
                len(sentence.text.strip()) < 50):
                return i + 1
        
        # 如果找不到合适的段落结尾，默认在当前句子后插入
        return self.current_index + 1
    
    async def _do_pause(self):
        """内部暂停实现 - 🔧 使用状态机管理"""
        self._sync_state_with_state_machine(PlayerState.PAUSED, "pause_command")
        # 🚀 通知进度追踪器暂停
        self.progress_tracker.handle_pause()
        logger.debug("⏸️ Internal pause executed with progress tracker notified")
    
    async def _do_resume(self):
        """内部恢复实现 - 🔧 使用状态机管理"""
        # 🚀 通知进度追踪器恢复
        self.progress_tracker.handle_resume()
        
        # 恢复到之前的播放状态
        if self._playing_state:
            # 如果还在播放过程中，恢复到空闲状态等待下一句
            self._sync_state_with_state_machine(PlayerState.IDLE, "resume_command")
        else:
            # 如果不在播放，保持空闲状态
            self._sync_state_with_state_machine(PlayerState.IDLE, "resume_idle")
        logger.debug("▶️ Internal resume executed with progress tracker notified")
    
    async def cleanup(self) -> None:
        """清理资源 - 🔧 包含状态机和进度追踪器清理"""
        try:
            await self.stop()
            if self.tts_engine:
                await self.tts_engine.cleanup()
            if self.semantic_splitter:
                self.semantic_splitter.clear_cache()
            
            # 最终状态机和进度追踪器重置
            self.state_manager.reset()
            self.progress_tracker.reset()
            logger.debug("🧹 MainContentPlayer cleanup completed with state machine and tracker reset")
        except Exception as e:
            logger.error(f"❌ Cleanup error: {e}")


# 工厂函数 - 已废弃
def get_main_content_player():
    """
    获取 MainContentPlayer 实例
    
    @deprecated 此函数已废弃！
    请使用: from ai_live_streamer.core.dependencies import get_main_content_player
    
    Raises:
        DeprecationWarning: 总是抛出废弃警告
    """
    raise DeprecationWarning(
        "此函数已废弃！请使用 'from ai_live_streamer.core.dependencies import get_main_content_player' "
        "来获取 MainContentPlayer 单例实例。"
    )


# 简单使用示例
async def example_usage():
    """使用示例"""
    player = MainContentPlayer()
    
    try:
        # 测试文本
        test_content = """
        大家好，欢迎来到我们的直播间！今天为大家介绍一款优秀的产品。
        这个产品质量很好，价格实惠，现在还有特价优惠。
        如果您有任何问题，欢迎随时提问！感谢大家的观看和支持。
        """
        
        logger.info("🎬 Starting example playback")
        await player._legacy_play_v1_deprecated(test_content)
        
    except Exception as e:
        logger.error(f"❌ Example failed: {e}")
    finally:
        await player.cleanup()


if __name__ == "__main__":
    asyncio.run(example_usage())