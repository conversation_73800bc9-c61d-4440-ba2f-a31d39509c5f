"""
运营表单与商品关联服务
提供表单与商品之间的完整关联功能
"""

import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from loguru import logger

from .persistence.database_manager import DatabaseManager
from .simple_product_service import SimpleProductService
from ..core.exceptions import ServiceError, ValidationError
from ..core.config import cfg


class FormProductAssociationService:
    """运营表单与商品关联服务"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        if db_manager is None:
            db_manager = DatabaseManager(cfg.database_path)
        self.db_manager = db_manager
        self.product_service = SimpleProductService(db_manager)
    
    def get_form_with_product_info(self, form_id: str) -> Dict[str, Any]:
        """获取包含商品信息的表单数据
        
        Args:
            form_id: 表单ID
            
        Returns:
            包含商品详细信息的表单数据
            
        Raises:
            ServiceError: 数据库操作失败
            ValidationError: 表单不存在
        """
        try:
            # 获取表单基本信息
            form_sql = """
                SELECT f.*, p.sku as product_sku, p.name as product_name, 
                       p.category as product_category, p.price as product_price,
                       p.stock as product_stock, p.description as product_description
                FROM operational_forms f
                LEFT JOIN products p ON f.selected_product_id = p.id
                WHERE f.id = ?
            """
            
            result = self.db_manager.execute_query(form_sql, (form_id,))
            if not result:
                raise ValidationError(f"表单 {form_id} 不存在")
            
            form_data = dict(result[0])
            
            # 解析商品价格配置
            if form_data['product_price_config']:
                try:
                    price_config = json.loads(form_data['product_price_config'])
                    form_data['product_price_config'] = price_config
                except json.JSONDecodeError:
                    logger.warning(f"表单 {form_id} 的价格配置JSON解析失败")
                    form_data['product_price_config'] = None
            
            # 如果有关联商品，获取商品的QA信息
            if form_data['selected_product_id']:
                try:
                    qa_list = self.product_service.get_product_qa(form_data['selected_product_id'])
                    form_data['associated_product_qa'] = qa_list
                except Exception as e:
                    logger.warning(f"获取商品QA信息失败: {e}")
                    form_data['associated_product_qa'] = []
            else:
                form_data['associated_product_qa'] = []
            
            logger.debug(f"获取表单 {form_id} 及关联商品信息成功")
            return form_data
            
        except Exception as e:
            logger.error(f"获取表单商品关联信息失败: {e}")
            raise ServiceError(f"获取表单商品关联信息失败: {e}")
    
    def associate_form_with_product(self, form_id: str, product_id: int, 
                                   price_config: Dict[str, Any] = None) -> bool:
        """将表单与商品关联
        
        Args:
            form_id: 表单ID
            product_id: 商品ID
            price_config: 价格配置
            
        Returns:
            关联是否成功
            
        Raises:
            ServiceError: 数据库操作失败
            ValidationError: 表单或商品不存在
        """
        try:
            # 验证表单存在
            form_check = self.db_manager.execute_query(
                "SELECT id FROM operational_forms WHERE id = ?", (form_id,)
            )
            if not form_check:
                raise ValidationError(f"表单 {form_id} 不存在")
            
            # 验证商品存在
            try:
                product = self.product_service.get_product(product_id)
            except Exception:
                raise ValidationError(f"商品 {product_id} 不存在")
            
            # 准备价格配置
            if price_config is None:
                price_config = {
                    "use_custom_price": False,
                    "custom_streaming_price": None
                }
            
            price_config_json = json.dumps(price_config)
            
            # 更新表单关联信息
            update_sql = """
                UPDATE operational_forms 
                SET selected_product_id = ?, 
                    product_price_config = ?,
                    product_associated_at = CURRENT_TIMESTAMP,
                    last_modified_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            
            affected_rows = self.db_manager.execute_update(
                update_sql, (product_id, price_config_json, form_id)
            )
            
            if affected_rows == 0:
                raise ServiceError("更新表单关联信息失败")
            
            logger.info(f"表单 {form_id} 已关联到商品 {product_id}")
            return True
            
        except Exception as e:
            logger.error(f"关联表单与商品失败: {e}")
            raise ServiceError(f"关联表单与商品失败: {e}")
    
    def remove_form_product_association(self, form_id: str) -> bool:
        """移除表单与商品的关联
        
        Args:
            form_id: 表单ID
            
        Returns:
            移除是否成功
        """
        try:
            update_sql = """
                UPDATE operational_forms 
                SET selected_product_id = NULL, 
                    product_price_config = NULL,
                    product_associated_at = NULL,
                    last_modified_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            
            affected_rows = self.db_manager.execute_update(update_sql, (form_id,))
            
            if affected_rows == 0:
                raise ValidationError(f"表单 {form_id} 不存在")
            
            logger.info(f"表单 {form_id} 的商品关联已移除")
            return True
            
        except Exception as e:
            logger.error(f"移除表单商品关联失败: {e}")
            raise ServiceError(f"移除表单商品关联失败: {e}")
    
    def get_forms_by_product(self, product_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """获取使用指定商品的表单列表
        
        Args:
            product_id: 商品ID
            limit: 返回数量限制
            
        Returns:
            表单列表
        """
        try:
            sql = """
                SELECT f.id, f.created_by, f.created_at, f.last_modified_at,
                       f.current_section, f.completion_percentage, f.is_complete,
                       f.is_submitted, f.is_processed, f.product_associated_at,
                       f.product_price_config,
                       p.sku as product_sku, p.name as product_name
                FROM operational_forms f
                INNER JOIN products p ON f.selected_product_id = p.id
                WHERE f.selected_product_id = ?
                ORDER BY f.product_associated_at DESC
                LIMIT ?
            """
            
            results = self.db_manager.execute_query(sql, (product_id, limit))
            
            forms = []
            for row in results:
                form_data = dict(row)
                
                # 解析价格配置
                if form_data['product_price_config']:
                    try:
                        form_data['product_price_config'] = json.loads(form_data['product_price_config'])
                    except json.JSONDecodeError:
                        form_data['product_price_config'] = None
                
                forms.append(form_data)
            
            logger.debug(f"找到 {len(forms)} 个使用商品 {product_id} 的表单")
            return forms
            
        except Exception as e:
            logger.error(f"查询商品关联表单失败: {e}")
            raise ServiceError(f"查询商品关联表单失败: {e}")
    
    def get_product_usage_stats(self, product_id: int) -> Dict[str, Any]:
        """获取商品使用统计
        
        Args:
            product_id: 商品ID
            
        Returns:
            商品使用统计信息
        """
        try:
            stats_sql = """
                SELECT 
                    COUNT(*) as total_forms,
                    COUNT(CASE WHEN is_submitted = 1 THEN 1 END) as submitted_forms,
                    COUNT(CASE WHEN is_processed = 1 THEN 1 END) as processed_forms,
                    MIN(product_associated_at) as first_used_at,
                    MAX(product_associated_at) as last_used_at
                FROM operational_forms 
                WHERE selected_product_id = ?
            """
            
            result = self.db_manager.execute_query(stats_sql, (product_id,))
            if not result:
                return {
                    "total_forms": 0,
                    "submitted_forms": 0,
                    "processed_forms": 0,
                    "first_used_at": None,
                    "last_used_at": None
                }
            
            stats = dict(result[0])
            
            # 价格配置统计
            price_config_sql = """
                SELECT product_price_config
                FROM operational_forms 
                WHERE selected_product_id = ? AND product_price_config IS NOT NULL
            """
            
            price_configs = self.db_manager.execute_query(price_config_sql, (product_id,))
            
            custom_price_count = 0
            original_price_count = 0
            
            for row in price_configs:
                try:
                    config = json.loads(row[0])
                    if config.get('use_custom_price'):
                        custom_price_count += 1
                    else:
                        original_price_count += 1
                except json.JSONDecodeError:
                    continue
            
            stats.update({
                "custom_price_usage": custom_price_count,
                "original_price_usage": original_price_count
            })
            
            return stats
            
        except Exception as e:
            logger.error(f"获取商品使用统计失败: {e}")
            raise ServiceError(f"获取商品使用统计失败: {e}")
    
    def get_association_summary(self) -> Dict[str, Any]:
        """获取关联关系总体统计
        
        Returns:
            关联关系统计信息
        """
        try:
            summary_sql = """
                SELECT 
                    COUNT(*) as total_forms,
                    COUNT(selected_product_id) as forms_with_products,
                    COUNT(DISTINCT selected_product_id) as unique_products_used,
                    COUNT(CASE WHEN is_submitted = 1 AND selected_product_id IS NOT NULL THEN 1 END) as submitted_with_products
                FROM operational_forms
            """
            
            result = self.db_manager.execute_query(summary_sql)
            summary = dict(result[0])
            
            # 最常用的商品
            popular_products_sql = """
                SELECT p.id, p.sku, p.name, COUNT(*) as usage_count
                FROM operational_forms f
                INNER JOIN products p ON f.selected_product_id = p.id
                GROUP BY p.id, p.sku, p.name
                ORDER BY usage_count DESC
                LIMIT 5
            """
            
            popular_products = self.db_manager.execute_query(popular_products_sql)
            summary['popular_products'] = [dict(row) for row in popular_products]
            
            return summary
            
        except Exception as e:
            logger.error(f"获取关联关系统计失败: {e}")
            raise ServiceError(f"获取关联关系统计失败: {e}")