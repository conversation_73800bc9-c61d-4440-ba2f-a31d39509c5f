"""Duration Management Engine

Provides simple duration management for live streams including content loop playback,
manual extension capabilities, and adaptive duration control based on planned vs actual timing.
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable, Tuple
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass
from loguru import logger

from ..services.script_previewer import ScriptTimeline, ScriptSegment, ScriptSegmentType, PriorityLevel
from ..models.state import LiveStreamState, StreamStatus
from ..core.exceptions import ServiceError


class ExtensionMode(Enum):
    """Methods for extending stream duration"""
    LOOP_COMPLETE = "loop_complete"  # Loop entire content
    LOOP_SEGMENTS = "loop_segments"  # Loop specific segments
    EXTEND_INTERACTION = "extend_interaction"  # Add more interaction time
    EMERGENCY_FILLER = "emergency_filler"  # Use emergency filler content
    MANUAL_PAUSE = "manual_pause"  # Manual pause extension


class DurationStatus(Enum):
    """Current duration management status"""
    ON_TRACK = "on_track"  # Within planned timing
    AHEAD_OF_SCHEDULE = "ahead_of_schedule"  # Finishing too early
    BEHIND_SCHEDULE = "behind_schedule"  # Running too long
    EXTENSION_NEEDED = "extension_needed"  # Need to extend content
    MANUAL_INTERVENTION = "manual_intervention"  # Requires manual control


@dataclass
class DurationMetrics:
    """Current duration tracking metrics"""
    planned_duration_minutes: int
    elapsed_minutes: float
    remaining_planned_minutes: float
    estimated_completion_minutes: float
    schedule_variance_minutes: float  # positive = ahead, negative = behind
    completion_percentage: float
    current_pace_words_per_minute: float
    segments_completed: int
    segments_remaining: int
    
    @property
    def is_ahead_of_schedule(self) -> bool:
        return self.schedule_variance_minutes > 2.0  # More than 2 minutes ahead
    
    @property
    def is_behind_schedule(self) -> bool:
        return self.schedule_variance_minutes < -3.0  # More than 3 minutes behind
    
    @property
    def needs_extension(self) -> bool:
        return (self.completion_percentage > 85.0 and 
                self.remaining_planned_minutes > 10.0)


@dataclass
class ExtensionPlan:
    """Plan for extending stream duration"""
    extension_mode: ExtensionMode
    target_additional_minutes: int
    segments_to_repeat: List[str]  # segment IDs to repeat
    interaction_extensions: List[Dict[str, Any]]  # additional interaction segments
    filler_content: List[ScriptSegment]  # emergency filler segments
    estimated_execution_time: float  # seconds to implement
    confidence_score: float  # 0-1 confidence in plan effectiveness


class DurationManagementEngine:
    """
    Simple duration management engine for live streams.
    
    Provides content looping, manual extension, and adaptive duration control
    to maintain planned stream duration with minimal manual intervention.
    """
    
    def __init__(self):
        self.logger = logger.bind(component="duration_management_engine")
        
        # Duration tracking
        self.start_time: Optional[datetime] = None
        self.planned_end_time: Optional[datetime] = None
        self.current_timeline: Optional[ScriptTimeline] = None
        self.original_timeline: Optional[ScriptTimeline] = None
        
        # Loop management
        self.loop_enabled = True
        self.max_loops = 3  # Maximum number of complete loops
        self.current_loop_count = 0
        self.looped_segments: List[str] = []  # Track which segments have been looped
        
        # Extension settings
        self.auto_extension_enabled = True
        self.min_extension_minutes = 5
        self.max_extension_minutes = 30
        self.extension_threshold_percentage = 85.0  # When to start considering extension
        
        # Segment preferences for looping
        self.loop_priority = {
            ScriptSegmentType.SELLING_POINT: 1.0,
            ScriptSegmentType.PRODUCT_INTRO: 0.9,
            ScriptSegmentType.CALL_TO_ACTION: 0.8,
            ScriptSegmentType.INTERACTION: 0.7,
            ScriptSegmentType.PRICE_ANNOUNCEMENT: 0.6,
            ScriptSegmentType.TRANSITION: 0.3,
            ScriptSegmentType.OPENING: 0.2,
            ScriptSegmentType.CLOSING: 0.1,
            ScriptSegmentType.EMERGENCY_FILLER: 0.0
        }
        
        # Callbacks
        self.on_extension_needed: Optional[Callable[[ExtensionPlan], None]] = None
        self.on_loop_started: Optional[Callable[[List[ScriptSegment]], None]] = None
        self.on_duration_warning: Optional[Callable[[DurationMetrics], None]] = None
        
        # Statistics
        self.stats = {
            "sessions_managed": 0,
            "total_extensions": 0,
            "total_loops": 0,
            "manual_interventions": 0,
            "average_schedule_variance": 0.0
        }
    
    def initialize_session(
        self, 
        timeline: ScriptTimeline, 
        live_stream_state: LiveStreamState
    ) -> bool:
        """Initialize duration management for a new streaming session
        
        Args:
            timeline: Script timeline to manage
            live_stream_state: Current live stream state
            
        Returns:
            True if initialized successfully
        """
        try:
            self.logger.info(f"Initializing duration management for {timeline.total_duration_minutes} minute stream")
            
            # Store timeline references
            self.current_timeline = timeline
            self.original_timeline = self._deep_copy_timeline(timeline)
            
            # Initialize timing
            self.start_time = datetime.utcnow()
            self.planned_end_time = self.start_time + timedelta(minutes=timeline.total_duration_minutes)
            
            # Reset counters
            self.current_loop_count = 0
            self.looped_segments.clear()
            
            # Update statistics
            self.stats["sessions_managed"] += 1
            
            self.logger.info(f"Duration management initialized - planned end: {self.planned_end_time}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize duration management: {e}")
            return False
    
    def get_current_metrics(self, current_segment_index: int) -> DurationMetrics:
        """Get current duration tracking metrics
        
        Args:
            current_segment_index: Index of currently playing segment
            
        Returns:
            Current duration metrics
        """
        if not self.start_time or not self.current_timeline:
            raise ServiceError("Duration management not initialized", "duration_management")
        
        now = datetime.utcnow()
        elapsed_seconds = (now - self.start_time).total_seconds()
        elapsed_minutes = elapsed_seconds / 60.0
        
        # Calculate planned vs actual progress
        total_segments = len(self.current_timeline.segments)
        segments_completed = current_segment_index
        completion_percentage = (segments_completed / total_segments) * 100.0 if total_segments > 0 else 0.0
        
        # Estimate remaining time based on current pace
        if segments_completed > 0:
            average_segment_time = elapsed_minutes / segments_completed
            estimated_remaining_minutes = average_segment_time * (total_segments - segments_completed)
        else:
            estimated_remaining_minutes = self.current_timeline.total_duration_minutes
        
        estimated_completion_minutes = elapsed_minutes + estimated_remaining_minutes
        planned_duration = self.current_timeline.total_duration_minutes
        schedule_variance = planned_duration - estimated_completion_minutes
        
        # Calculate current pace
        total_words = sum(len(segment.content.split()) for segment in self.current_timeline.segments[:current_segment_index])
        current_pace = total_words / elapsed_minutes if elapsed_minutes > 0 and total_words > 0 else 0.0
        
        return DurationMetrics(
            planned_duration_minutes=planned_duration,
            elapsed_minutes=elapsed_minutes,
            remaining_planned_minutes=max(0, planned_duration - elapsed_minutes),
            estimated_completion_minutes=estimated_completion_minutes,
            schedule_variance_minutes=schedule_variance,
            completion_percentage=completion_percentage,
            current_pace_words_per_minute=current_pace,
            segments_completed=segments_completed,
            segments_remaining=total_segments - segments_completed
        )
    
    def check_duration_status(self, current_segment_index: int) -> DurationStatus:
        """Check current duration management status
        
        Args:
            current_segment_index: Index of currently playing segment
            
        Returns:
            Current duration status
        """
        metrics = self.get_current_metrics(current_segment_index)
        
        if metrics.needs_extension:
            return DurationStatus.EXTENSION_NEEDED
        elif metrics.is_ahead_of_schedule:
            return DurationStatus.AHEAD_OF_SCHEDULE
        elif metrics.is_behind_schedule:
            return DurationStatus.BEHIND_SCHEDULE
        else:
            return DurationStatus.ON_TRACK
    
    async def handle_extension_needed(
        self, 
        current_segment_index: int,
        target_additional_minutes: Optional[int] = None
    ) -> ExtensionPlan:
        """Handle situation where stream extension is needed
        
        Args:
            current_segment_index: Current segment index
            target_additional_minutes: Target additional minutes (auto-calculated if None)
            
        Returns:
            Extension plan to implement
        """
        metrics = self.get_current_metrics(current_segment_index)
        
        if target_additional_minutes is None:
            target_additional_minutes = max(
                self.min_extension_minutes,
                min(self.max_extension_minutes, int(metrics.remaining_planned_minutes))
            )
        
        self.logger.info(f"Planning extension for {target_additional_minutes} additional minutes")
        
        # Determine best extension mode
        extension_mode = self._determine_extension_mode(metrics, target_additional_minutes)
        
        # Create extension plan
        plan = await self._create_extension_plan(
            extension_mode, 
            target_additional_minutes, 
            current_segment_index
        )
        
        # Call callback if registered
        if self.on_extension_needed:
            self.on_extension_needed(plan)
        
        return plan
    
    async def implement_extension_plan(self, plan: ExtensionPlan) -> bool:
        """Implement an extension plan
        
        Args:
            plan: Extension plan to implement
            
        Returns:
            True if plan implemented successfully
        """
        try:
            self.logger.info(f"Implementing extension plan: {plan.extension_mode.value}")
            
            if plan.extension_mode == ExtensionMode.LOOP_COMPLETE:
                success = await self._implement_complete_loop()
            elif plan.extension_mode == ExtensionMode.LOOP_SEGMENTS:
                success = await self._implement_segment_loop(plan.segments_to_repeat)
            elif plan.extension_mode == ExtensionMode.EXTEND_INTERACTION:
                success = await self._implement_interaction_extension(plan.interaction_extensions)
            elif plan.extension_mode == ExtensionMode.EMERGENCY_FILLER:
                success = await self._implement_emergency_filler(plan.filler_content)
            else:
                success = False
            
            if success:
                self.stats["total_extensions"] += 1
                self.logger.info(f"Extension plan implemented successfully")
            else:
                self.logger.error(f"Failed to implement extension plan")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to implement extension plan: {e}")
            return False
    
    def enable_content_loop(self, max_loops: int = 3) -> None:
        """Enable content looping with specified maximum loops
        
        Args:
            max_loops: Maximum number of loops allowed
        """
        self.loop_enabled = True
        self.max_loops = max_loops
        self.logger.info(f"Content looping enabled with max {max_loops} loops")
    
    def disable_content_loop(self) -> None:
        """Disable content looping"""
        self.loop_enabled = False
        self.logger.info("Content looping disabled")
    
    def manual_extend_duration(self, additional_minutes: int) -> bool:
        """Manually extend stream duration
        
        Args:
            additional_minutes: Minutes to add to stream
            
        Returns:
            True if extension applied successfully
        """
        try:
            if not self.planned_end_time:
                raise ServiceError("No active session to extend", "duration_management")
            
            # Extend planned end time
            self.planned_end_time += timedelta(minutes=additional_minutes)
            
            # Update timeline duration
            if self.current_timeline:
                self.current_timeline.total_duration_minutes += additional_minutes
            
            self.stats["manual_interventions"] += 1
            self.logger.info(f"Manually extended duration by {additional_minutes} minutes")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to manually extend duration: {e}")
            return False
    
    def get_loop_candidates(self) -> List[ScriptSegment]:
        """Get segments that are good candidates for looping
        
        Returns:
            List of segments suitable for looping, sorted by priority
        """
        if not self.current_timeline:
            return []
        
        candidates = []
        for segment in self.current_timeline.segments:
            if (segment.segment_id not in self.looped_segments and 
                segment.segment_type in self.loop_priority):
                priority = self.loop_priority[segment.segment_type]
                candidates.append((segment, priority))
        
        # Sort by priority (higher first)
        candidates.sort(key=lambda x: x[1], reverse=True)
        return [segment for segment, _ in candidates]
    
    def _determine_extension_mode(
        self, 
        metrics: DurationMetrics, 
        target_minutes: int
    ) -> ExtensionMode:
        """Determine the best extension mode for current situation"""
        
        # If we have few segments remaining and need significant extension
        if metrics.segments_remaining < 3 and target_minutes > 10:
            return ExtensionMode.LOOP_COMPLETE
        
        # If we have good loop candidates
        loop_candidates = self.get_loop_candidates()
        if len(loop_candidates) >= 2 and target_minutes <= 15:
            return ExtensionMode.LOOP_SEGMENTS
        
        # If we need moderate extension and have interaction points
        if 5 <= target_minutes <= 15 and hasattr(self.current_timeline, 'interaction_points'):
            return ExtensionMode.EXTEND_INTERACTION
        
        # Fallback to emergency filler
        return ExtensionMode.EMERGENCY_FILLER
    
    async def _create_extension_plan(
        self, 
        mode: ExtensionMode, 
        target_minutes: int, 
        current_index: int
    ) -> ExtensionPlan:
        """Create detailed extension plan"""
        
        segments_to_repeat = []
        interaction_extensions = []
        filler_content = []
        confidence_score = 0.7  # Default confidence
        
        if mode == ExtensionMode.LOOP_SEGMENTS:
            candidates = self.get_loop_candidates()[:3]  # Top 3 candidates
            segments_to_repeat = [seg.segment_id for seg in candidates]
            confidence_score = 0.8 if len(candidates) >= 2 else 0.6
            
        elif mode == ExtensionMode.EXTEND_INTERACTION:
            interaction_extensions = [
                {"type": "qa_session", "duration_minutes": min(5, target_minutes // 2)},
                {"type": "product_demo", "duration_minutes": min(8, target_minutes // 2)}
            ]
            confidence_score = 0.7
            
        elif mode == ExtensionMode.EMERGENCY_FILLER:
            filler_content = self._create_emergency_filler_segments(target_minutes)
            confidence_score = 0.5  # Lower confidence for filler
        
        return ExtensionPlan(
            extension_mode=mode,
            target_additional_minutes=target_minutes,
            segments_to_repeat=segments_to_repeat,
            interaction_extensions=interaction_extensions,
            filler_content=filler_content,
            estimated_execution_time=30.0,  # Seconds to implement
            confidence_score=confidence_score
        )
    
    async def _implement_complete_loop(self) -> bool:
        """Implement complete content loop"""
        if not self.current_timeline or not self.loop_enabled:
            return False
        
        if self.current_loop_count >= self.max_loops:
            self.logger.warning(f"Maximum loops ({self.max_loops}) reached")
            return False
        
        # Add all segments to timeline again
        loop_segments = [seg for seg in self.original_timeline.segments 
                        if seg.segment_type != ScriptSegmentType.OPENING]  # Skip opening in loops
        
        self.current_timeline.segments.extend(loop_segments)
        self.current_timeline.total_duration_minutes += sum(
            seg.estimated_duration_seconds // 60 for seg in loop_segments
        )
        
        self.current_loop_count += 1
        self.stats["total_loops"] += 1
        
        if self.on_loop_started:
            self.on_loop_started(loop_segments)
        
        self.logger.info(f"Implemented complete loop #{self.current_loop_count}")
        return True
    
    async def _implement_segment_loop(self, segment_ids: List[str]) -> bool:
        """Implement specific segment looping"""
        if not self.current_timeline:
            return False
        
        segments_to_add = []
        for seg_id in segment_ids:
            for segment in self.original_timeline.segments:
                if segment.segment_id == seg_id:
                    segments_to_add.append(segment)
                    self.looped_segments.append(seg_id)
                    break
        
        if segments_to_add:
            self.current_timeline.segments.extend(segments_to_add)
            additional_duration = sum(seg.estimated_duration_seconds // 60 for seg in segments_to_add)
            self.current_timeline.total_duration_minutes += additional_duration
            
            self.logger.info(f"Looped {len(segments_to_add)} segments, added {additional_duration} minutes")
            return True
        
        return False
    
    async def _implement_interaction_extension(self, extensions: List[Dict[str, Any]]) -> bool:
        """Implement interaction time extensions"""
        # This would integrate with interaction systems
        self.logger.info(f"Extended interaction time with {len(extensions)} activities")
        return True
    
    async def _implement_emergency_filler(self, filler_segments: List[ScriptSegment]) -> bool:
        """Implement emergency filler content"""
        if not self.current_timeline:
            return False
        
        self.current_timeline.segments.extend(filler_segments)
        additional_duration = sum(seg.estimated_duration_seconds // 60 for seg in filler_segments)
        self.current_timeline.total_duration_minutes += additional_duration
        
        self.logger.info(f"Added {len(filler_segments)} filler segments, {additional_duration} minutes")
        return True
    
    def _create_emergency_filler_segments(self, target_minutes: int) -> List[ScriptSegment]:
        """Create emergency filler segments"""
        filler_segments = []
        
        # Create product recap segment
        if target_minutes >= 3:
            filler_segments.append(ScriptSegment(
                segment_id=f"filler_recap_{datetime.now().strftime('%H%M%S')}",
                segment_type=ScriptSegmentType.EMERGENCY_FILLER,
                title="产品要点回顾",
                content="让我们再次回顾一下今天介绍的产品主要特点和优势",
                estimated_duration_seconds=180,
                priority=PriorityLevel.LOW,
                triggers=["duration_extension"],
                variables={}
            ))
        
        # Create interaction segment
        if target_minutes >= 5:
            filler_segments.append(ScriptSegment(
                segment_id=f"filler_interaction_{datetime.now().strftime('%H%M%S')}",
                segment_type=ScriptSegmentType.INTERACTION,
                title="观众互动时间",
                content="现在有时间回答更多观众朋友的问题，大家有什么想了解的可以在评论区留言",
                estimated_duration_seconds=300,
                priority=PriorityLevel.MEDIUM,
                triggers=["duration_extension"],
                variables={}
            ))
        
        return filler_segments
    
    def _deep_copy_timeline(self, timeline: ScriptTimeline) -> ScriptTimeline:
        """Create a deep copy of timeline"""
        from copy import deepcopy
        return deepcopy(timeline)
    
    def get_management_stats(self) -> Dict[str, Any]:
        """Get duration management statistics"""
        return {
            "stats": self.stats.copy(),
            "configuration": {
                "loop_enabled": self.loop_enabled,
                "max_loops": self.max_loops,
                "auto_extension_enabled": self.auto_extension_enabled,
                "min_extension_minutes": self.min_extension_minutes,
                "max_extension_minutes": self.max_extension_minutes
            },
            "current_session": {
                "is_active": self.start_time is not None,
                "current_loop_count": self.current_loop_count,
                "looped_segments_count": len(self.looped_segments)
            }
        }


# Global duration management engine instance
_duration_engine: Optional[DurationManagementEngine] = None


def get_duration_management_engine() -> DurationManagementEngine:
    """Get global duration management engine instance"""
    global _duration_engine
    if _duration_engine is None:
        _duration_engine = DurationManagementEngine()
    return _duration_engine