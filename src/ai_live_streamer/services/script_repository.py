"""
Script Repository Service

Database operations for script persistence, including CRUD operations,
search, analytics, and batch processing with optimized performance.
"""

import sqlite3
import json
import asyncio
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
from contextlib import contextmanager, asynccontextmanager
from concurrent.futures import ThreadPoolExecutor
from loguru import logger

from ..models.script_persistence import (
    PersistedScript, PersistedScriptSegment, StreamingSession,
    ScriptQuery, ScriptAnalytics, GenerationMethod, ScriptStatus,
    ScriptSearchIndex, ScriptValidationResult, PersistenceConfig
)
from ..database.migrations import DatabaseMigrator
from ..core.exceptions import ServiceError


class ScriptRepository:
    """
    High-performance repository for script persistence operations
    with async support, connection pooling, and comprehensive querying.
    """
    
    def __init__(self, config: Optional[PersistenceConfig] = None):
        self.config = config or PersistenceConfig()
        self.migrator = DatabaseMigrator(self.config)
        self._executor = ThreadPoolExecutor(max_workers=self.config.connection_pool_size)
        self._initialized = False
        
        # Initialize database on startup
        self._ensure_initialized()
    
    def _ensure_initialized(self) -> None:
        """Ensure database is initialized"""
        if not self._initialized:
            success = self.migrator.ensure_database_exists()
            if not success:
                raise ServiceError("Failed to initialize script persistence database")
            self._initialized = True
            logger.info("Script repository initialized successfully")
    
    @contextmanager
    def _get_connection(self):
        """Get database connection with proper configuration"""
        conn = None
        try:
            conn = sqlite3.connect(
                str(self.migrator.db_path),
                timeout=30.0,
                check_same_thread=False
            )
            
            # Configure connection for optimal performance
            conn.execute("PRAGMA foreign_keys=ON")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA journal_mode=WAL")
            conn.row_factory = sqlite3.Row  # Enable column access by name
            
            yield conn
            
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
    
    # === Core CRUD Operations ===
    
    async def save_script(self, script: PersistedScript) -> bool:
        """Save a complete script with all segments"""
        try:
            return await self._run_in_executor(self._save_script_sync, script)
        except Exception as e:
            logger.error(f"Failed to save script {script.script_id}: {e}")
            return False
    
    def _save_script_sync(self, script: PersistedScript) -> bool:
        """Synchronous script save operation"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Insert or update main script record
            cursor.execute("""
                INSERT OR REPLACE INTO persisted_scripts (
                    script_id, form_id, generated_at, generation_method,
                    total_duration_seconds, segment_count, generation_time_ms,
                    html_preview, interaction_points, break_points,
                    adaptation_rules, estimated_metrics, generation_warnings,
                    persona_info, form_snapshot, status, usage_count,
                    last_used_at, created_by, tags, quality_score,
                    performance_metrics, user_feedback
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                script.script_id,
                script.form_id,
                script.generated_at,
                script.generation_method.value,
                script.total_duration_seconds,
                script.segment_count,
                script.generation_time_ms,
                script.html_preview,
                json.dumps(script.interaction_points),
                json.dumps(script.break_points),
                json.dumps(script.adaptation_rules),
                json.dumps(script.estimated_metrics),
                json.dumps(script.generation_warnings),
                json.dumps(script.persona_info),
                json.dumps(script.form_snapshot),
                script.status.value,
                script.usage_count,
                script.last_used_at,
                script.created_by,
                json.dumps(script.tags),
                script.quality_score,
                json.dumps(script.performance_metrics),
                json.dumps(script.user_feedback)
            ))
            
            # Delete existing segments for this script
            cursor.execute("DELETE FROM script_segments WHERE script_id = ?", (script.script_id,))
            
            # Insert all segments
            segment_data = []
            for segment in script.segments:
                segment_data.append((
                    segment.segment_id,
                    segment.script_id,
                    segment.segment_type,
                    segment.title,
                    segment.content,
                    segment.duration_seconds,
                    segment.priority,
                    segment.segment_order,
                    json.dumps(segment.triggers),
                    json.dumps(segment.variables),
                    json.dumps(segment.generation_metadata),
                    segment.created_at,
                    segment.updated_at,
                    segment.usage_count,
                    segment.last_used_at
                ))
            
            cursor.executemany("""
                INSERT OR REPLACE INTO script_segments (
                    segment_id, script_id, segment_type, title, content,
                    duration_seconds, priority, segment_order, triggers,
                    variables, generation_metadata, created_at, updated_at,
                    usage_count, last_used_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, segment_data)
            
            # Update search index
            self._update_search_index_sync(cursor, script)
            
            conn.commit()
            logger.debug(f"Saved script {script.script_id} with {len(script.segments)} segments")
            return True
    
    async def get_script(self, script_id: str) -> Optional[PersistedScript]:
        """Retrieve a complete script by ID"""
        try:
            return await self._run_in_executor(self._get_script_sync, script_id)
        except Exception as e:
            logger.error(f"Failed to retrieve script {script_id}: {e}")
            return None
    
    def _get_script_sync(self, script_id: str) -> Optional[PersistedScript]:
        """Synchronous script retrieval"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Get main script record
            cursor.execute("""
                SELECT * FROM persisted_scripts WHERE script_id = ?
            """, (script_id,))
            
            script_row = cursor.fetchone()
            if not script_row:
                return None
            
            # Get all segments
            cursor.execute("""
                SELECT * FROM script_segments 
                WHERE script_id = ? 
                ORDER BY segment_order
            """, (script_id,))
            
            segment_rows = cursor.fetchall()
            
            # Build script object
            return self._build_script_from_rows(dict(script_row), segment_rows)
    
    async def search_scripts(self, query: ScriptQuery) -> Tuple[List[PersistedScript], int]:
        """Search scripts with filtering and pagination"""
        try:
            return await self._run_in_executor(self._search_scripts_sync, query)
        except Exception as e:
            logger.error(f"Script search failed: {e}")
            return [], 0
    
    def _search_scripts_sync(self, query: ScriptQuery) -> Tuple[List[PersistedScript], int]:
        """Synchronous script search"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Build WHERE clause and parameters
            where_conditions = []
            params = []
            
            if query.form_id:
                where_conditions.append("form_id = ?")
                params.append(query.form_id)
            
            if query.generation_method:
                where_conditions.append("generation_method = ?")
                params.append(query.generation_method.value)
            
            if query.status:
                where_conditions.append("status = ?")
                params.append(query.status.value)
            
            if query.created_after:
                where_conditions.append("generated_at >= ?")
                params.append(query.created_after)
            
            if query.created_before:
                where_conditions.append("generated_at <= ?")
                params.append(query.created_before)
            
            if query.min_duration_seconds:
                where_conditions.append("total_duration_seconds >= ?")
                params.append(query.min_duration_seconds)
            
            if query.max_duration_seconds:
                where_conditions.append("total_duration_seconds <= ?")
                params.append(query.max_duration_seconds)
            
            if query.min_quality_score:
                where_conditions.append("quality_score >= ?")
                params.append(query.min_quality_score)
            
            if query.created_by:
                where_conditions.append("created_by = ?")
                params.append(query.created_by)
            
            if query.used_after:
                where_conditions.append("last_used_at >= ?")
                params.append(query.used_after)
            
            # Handle tags filtering (JSON contains)
            if query.tags:
                for tag in query.tags:
                    where_conditions.append("json_extract(tags, '$') LIKE ?")
                    params.append(f'%"{tag}"%')
            
            # Build final query
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            order_clause = f"ORDER BY {query.order_by} {'DESC' if query.order_desc else 'ASC'}"
            
            # Get total count for pagination
            count_query = f"SELECT COUNT(*) FROM persisted_scripts WHERE {where_clause}"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()[0]
            
            # Get paginated results
            search_query = f"""
                SELECT * FROM persisted_scripts 
                WHERE {where_clause} 
                {order_clause}
                LIMIT ? OFFSET ?
            """
            params.extend([query.limit, query.offset])
            cursor.execute(search_query, params)
            
            script_rows = cursor.fetchall()
            scripts = []
            
            # Load segments for each script
            for script_row in script_rows:
                script_dict = dict(script_row)
                
                # Get segments for this script
                cursor.execute("""
                    SELECT * FROM script_segments 
                    WHERE script_id = ? 
                    ORDER BY segment_order
                """, (script_dict['script_id'],))
                
                segment_rows = cursor.fetchall()
                script = self._build_script_from_rows(script_dict, segment_rows)
                if script:
                    scripts.append(script)
            
            return scripts, total_count
    
    async def update_script_usage(self, script_id: str, session: StreamingSession) -> bool:
        """Update script usage statistics"""
        try:
            return await self._run_in_executor(self._update_script_usage_sync, script_id, session)
        except Exception as e:
            logger.error(f"Failed to update script usage for {script_id}: {e}")
            return False
    
    def _update_script_usage_sync(self, script_id: str, session: StreamingSession) -> bool:
        """Synchronous usage update"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Update script usage count and timestamp
            cursor.execute("""
                UPDATE persisted_scripts 
                SET usage_count = usage_count + 1,
                    last_used_at = ?
                WHERE script_id = ?
            """, (session.started_at, script_id))
            
            # Insert session record
            cursor.execute("""
                INSERT INTO streaming_sessions (
                    session_id, script_id, started_at, ended_at,
                    segments_used, segments_skipped, total_viewers,
                    peak_viewers, average_engagement,
                    generation_to_use_delay_minutes,
                    actual_vs_planned_duration_ratio,
                    viewer_retention_rate, questions_received,
                    questions_answered, interaction_rate,
                    conversion_metrics, stream_title, persona_used,
                    platform_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                session.session_id, session.script_id, session.started_at,
                session.ended_at, session.segments_used, session.segments_skipped,
                session.total_viewers, session.peak_viewers, session.average_engagement,
                session.generation_to_use_delay_minutes,
                session.actual_vs_planned_duration_ratio, session.viewer_retention_rate,
                session.questions_received, session.questions_answered,
                session.interaction_rate, json.dumps(session.conversion_metrics),
                session.stream_title, session.persona_used,
                json.dumps(session.platform_data)
            ))
            
            # Update segment usage if applicable
            if hasattr(session, 'used_segment_ids'):
                for segment_id in session.used_segment_ids:
                    cursor.execute("""
                        UPDATE script_segments 
                        SET usage_count = usage_count + 1,
                            last_used_at = ?
                        WHERE segment_id = ?
                    """, (session.started_at, segment_id))
            
            conn.commit()
            return True
    
    async def delete_script(self, script_id: str, soft_delete: bool = True) -> bool:
        """Delete a script (soft or hard delete)"""
        try:
            return await self._run_in_executor(self._delete_script_sync, script_id, soft_delete)
        except Exception as e:
            logger.error(f"Failed to delete script {script_id}: {e}")
            return False
    
    def _delete_script_sync(self, script_id: str, soft_delete: bool) -> bool:
        """Synchronous script deletion"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            if soft_delete:
                # Mark as deleted
                cursor.execute("""
                    UPDATE persisted_scripts 
                    SET status = 'deleted' 
                    WHERE script_id = ?
                """, (script_id,))
            else:
                # Hard delete (cascades to segments and sessions)
                cursor.execute("DELETE FROM persisted_scripts WHERE script_id = ?", (script_id,))
                
                # Remove from search index
                cursor.execute("DELETE FROM script_search_index WHERE script_id = ?", (script_id,))
            
            conn.commit()
            return cursor.rowcount > 0
    
    # === Analytics and Reporting ===
    
    async def get_analytics(self, days: int = 30) -> ScriptAnalytics:
        """Get comprehensive analytics for specified time period"""
        try:
            return await self._run_in_executor(self._get_analytics_sync, days)
        except Exception as e:
            logger.error(f"Failed to get analytics: {e}")
            return self._get_empty_analytics()
    
    def _get_analytics_sync(self, days: int) -> ScriptAnalytics:
        """Synchronous analytics calculation"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Generation analytics
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_scripts,
                    AVG(generation_time_ms) as avg_generation_time,
                    AVG(quality_score) as avg_quality_score
                FROM persisted_scripts 
                WHERE generated_at >= ? AND status != 'deleted'
            """, (cutoff_date,))
            
            gen_stats = cursor.fetchone()
            
            # Generation by method
            cursor.execute("""
                SELECT generation_method, COUNT(*) 
                FROM persisted_scripts 
                WHERE generated_at >= ? AND status != 'deleted'
                GROUP BY generation_method
            """, (cutoff_date,))
            
            method_stats = dict(cursor.fetchall())
            
            # Usage analytics
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_sessions,
                    AVG(julianday(ended_at) - julianday(started_at)) * 24 * 60 as avg_duration_minutes,
                    AVG(average_engagement) as avg_engagement,
                    AVG(interaction_rate) as avg_interaction_rate,
                    AVG(viewer_retention_rate) as avg_retention_rate
                FROM streaming_sessions 
                WHERE started_at >= ?
                AND ended_at IS NOT NULL
            """, (cutoff_date,))
            
            usage_stats = cursor.fetchone()
            
            # Daily trends
            daily_generation = self._get_daily_trend(cursor, 'persisted_scripts', 'generated_at', days)
            daily_usage = self._get_daily_trend(cursor, 'streaming_sessions', 'started_at', days)
            daily_quality = self._get_daily_quality_trend(cursor, days)
            
            # Top performing scripts
            cursor.execute("""
                SELECT s.script_id
                FROM persisted_scripts s
                LEFT JOIN streaming_sessions ss ON s.script_id = ss.script_id
                WHERE s.generated_at >= ? AND s.status != 'deleted'
                GROUP BY s.script_id
                ORDER BY AVG(ss.average_engagement) DESC, s.usage_count DESC
                LIMIT 10
            """, (cutoff_date,))
            
            top_scripts = [row[0] for row in cursor.fetchall()]
            
            # Quality distribution
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN quality_score >= 0.8 THEN 'excellent'
                        WHEN quality_score >= 0.6 THEN 'good'
                        WHEN quality_score >= 0.4 THEN 'fair'
                        ELSE 'poor'
                    END as quality_range,
                    COUNT(*) as count
                FROM persisted_scripts 
                WHERE generated_at >= ? AND status != 'deleted' AND quality_score IS NOT NULL
                GROUP BY quality_range
            """, (cutoff_date,))
            
            quality_dist = dict(cursor.fetchall())
            
            # Calculate derived metrics
            total_scripts = gen_stats[0] or 0
            total_sessions = usage_stats[0] or 0
            reuse_rate = (total_sessions / max(total_scripts, 1)) if total_scripts > 0 else 0.0
            
            return ScriptAnalytics(
                total_scripts=total_scripts,
                scripts_by_method={GenerationMethod(k): v for k, v in method_stats.items()},
                average_generation_time_ms=gen_stats[1] or 0.0,
                generation_success_rate=1.0,  # Calculated separately if needed
                
                total_sessions=total_sessions,
                total_usage_minutes=int((usage_stats[1] or 0) * total_sessions),
                average_session_duration_minutes=usage_stats[1] or 0.0,
                scripts_reuse_rate=reuse_rate,
                
                average_quality_score=gen_stats[2] or 0.0,
                quality_distribution=quality_dist,
                top_performing_scripts=top_scripts,
                
                average_engagement_score=usage_stats[2] or 0.0,
                average_interaction_rate=usage_stats[3] or 0.0,
                average_retention_rate=usage_stats[4] or 0.0,
                
                generation_trend_7d=daily_generation[-7:],
                usage_trend_7d=daily_usage[-7:],
                quality_trend_7d=daily_quality[-7:],
                
                generation_efficiency_score=self._calculate_efficiency_score(gen_stats),
                content_utilization_rate=min(1.0, reuse_rate),
                user_satisfaction_score=usage_stats[2] or 0.0
            )
    
    # === Full-text Search ===
    
    async def full_text_search(self, query: str, limit: int = 20) -> List[PersistedScript]:
        """Perform full-text search on script content"""
        try:
            return await self._run_in_executor(self._full_text_search_sync, query, limit)
        except Exception as e:
            logger.error(f"Full-text search failed: {e}")
            return []
    
    def _full_text_search_sync(self, query: str, limit: int) -> List[PersistedScript]:
        """Synchronous full-text search"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Search using FTS5
            cursor.execute("""
                SELECT s.* FROM persisted_scripts s
                JOIN script_search_index idx ON s.script_id = idx.script_id
                WHERE script_search_index MATCH ?
                AND s.status != 'deleted'
                ORDER BY rank
                LIMIT ?
            """, (query, limit))
            
            script_rows = cursor.fetchall()
            scripts = []
            
            for script_row in script_rows:
                script_dict = dict(script_row)
                
                cursor.execute("""
                    SELECT * FROM script_segments 
                    WHERE script_id = ? 
                    ORDER BY segment_order
                """, (script_dict['script_id'],))
                
                segment_rows = cursor.fetchall()
                script = self._build_script_from_rows(script_dict, segment_rows)
                if script:
                    scripts.append(script)
            
            return scripts
    
    # === Batch Operations ===
    
    async def batch_save_scripts(self, scripts: List[PersistedScript]) -> int:
        """Save multiple scripts in a single transaction"""
        try:
            return await self._run_in_executor(self._batch_save_scripts_sync, scripts)
        except Exception as e:
            logger.error(f"Batch save failed: {e}")
            return 0
    
    def _batch_save_scripts_sync(self, scripts: List[PersistedScript]) -> int:
        """Synchronous batch save"""
        saved_count = 0
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            for script in scripts:
                try:
                    # Use the same logic as single save but in one transaction
                    self._save_single_script_in_transaction(cursor, script)
                    saved_count += 1
                except Exception as e:
                    logger.warning(f"Failed to save script {script.script_id} in batch: {e}")
                    continue
            
            conn.commit()
            logger.info(f"Batch saved {saved_count}/{len(scripts)} scripts")
            return saved_count
    
    # === Helper Methods ===
    
    def _build_script_from_rows(self, script_row: Dict, segment_rows: List) -> Optional[PersistedScript]:
        """Build PersistedScript from database rows"""
        try:
            # Parse JSON fields
            interaction_points = json.loads(script_row.get('interaction_points', '[]'))
            break_points = json.loads(script_row.get('break_points', '[]'))
            adaptation_rules = json.loads(script_row.get('adaptation_rules', '{}'))
            estimated_metrics = json.loads(script_row.get('estimated_metrics', '{}'))
            generation_warnings = json.loads(script_row.get('generation_warnings', '[]'))
            persona_info = json.loads(script_row.get('persona_info', '{}'))
            form_snapshot = json.loads(script_row.get('form_snapshot', '{}'))
            tags = json.loads(script_row.get('tags', '[]'))
            performance_metrics = json.loads(script_row.get('performance_metrics', '{}'))
            user_feedback = json.loads(script_row.get('user_feedback', '{}'))
            
            # Build segments
            segments = []
            for seg_row in segment_rows:
                seg_dict = dict(seg_row)
                segment = PersistedScriptSegment(
                    segment_id=seg_dict['segment_id'],
                    script_id=seg_dict['script_id'],
                    segment_type=seg_dict['segment_type'],
                    title=seg_dict['title'],
                    content=seg_dict['content'],
                    duration_seconds=seg_dict['duration_seconds'],
                    priority=seg_dict['priority'],
                    segment_order=seg_dict['segment_order'],
                    triggers=json.loads(seg_dict.get('triggers', '[]')),
                    variables=json.loads(seg_dict.get('variables', '{}')),
                    generation_metadata=json.loads(seg_dict.get('generation_metadata', '{}')),
                    created_at=seg_dict.get('created_at', datetime.now()),
                    updated_at=seg_dict.get('updated_at'),
                    usage_count=seg_dict.get('usage_count', 0),
                    last_used_at=seg_dict.get('last_used_at')
                )
                segments.append(segment)
            
            # Build main script
            return PersistedScript(
                script_id=script_row['script_id'],
                form_id=script_row['form_id'],
                generated_at=script_row['generated_at'],
                generation_method=GenerationMethod(script_row['generation_method']),
                total_duration_seconds=script_row['total_duration_seconds'],
                segment_count=script_row['segment_count'],
                generation_time_ms=script_row['generation_time_ms'],
                html_preview=script_row.get('html_preview'),
                segments=segments,
                interaction_points=interaction_points,
                break_points=break_points,
                adaptation_rules=adaptation_rules,
                estimated_metrics=estimated_metrics,
                generation_warnings=generation_warnings,
                persona_info=persona_info,
                form_snapshot=form_snapshot,
                status=ScriptStatus(script_row.get('status', 'generated')),
                usage_count=script_row.get('usage_count', 0),
                last_used_at=script_row.get('last_used_at'),
                created_by=script_row.get('created_by'),
                tags=tags,
                quality_score=script_row.get('quality_score'),
                performance_metrics=performance_metrics,
                user_feedback=user_feedback
            )
            
        except Exception as e:
            logger.error(f"Failed to build script from database rows: {e}")
            return None
    
    def _update_search_index_sync(self, cursor: sqlite3.Cursor, script: PersistedScript) -> None:
        """Update full-text search index"""
        try:
            # Extract searchable content
            content_parts = []
            for segment in script.segments:
                content_parts.append(segment.title)
                content_parts.append(segment.content)
            
            searchable_content = " ".join(content_parts)
            
            # Extract form data for search
            form_data = script.form_snapshot
            product_name = form_data.get('product_information', {}).get('product_name', '')
            brand = form_data.get('product_information', {}).get('brand', '')
            category = form_data.get('product_information', {}).get('category', '')
            title = form_data.get('basic_information', {}).get('stream_title', '')
            
            # Update search index
            cursor.execute("""
                INSERT OR REPLACE INTO script_search_index (
                    script_id, form_id, title, content, tags,
                    product_name, brand, category
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                script.script_id,
                script.form_id,
                title,
                searchable_content,
                " ".join(script.tags),
                product_name,
                brand,
                category
            ))
            
        except Exception as e:
            logger.warning(f"Failed to update search index for {script.script_id}: {e}")
    
    def _get_daily_trend(self, cursor: sqlite3.Cursor, table: str, date_column: str, days: int) -> List[int]:
        """Get daily count trend for specified period"""
        cursor.execute(f"""
            SELECT DATE({date_column}) as day, COUNT(*) as count
            FROM {table}
            WHERE {date_column} >= date('now', '-{days} days')
            GROUP BY DATE({date_column})
            ORDER BY day
        """)
        
        trend_data = dict(cursor.fetchall())
        
        # Fill in missing days with 0
        trend = []
        for i in range(days):
            day = (datetime.now() - timedelta(days=days-i-1)).strftime('%Y-%m-%d')
            trend.append(trend_data.get(day, 0))
        
        return trend
    
    def _get_daily_quality_trend(self, cursor: sqlite3.Cursor, days: int) -> List[float]:
        """Get daily average quality score trend"""
        cursor.execute("""
            SELECT DATE(generated_at) as day, AVG(quality_score) as avg_quality
            FROM persisted_scripts
            WHERE generated_at >= date('now', ?||' days')
            AND quality_score IS NOT NULL
            AND status != 'deleted'
            GROUP BY DATE(generated_at)
            ORDER BY day
        """, (f'-{days}',))
        
        trend_data = dict(cursor.fetchall())
        
        # Fill in missing days with 0
        trend = []
        for i in range(days):
            day = (datetime.now() - timedelta(days=days-i-1)).strftime('%Y-%m-%d')
            trend.append(trend_data.get(day, 0.0))
        
        return trend
    
    def _calculate_efficiency_score(self, gen_stats: tuple) -> float:
        """Calculate generation efficiency score"""
        avg_time = gen_stats[1] or 0
        if avg_time == 0:
            return 1.0
        
        # Score based on generation time (lower is better)
        # Target: under 5 seconds = excellent, under 10 = good, etc.
        if avg_time < 5000:
            return 1.0
        elif avg_time < 10000:
            return 0.8
        elif avg_time < 20000:
            return 0.6
        else:
            return 0.4
    
    def _get_empty_analytics(self) -> ScriptAnalytics:
        """Return empty analytics object"""
        return ScriptAnalytics(
            total_scripts=0,
            scripts_by_method={},
            average_generation_time_ms=0.0,
            generation_success_rate=0.0,
            total_sessions=0,
            total_usage_minutes=0,
            average_session_duration_minutes=0.0,
            scripts_reuse_rate=0.0,
            average_quality_score=0.0,
            quality_distribution={},
            top_performing_scripts=[],
            average_engagement_score=0.0,
            average_interaction_rate=0.0,
            average_retention_rate=0.0,
            generation_trend_7d=[0] * 7,
            usage_trend_7d=[0] * 7,
            quality_trend_7d=[0.0] * 7,
            generation_efficiency_score=0.0,
            content_utilization_rate=0.0,
            user_satisfaction_score=0.0
        )
    
    async def _run_in_executor(self, func, *args):
        """Run blocking database operations in thread pool"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self._executor, func, *args)
    
    def _save_single_script_in_transaction(self, cursor: sqlite3.Cursor, script: PersistedScript) -> None:
        """Save single script within existing transaction"""
        # Same logic as _save_script_sync but without connection management
        cursor.execute("""
            INSERT OR REPLACE INTO persisted_scripts (
                script_id, form_id, generated_at, generation_method,
                total_duration_seconds, segment_count, generation_time_ms,
                html_preview, interaction_points, break_points,
                adaptation_rules, estimated_metrics, generation_warnings,
                persona_info, form_snapshot, status, usage_count,
                last_used_at, created_by, tags, quality_score,
                performance_metrics, user_feedback
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            script.script_id, script.form_id, script.generated_at,
            script.generation_method.value, script.total_duration_seconds,
            script.segment_count, script.generation_time_ms, script.html_preview,
            json.dumps(script.interaction_points), json.dumps(script.break_points),
            json.dumps(script.adaptation_rules), json.dumps(script.estimated_metrics),
            json.dumps(script.generation_warnings), json.dumps(script.persona_info),
            json.dumps(script.form_snapshot), script.status.value, script.usage_count,
            script.last_used_at, script.created_by, json.dumps(script.tags),
            script.quality_score, json.dumps(script.performance_metrics),
            json.dumps(script.user_feedback)
        ))
        
        # Delete and re-insert segments
        cursor.execute("DELETE FROM script_segments WHERE script_id = ?", (script.script_id,))
        
        segment_data = []
        for segment in script.segments:
            segment_data.append((
                segment.segment_id, segment.script_id, segment.segment_type,
                segment.title, segment.content, segment.duration_seconds,
                segment.priority, segment.segment_order, json.dumps(segment.triggers),
                json.dumps(segment.variables), json.dumps(segment.generation_metadata),
                segment.created_at, segment.updated_at, segment.usage_count,
                segment.last_used_at
            ))
        
        cursor.executemany("""
            INSERT INTO script_segments (
                segment_id, script_id, segment_type, title, content,
                duration_seconds, priority, segment_order, triggers,
                variables, generation_metadata, created_at, updated_at,
                usage_count, last_used_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, segment_data)
        
        # Update search index
        self._update_search_index_sync(cursor, script)
    
    # === Cleanup and Maintenance ===
    
    async def cleanup_old_data(self, retention_days: int = 365) -> int:
        """Clean up old data based on retention policy"""
        return await self._run_in_executor(self.migrator.cleanup_old_data, retention_days)
    
    async def optimize_database(self) -> bool:
        """Optimize database performance"""
        return await self._run_in_executor(self.migrator.optimize_database)
    
    async def get_repository_stats(self) -> Dict[str, Any]:
        """Get repository statistics"""
        return await self._run_in_executor(self.migrator.get_database_stats)