"""产品服务层

提供产品的CRUD操作、统计信息和标签管理功能。
"""

import sqlite3
import json
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from loguru import logger

from ..models.product import (
    Product, ProductCreate, ProductUpdate, ProductResponse, 
    ProductDetailResponse, ProductStats, ProductFilters,
    Tag, ProductTag, Pagination, Page
)
from ..services.persistence import DatabaseManager
from ..core.exceptions import ServiceError, ValidationError


class ProductService:
    """产品服务类"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self._cache = {}  # 简单的内存缓存
        self._cache_ttl = 300  # 缓存5分钟
        
    async def create_product(self, product_data: ProductCreate) -> Product:
        """创建新产品"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 检查SKU是否已存在
            cursor.execute("SELECT id FROM products WHERE sku = ?", (product_data.sku,))
            if cursor.fetchone():
                raise ValidationError(f"SKU {product_data.sku} 已存在")
            
            # 插入产品
            cursor.execute("""
            INSERT INTO products (sku, name, category, description, price, stock)
            VALUES (?, ?, ?, ?, ?, ?)
            """, (
                product_data.sku.upper(),
                product_data.name,
                product_data.category,
                product_data.description,
                product_data.price,
                product_data.stock
            ))
            
            product_id = cursor.lastrowid
            
            # 处理标签
            if product_data.tags:
                await self._add_tags_to_product(conn, product_id, product_data.tags)
            
            conn.commit()
            
            # 获取创建的产品
            return await self.get_product(product_id)
            
        except sqlite3.Error as e:
            logger.error(f"创建产品失败: {e}")
            raise ServiceError(f"创建产品失败: {e}")
        finally:
            conn.close()
    
    async def get_product(self, product_id: int) -> Product:
        """获取产品详情"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
            SELECT id, sku, name, category, description, price, stock, 
                   created_at, updated_at
            FROM products WHERE id = ?
            """, (product_id,))
            
            row = cursor.fetchone()
            if not row:
                raise ValidationError(f"产品ID {product_id} 不存在")
            
            product = Product(
                id=row[0],
                sku=row[1],
                name=row[2],
                category=row[3],
                description=row[4],
                price=row[5],
                stock=row[6],
                created_at=datetime.fromisoformat(row[7]) if row[7] else None,
                updated_at=datetime.fromisoformat(row[8]) if row[8] else None
            )
            
            return product
            
        except sqlite3.Error as e:
            logger.error(f"获取产品失败: {e}")
            raise ServiceError(f"获取产品失败: {e}")
        finally:
            conn.close()
    
    async def update_product(self, product_id: int, updates: ProductUpdate) -> Product:
        """更新产品信息"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 检查产品是否存在
            cursor.execute("SELECT id FROM products WHERE id = ?", (product_id,))
            if not cursor.fetchone():
                raise ValidationError(f"产品ID {product_id} 不存在")
            
            # 构建更新语句
            update_fields = []
            params = []
            
            if updates.name is not None:
                update_fields.append("name = ?")
                params.append(updates.name)
            
            if updates.category is not None:
                update_fields.append("category = ?")
                params.append(updates.category)
            
            if updates.description is not None:
                update_fields.append("description = ?")
                params.append(updates.description)
            
            if updates.price is not None:
                update_fields.append("price = ?")
                params.append(updates.price)
            
            if updates.stock is not None:
                update_fields.append("stock = ?")
                params.append(updates.stock)
            
            if update_fields:
                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                query = f"UPDATE products SET {', '.join(update_fields)} WHERE id = ?"
                params.append(product_id)
                cursor.execute(query, params)
            
            # 更新标签
            if updates.tags is not None:
                # 删除旧标签关联
                cursor.execute("DELETE FROM product_tags WHERE product_id = ?", (product_id,))
                # 添加新标签
                if updates.tags:
                    await self._add_tags_to_product(conn, product_id, updates.tags)
            
            conn.commit()
            
            # 清除缓存
            self._invalidate_cache(product_id)
            
            return await self.get_product(product_id)
            
        except sqlite3.Error as e:
            logger.error(f"更新产品失败: {e}")
            raise ServiceError(f"更新产品失败: {e}")
        finally:
            conn.close()
    
    async def delete_product(self, product_id: int) -> bool:
        """删除产品"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 检查是否有关联的配置
            cursor.execute("""
            SELECT COUNT(*) FROM streaming_configs WHERE product_id = ?
            """, (product_id,))
            config_count = cursor.fetchone()[0]
            
            if config_count > 0:
                raise ValidationError(f"产品有{config_count}个关联配置，无法删除")
            
            # 删除产品（级联删除标签关联和QA）
            cursor.execute("DELETE FROM products WHERE id = ?", (product_id,))
            
            if cursor.rowcount == 0:
                raise ValidationError(f"产品ID {product_id} 不存在")
            
            conn.commit()
            
            # 清除缓存
            self._invalidate_cache(product_id)
            
            return True
            
        except sqlite3.Error as e:
            logger.error(f"删除产品失败: {e}")
            raise ServiceError(f"删除产品失败: {e}")
        finally:
            conn.close()
    
    async def list_products(self, filters: ProductFilters, pagination: Pagination) -> Page:
        """获取产品列表"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_clauses = []
            params = []
            
            if filters.category:
                where_clauses.append("p.category = ?")
                params.append(filters.category)
            
            if filters.search:
                where_clauses.append("(p.name LIKE ? OR p.description LIKE ? OR p.sku LIKE ?)")
                search_pattern = f"%{filters.search}%"
                params.extend([search_pattern, search_pattern, search_pattern])
            
            if filters.min_price is not None:
                where_clauses.append("p.price >= ?")
                params.append(filters.min_price)
            
            if filters.max_price is not None:
                where_clauses.append("p.price <= ?")
                params.append(filters.max_price)
            
            if filters.has_stock is not None:
                if filters.has_stock:
                    where_clauses.append("p.stock > 0")
                else:
                    where_clauses.append("(p.stock = 0 OR p.stock IS NULL)")
            
            where_clause = " AND ".join(where_clauses) if where_clauses else "1=1"
            
            # 获取总数
            count_query = f"SELECT COUNT(*) FROM products p WHERE {where_clause}"
            cursor.execute(count_query, params)
            total = cursor.fetchone()[0]
            
            # 获取分页数据
            order_by = f"p.{filters.sort_by} {filters.sort_order.upper()}"
            query = f"""
            SELECT p.id, p.sku, p.name, p.category, p.description, 
                   p.price, p.stock, p.created_at, p.updated_at,
                   GROUP_CONCAT(t.name) as tags
            FROM products p
            LEFT JOIN product_tags pt ON p.id = pt.product_id
            LEFT JOIN tags t ON pt.tag_id = t.id
            WHERE {where_clause}
            GROUP BY p.id
            ORDER BY {order_by}
            LIMIT ? OFFSET ?
            """
            
            offset = (pagination.page - 1) * pagination.size
            params.extend([pagination.size, offset])
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 构建响应
            items = []
            for row in rows:
                items.append(ProductResponse(
                    id=row[0],
                    sku=row[1],
                    name=row[2],
                    category=row[3],
                    description=row[4],
                    price=row[5],
                    stock=row[6],
                    tags=row[9].split(',') if row[9] else [],
                    created_at=datetime.fromisoformat(row[7]) if row[7] else datetime.now(),
                    updated_at=datetime.fromisoformat(row[8]) if row[8] else datetime.now()
                ))
            
            pages = (total + pagination.size - 1) // pagination.size
            
            return Page(
                items=items,
                total=total,
                page=pagination.page,
                size=pagination.size,
                pages=pages,
                has_next=pagination.page < pages,
                has_prev=pagination.page > 1
            )
            
        except sqlite3.Error as e:
            logger.error(f"获取产品列表失败: {e}")
            raise ServiceError(f"获取产品列表失败: {e}")
        finally:
            conn.close()
    
    async def get_product_stats(self, product_id: int) -> ProductStats:
        """获取产品统计信息"""
        # 检查缓存
        cache_key = f"product_stats_{product_id}"
        if cache_key in self._cache:
            cached_data, cached_time = self._cache[cache_key]
            if (datetime.now() - cached_time).seconds < self._cache_ttl:
                return cached_data
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # 获取产品基本信息
            product = await self.get_product(product_id)
            
            # 获取QA数量
            cursor.execute("""
            SELECT COUNT(*) FROM qa_entries WHERE product_id = ?
            """, (product_id,))
            qa_count = cursor.fetchone()[0]
            
            # 获取配置数量
            cursor.execute("""
            SELECT COUNT(*) FROM streaming_configs WHERE product_id = ?
            """, (product_id,))
            config_count = cursor.fetchone()[0]
            
            # 获取QA总命中次数
            cursor.execute("""
            SELECT SUM(hit_count) FROM qa_entries WHERE product_id = ?
            """, (product_id,))
            total_hits = cursor.fetchone()[0] or 0
            
            # 获取热门问题
            cursor.execute("""
            SELECT question, answer, hit_count 
            FROM qa_entries 
            WHERE product_id = ? AND hit_count > 0
            ORDER BY hit_count DESC
            LIMIT 5
            """, (product_id,))
            
            popular_questions = []
            for row in cursor.fetchall():
                popular_questions.append({
                    'question': row[0],
                    'answer': row[1],
                    'hit_count': row[2]
                })
            
            # 获取最后使用时间（最新配置的创建时间）
            cursor.execute("""
            SELECT MAX(created_at) FROM streaming_configs WHERE product_id = ?
            """, (product_id,))
            last_used_str = cursor.fetchone()[0]
            last_used = datetime.fromisoformat(last_used_str) if last_used_str else None
            
            stats = ProductStats(
                product_id=product_id,
                qa_count=qa_count,
                config_count=config_count,
                last_used=last_used,
                total_qa_hits=total_hits,
                popular_questions=popular_questions,
                created_at=product.created_at,
                updated_at=product.updated_at
            )
            
            # 更新缓存
            self._cache[cache_key] = (stats, datetime.now())
            
            return stats
            
        except sqlite3.Error as e:
            logger.error(f"获取产品统计失败: {e}")
            raise ServiceError(f"获取产品统计失败: {e}")
        finally:
            conn.close()
    
    async def get_product_by_sku(self, sku: str) -> Optional[Product]:
        """根据SKU获取产品"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
            SELECT id FROM products WHERE sku = ?
            """, (sku.upper(),))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            return await self.get_product(row[0])
            
        except sqlite3.Error as e:
            logger.error(f"根据SKU获取产品失败: {e}")
            raise ServiceError(f"根据SKU获取产品失败: {e}")
        finally:
            conn.close()
    
    async def _add_tags_to_product(self, conn: sqlite3.Connection, 
                                  product_id: int, tags: List[str]):
        """为产品添加标签"""
        cursor = conn.cursor()
        
        for tag_name in tags:
            tag_name = tag_name.strip().lower()
            if not tag_name:
                continue
            
            # 插入或获取标签ID
            cursor.execute("""
            INSERT OR IGNORE INTO tags (name, category) VALUES (?, 'other')
            """, (tag_name,))
            
            cursor.execute("SELECT id FROM tags WHERE name = ?", (tag_name,))
            tag_id = cursor.fetchone()[0]
            
            # 创建产品-标签关联
            cursor.execute("""
            INSERT OR IGNORE INTO product_tags (product_id, tag_id)
            VALUES (?, ?)
            """, (product_id, tag_id))
            
            # 更新标签使用次数
            cursor.execute("""
            UPDATE tags SET usage_count = usage_count + 1 WHERE id = ?
            """, (tag_id,))
    
    def _invalidate_cache(self, product_id: int):
        """清除产品相关缓存"""
        cache_key = f"product_stats_{product_id}"
        if cache_key in self._cache:
            del self._cache[cache_key]