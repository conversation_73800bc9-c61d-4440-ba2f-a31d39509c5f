"""Script to TTS Converter

Converts script segments to TTS-compatible audio chunks for streaming playback.
Provides the bridge between script preview system and audio playback system.
"""

import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator
from pathlib import Path
from datetime import datetime
from loguru import logger

from ..services.script_previewer import ScriptSegment, ScriptSegmentType, ScriptTimeline
from ..services.tts_engines.cozyvoice_cloud_engine import CozyVoiceCloudEngine
from ..services.audio_streaming_proxy import get_audio_proxy
from ..models.audio import AudioChunk, AudioFormat, AudioPriority
from ..core.exceptions import ServiceError
from ..core.config import cfg


class ScriptToTTSConverter:
    """Converts script segments to audio chunks using TTS"""
    
    def __init__(self):
        self.tts_engine: Optional[CozyVoiceCloudEngine] = None
        self.logger = logger.bind(component="script_to_tts_converter")
        
        # TTS configuration
        self.tts_config = {
            'voice_id': cfg.get_yaml_config('tts.engines.cozyvoice_cloud.voice_id', 'cosyvoice-v2-zh-CN-female'),
            'model': 'cosyvoice-v2',
            'sample_rate': 24000,
            'speech_rate': 1.15,
            'ssml_config': {
                'sentence_break_ms': 500,
                'clause_break_ms': 300,
                'paragraph_break_ms': 800,
                'safe_char_limit': 4800
            }
        }
    
    async def initialize(self):
        """Initialize TTS engine"""
        try:
            self.tts_engine = CozyVoiceCloudEngine(self.tts_config)
            await self.tts_engine.initialize()
            self.logger.info("Script to TTS converter initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize TTS engine: {e}")
            raise ServiceError(f"TTS initialization failed: {e}", "script_to_tts_converter")
    
    def prepare_segment_text(self, segment: ScriptSegment) -> str:
        """Prepare script segment text for TTS conversion
        
        Args:
            segment: Script segment to process
            
        Returns:
            Clean text ready for TTS synthesis
        """
        text = segment.content.strip()
        
        # Basic text cleaning
        text = text.replace('\n', ' ').replace('\r', ' ')
        text = ' '.join(text.split())  # Normalize whitespace
        
        # Add segment-specific optimizations
        if segment.segment_type == ScriptSegmentType.OPENING:
            # Add slight pause after opening
            text = text + "。"
        elif segment.segment_type == ScriptSegmentType.PRICE_ANNOUNCEMENT:
            # Emphasize price information
            text = text.replace('元', '元！')
        elif segment.segment_type == ScriptSegmentType.CALL_TO_ACTION:
            # Add urgency to call-to-action
            if not text.endswith(('！', '!', '。', '?', '？')):
                text = text + "！"
        
        # Ensure text length is within limits
        if len(text) > 1000:  # Conservative limit for single segment
            self.logger.warning(f"Segment text too long ({len(text)} chars), truncating")
            text = text[:950] + "..."
        
        return text
    
    async def convert_segment_to_audio(self, segment: ScriptSegment) -> Optional[AudioChunk]:
        """Convert a single script segment to audio chunk
        
        Args:
            segment: Script segment to convert
            
        Returns:
            AudioChunk with synthesized audio, or None if failed
        """
        if not self.tts_engine:
            await self.initialize()
        
        try:
            # Prepare text for TTS
            text = self.prepare_segment_text(segment)
            
            if not text.strip():
                self.logger.warning(f"Empty text for segment {segment.segment_id}")
                return None
            
            self.logger.info(f"Converting segment '{segment.title}' to audio: {text[:50]}...")
            
            # Synthesize audio
            audio_chunk = await self.tts_engine.synthesize(text)
            
            # Update chunk metadata with segment information
            audio_chunk.text = f"[{segment.segment_type.value}] {segment.title}"
            audio_chunk.priority = self._get_audio_priority(segment.priority)
            
            self.logger.info(f"Successfully converted segment to audio: {Path(audio_chunk.file_path).name}")
            return audio_chunk
            
        except Exception as e:
            self.logger.error(f"Failed to convert segment {segment.segment_id} to audio: {e}")
            return None
    
    async def convert_segments_to_playlist(self, segments: List[ScriptSegment]) -> List[AudioChunk]:
        """Convert multiple script segments to audio playlist
        
        Args:
            segments: List of script segments to convert
            
        Returns:
            List of audio chunks ready for playback
        """
        if not segments:
            self.logger.warning("No segments provided for conversion")
            return []
        
        self.logger.info(f"Converting {len(segments)} segments to audio playlist")
        
        audio_chunks = []
        
        for i, segment in enumerate(segments):
            try:
                self.logger.info(f"Processing segment {i+1}/{len(segments)}: {segment.title}")
                
                audio_chunk = await self.convert_segment_to_audio(segment)
                
                if audio_chunk:
                    audio_chunks.append(audio_chunk)
                    self.logger.info(f"✅ Segment {i+1} converted successfully")
                else:
                    self.logger.warning(f"⚠️ Segment {i+1} conversion failed, skipping")
                    
            except Exception as e:
                self.logger.error(f"❌ Error processing segment {i+1}: {e}")
                continue
        
        self.logger.info(f"Conversion complete: {len(audio_chunks)}/{len(segments)} segments successful")
        return audio_chunks
    
    async def convert_timeline_to_playlist(self, timeline: ScriptTimeline) -> List[AudioChunk]:
        """Convert entire script timeline to audio playlist
        
        Args:
            timeline: Script timeline with segments
            
        Returns:
            List of audio chunks ready for playback
        """
        if not timeline or not timeline.segments:
            self.logger.warning("Empty timeline provided")
            return []
        
        self.logger.info(f"Converting timeline with {len(timeline.segments)} segments")
        self.logger.info(f"Target duration: {timeline.total_duration_minutes} minutes")
        
        return await self.convert_segments_to_playlist(timeline.segments)
    
    def _get_audio_priority(self, script_priority) -> AudioPriority:
        """Convert script priority to audio priority
        
        Args:
            script_priority: Script segment priority
            
        Returns:
            Corresponding audio priority
        """
        # Map script priorities to audio priorities
        priority_mapping = {
            'HIGH': AudioPriority.HIGH,
            'MEDIUM': AudioPriority.NORMAL,
            'LOW': AudioPriority.LOW
        }
        
        if hasattr(script_priority, 'value'):
            priority_str = script_priority.value.upper()
        else:
            priority_str = str(script_priority).upper()
        
        return priority_mapping.get(priority_str, AudioPriority.NORMAL)
    
    async def cleanup(self):
        """Clean up resources"""
        if self.tts_engine:
            await self.tts_engine.cleanup()
        self.logger.info("Script to TTS converter cleaned up")


class StreamingScriptPlayer:
    """Manages streaming transmission of converted script segments to AudioStreamingProxy"""
    
    def __init__(self):
        self.converter = ScriptToTTSConverter()
        self.audio_proxy = get_audio_proxy()
        self.logger = logger.bind(component="streaming_script_player")
        
        # Streaming state
        self.current_segments: List[ScriptSegment] = []
        self.is_streaming = False
        self.current_session_id: Optional[str] = None
    
    async def initialize(self):
        """Initialize the streaming player"""
        await self.converter.initialize()
        self.logger.info("Streaming script player initialized")
    
    async def stream_timeline_to_proxy(self, timeline: ScriptTimeline) -> bool:
        """Stream script timeline to AudioStreamingProxy for WebSocket broadcast
        
        Args:
            timeline: Script timeline to stream
            
        Returns:
            True if streaming started successfully
        """
        try:
            self.logger.info(f"Streaming timeline to audio proxy: {len(timeline.segments)} segments")
            
            # Prepare segments as text list for streaming
            segment_texts = []
            for segment in timeline.segments:
                text = self.converter.prepare_segment_text(segment)
                if text.strip():
                    segment_texts.append(text)
            
            if not segment_texts:
                self.logger.error("No valid text segments for streaming")
                return False
            
            # Configure voice for streaming
            voice_config = {
                "voice": self.converter.tts_config.get('voice_id', 'longanran'),
                "format": "mp3",  # Browser-friendly format
                "sample_rate": self.converter.tts_config.get('sample_rate', 24000),
                "model": self.converter.tts_config.get('model', 'cosyvoice-v2')
            }
            
            # Start streaming via AudioStreamingProxy
            session_id = f"script_timeline_{int(datetime.now().timestamp())}"
            self.current_session_id = session_id
            self.is_streaming = True
            
            # Stream to connected WebSocket clients
            await self.audio_proxy.start_streaming(
                texts=segment_texts,
                voice_config=voice_config,
                session_id=session_id
            )
            
            self.logger.info(f"Successfully started streaming timeline with {len(segment_texts)} segments")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stream timeline to proxy: {e}")
            self.is_streaming = False
            return False
    
    async def stream_segments_to_proxy(self, segments: List[ScriptSegment]) -> bool:
        """Stream script segments to AudioStreamingProxy for WebSocket broadcast
        
        Args:
            segments: List of script segments to stream
            
        Returns:
            True if streaming started successfully
        """
        try:
            self.logger.info(f"Streaming segments to audio proxy: {len(segments)} segments")
            
            # Prepare segments as text list for streaming
            segment_texts = []
            for segment in segments:
                text = self.converter.prepare_segment_text(segment)
                if text.strip():
                    segment_texts.append(text)
            
            if not segment_texts:
                self.logger.error("No valid text segments for streaming")
                return False
            
            # Configure voice for streaming
            voice_config = {
                "voice": self.converter.tts_config.get('voice_id', 'longanran'),
                "format": "mp3",  # Browser-friendly format
                "sample_rate": self.converter.tts_config.get('sample_rate', 24000),
                "model": self.converter.tts_config.get('model', 'cosyvoice-v2')
            }
            
            # Start streaming via AudioStreamingProxy
            session_id = f"script_segments_{int(datetime.now().timestamp())}"
            self.current_session_id = session_id
            self.is_streaming = True
            
            # Stream to connected WebSocket clients
            await self.audio_proxy.start_streaming(
                texts=segment_texts,
                voice_config=voice_config,
                session_id=session_id
            )
            
            self.logger.info(f"Successfully started streaming segments with {len(segment_texts)} segments")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stream segments to proxy: {e}")
            self.is_streaming = False
            return False
    
    def get_streaming_status(self) -> Dict[str, Any]:
        """Get current streaming status
        
        Returns:
            Dictionary with current streaming status
        """
        proxy_status = self.audio_proxy.get_status() if self.audio_proxy else {}
        
        return {
            'is_streaming': self.is_streaming,
            'current_session_id': self.current_session_id,
            'connected_clients': proxy_status.get('active_connections', 0),
            'proxy_streaming': proxy_status.get('is_streaming', False),
            'total_chunks_sent': proxy_status.get('stats', {}).get('total_audio_chunks', 0),
            'total_bytes_sent': proxy_status.get('stats', {}).get('total_bytes_sent', 0)
        }
    
    async def stop_streaming(self):
        """Stop current streaming session"""
        try:
            if self.current_session_id and self.audio_proxy:
                await self.audio_proxy.stop_streaming(self.current_session_id)
            
            self.is_streaming = False
            self.current_session_id = None
            self.logger.info("Streaming session stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping streaming session: {e}")
    
    async def cleanup(self):
        """Clean up resources"""
        await self.stop_streaming()
        await self.converter.cleanup()
        self.logger.info("Streaming script player cleaned up")


# Global instance for easy access
_streaming_player: Optional[StreamingScriptPlayer] = None


def get_streaming_script_player() -> StreamingScriptPlayer:
    """Get or create global streaming script player instance"""
    global _streaming_player
    if _streaming_player is None:
        _streaming_player = StreamingScriptPlayer()
    return _streaming_player


async def initialize_streaming_script_player() -> StreamingScriptPlayer:
    """Initialize and return streaming script player"""
    global _streaming_player
    _streaming_player = StreamingScriptPlayer()
    await _streaming_player.initialize()
    return _streaming_player