"""
LLM-based Script Generator Service

Generates streaming scripts using LLM with PersonaConfig integration,
replacing template-based generation with context-aware AI content creation.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import asyncio
from loguru import logger

from ..models.forms import (
    OperationalForm, BasicInformation, ProductInformation,
    SellingPointsStructure, SellingPoint, PersonaConfiguration,
    PriorityLevel
)
from ..models.persona import PersonaConfig, PersonaType
from ..services.factories import ServiceFactory
from ..services.llm_adapters.base import LLMMessage
from ..core.exceptions import ServiceError, ValidationError
from ..core.config import cfg


class ScriptSegmentType(Enum):
    """Types of script segments"""
    OPENING = "opening"
    PRODUCT_INTRO = "product_intro"
    SELLING_POINT = "selling_point"
    INTERACTION = "interaction"
    PRICE_ANNOUNCEMENT = "price_announcement"
    CALL_TO_ACTION = "call_to_action"
    TRANSITION = "transition"
    CLOSING = "closing"
    EMERGENCY_FILLER = "emergency_filler"


@dataclass
class LLMScriptSegment:
    """Script segment generated by LLM"""
    segment_id: str
    segment_type: ScriptSegmentType
    title: str
    content: str
    estimated_duration_seconds: int
    priority: PriorityLevel
    triggers: List[str]
    variables: Dict[str, Any]
    generation_metadata: Dict[str, Any]  # LLM generation stats


@dataclass
class LLMScriptResult:
    """Complete LLM-generated script result"""
    success: bool
    form_id: str
    segments: List[LLMScriptSegment]
    total_duration_seconds: int
    persona_info: Dict[str, Any]
    generation_stats: Dict[str, Any]
    error_messages: List[str]
    warnings: List[str]


class LLMScriptGenerator:
    """
    LLM-powered script generator with PersonaConfig integration
    
    Replaces template-based generation with AI-driven content creation
    that adapts to persona, time context, and product information.
    """
    
    def __init__(self):
        self._llm_adapter = None
        self.generation_stats = {
            "scripts_generated": 0,
            "total_tokens_used": 0,
            "average_generation_time": 0.0,
            "persona_adaptations": 0,
            "regeneration_requests": 0,
            "quality_adjustments": 0
        }
        
        # Advanced generation control parameters
        self.generation_config = {
            "enable_quality_check": True,
            "enable_multi_version": False,
            "max_retries": 2,
            "quality_threshold": 0.7,
            "temperature_adjustment": 0.1,
            "content_diversity": False
        }
    
    async def _initialize_llm(self):
        """Initialize LLM adapter"""
        if self._llm_adapter is None:
            self._llm_adapter = await ServiceFactory.create_llm_adapter()
            logger.info(f"LLM Script Generator initialized with: {self._llm_adapter.provider_name}")
    
    def _prepare_generation_params(self, base_params: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare generation parameters with caching support if available
        
        Args:
            base_params: Base parameters like max_tokens, temperature
            
        Returns:
            Parameters with caching added if supported
        """
        params = base_params.copy()
        
        # Add caching parameter if adapter supports it
        if (hasattr(self._llm_adapter, 'supports_caching') and 
            self._llm_adapter.supports_caching):
            params['caching'] = True
            
        return params
    
    async def generate_script(self, form: OperationalForm, persona_config: Optional[PersonaConfig] = None, retry_on_failure: bool = True) -> LLMScriptResult:
        """
        Generate complete script using LLM
        
        Args:
            form: Operational form with configuration data
            persona_config: Optional persona configuration for style adaptation
            
        Returns:
            LLMScriptResult with generated segments
        """
        start_time = datetime.utcnow()
        result = LLMScriptResult(
            success=False,
            form_id=form.form_id,
            segments=[],
            total_duration_seconds=0,
            persona_info={},
            generation_stats={},
            error_messages=[],
            warnings=[]
        )
        
        try:
            # Initialize LLM with error handling
            await self._initialize_llm_with_fallback()
            
            # Load or create persona configuration
            if persona_config is None:
                persona_config = await self._get_default_persona_config(form)
            
            # Validate form data
            validation_errors = self._validate_form_for_generation(form)
            if validation_errors:
                result.error_messages.extend(validation_errors)
                if len(validation_errors) > 2:  # Too many critical errors
                    return result
            
            # Generate script segments with error recovery  
            segments = await self._generate_all_segments_with_recovery(form, persona_config, retry_on_failure)
            
            if not segments:
                result.error_messages.append("Failed to generate any script segments")
                return result
            
            # Check if we have at least minimum required segments
            required_types = {ScriptSegmentType.OPENING, ScriptSegmentType.PRODUCT_INTRO}
            available_types = {seg.segment_type for seg in segments}
            missing_critical = required_types - available_types
            
            if missing_critical:
                result.warnings.append(f"Missing critical segments: {[t.value for t in missing_critical]}")
                if len(missing_critical) == len(required_types):
                    result.error_messages.append("Failed to generate critical segments")
                    return result
            
            # Calculate total duration
            total_duration = sum(seg.estimated_duration_seconds for seg in segments)
            
            # Build successful result
            result.success = True
            result.segments = segments
            result.total_duration_seconds = total_duration
            result.persona_info = self._extract_persona_info(persona_config)
            result.generation_stats = {
                "generation_time_seconds": (datetime.utcnow() - start_time).total_seconds(),
                "segments_count": len(segments),
                "total_duration_seconds": total_duration,
                "persona_type": persona_config.persona_type.value if persona_config else "default"
            }
            
            # Update stats
            self.generation_stats["scripts_generated"] += 1
            self.generation_stats["persona_adaptations"] += 1 if persona_config else 0
            self.generation_stats["total_tokens_used"] += sum(seg.generation_metadata.get("tokens_used", 0) for seg in segments)
            
            logger.info(f"Generated script with {len(segments)} segments for form {form.form_id}")
            return result
            
        except Exception as e:
            logger.error(f"Script generation failed for form {form.form_id}: {e}")
            result.error_messages.append(f"Generation failed: {str(e)}")
            return result
    
    
    async def _generate_opening_segment(self, form: OperationalForm, persona_config: PersonaConfig) -> Optional[LLMScriptSegment]:
        """Generate opening segment using LLM"""
        try:
            # Build context-rich prompt
            system_prompt = self._build_persona_system_prompt(persona_config, "opening")
            user_prompt = f"""
请为直播生成开场白内容。

基本信息：
- 直播标题：{form.basic_information.stream_title}
- 产品名称：{form.product_information.product_name}
- 品牌：{form.product_information.brand}
- 产品类别：{form.product_information.category}
- 计划时长：{form.basic_information.planned_duration_minutes}分钟
- 直播类型：{form.basic_information.stream_type}

时间背景：
- 当前时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M')}

要求：
1. 控制在60秒内的内容长度
2. 语调要{persona_config.voice_style.value}
3. 包含欢迎语、产品预告、互动引导
4. 符合{persona_config.name}的说话风格
5. 直接输出内容，不要包含格式标记

请生成开场白内容：
"""
            
            # Generate content
            messages = [
                LLMMessage(role="system", content=system_prompt),
                LLMMessage(role="user", content=user_prompt)
            ]
            
            # Prepare generation parameters
            base_params = {
                'max_tokens': 300,
                'temperature': persona_config.consistency_temperature
            }
            params = self._prepare_generation_params(base_params)
            
            response = await self._llm_adapter.generate(messages, **params)
            
            return LLMScriptSegment(
                segment_id="opening_001",
                segment_type=ScriptSegmentType.OPENING,
                title="开场白",
                content=response.content.strip(),
                estimated_duration_seconds=60,
                priority=PriorityLevel.HIGH,
                triggers=["stream_start"],
                variables={
                    "stream_title": form.basic_information.stream_title,
                    "product_name": form.product_information.product_name,
                    "brand": form.product_information.brand
                },
                generation_metadata={
                    "model": response.model,
                    "tokens_used": response.usage.get("total_tokens", 0),
                    "response_time_ms": response.response_time_ms,
                    "persona_id": persona_config.persona_id
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to generate opening segment: {e}")
            return None
    
    async def _generate_selling_point_segment(
        self, 
        form: OperationalForm, 
        selling_point: SellingPoint, 
        persona_config: PersonaConfig,
        index: int
    ) -> Optional[LLMScriptSegment]:
        """Generate selling point segment using LLM"""
        try:
            system_prompt = self._build_persona_system_prompt(persona_config, "selling_point")
            user_prompt = f"""
请为以下产品卖点生成直播讲解内容。

产品信息：
- 产品名称：{form.product_information.product_name}
- 品牌：{form.product_information.brand}

卖点信息：
- 卖点标题：{selling_point.title}
- 详细描述：{selling_point.description}
- 支撑事实：{', '.join(selling_point.supporting_facts) if selling_point.supporting_facts else '无'}
- 优先级：{selling_point.priority.value}

要求：
1. 这是第{index + 1}个卖点
2. 根据优先级调整内容详细程度（高优先级更详细）
3. 语调要{persona_config.voice_style.value}
4. 包含事实支撑和互动引导
5. 控制在{90 if selling_point.priority == PriorityLevel.HIGH else 60}秒内的内容长度
6. 符合{persona_config.name}的说话风格
7. 直接输出内容，不要包含格式标记

请生成卖点讲解内容：
"""
            
            messages = [
                LLMMessage(role="system", content=system_prompt),
                LLMMessage(role="user", content=user_prompt)
            ]
            
            base_params = {
                'max_tokens': 400,
                'temperature': persona_config.consistency_temperature
            }
            params = self._prepare_generation_params(base_params)
            
            response = await self._llm_adapter.generate(messages, **params)
            
            duration_map = {
                PriorityLevel.HIGH: 120,
                PriorityLevel.MEDIUM: 90,
                PriorityLevel.LOW: 60
            }
            
            return LLMScriptSegment(
                segment_id=f"selling_point_{selling_point.point_id}",
                segment_type=ScriptSegmentType.SELLING_POINT,
                title=f"卖点介绍: {selling_point.title}",
                content=response.content.strip(),
                estimated_duration_seconds=duration_map[selling_point.priority],
                priority=selling_point.priority,
                triggers=[f"after_selling_point_{index-1}" if index > 0 else "after_product_intro"],
                variables={
                    "selling_point_title": selling_point.title,
                    "selling_point_description": selling_point.description,
                    "supporting_facts": selling_point.supporting_facts,
                    "priority": selling_point.priority.value
                },
                generation_metadata={
                    "model": response.model,
                    "tokens_used": response.usage.get("total_tokens", 0),
                    "response_time_ms": response.response_time_ms,
                    "persona_id": persona_config.persona_id
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to generate selling point segment: {e}")
            return None
    
    def _build_persona_system_prompt(self, persona_config: PersonaConfig, segment_type: str) -> str:
        """Build system prompt incorporating persona configuration"""
        base_prompt = f"""
你是一个专业的直播主播，名叫{persona_config.name}。

人设特征：
- 人设描述：{persona_config.description}
- 语音风格：{persona_config.voice_style.value}
- 说话语速：{persona_config.speaking_rate}倍速
- 一致性要求：保持{persona_config.consistency_temperature}的风格一致性

"""
        
        # Add persona-specific templates if available
        if persona_config.persona_type == PersonaType.TEMPLATE_BASED and persona_config.template_config:
            template_config = persona_config.template_config
            segment_templates = {
                "opening": template_config.greeting_template,
                "selling_point": template_config.narration_template,
                "interaction": template_config.qa_template,
                "closing": template_config.closing_template
            }
            
            if segment_type in segment_templates:
                base_prompt += f"""
参考模板风格：
{segment_templates[segment_type]}

语调关键词：{', '.join(template_config.tone_keywords)}
常用表达：{', '.join(template_config.speaking_patterns)}
"""
        
        base_prompt += """
生成内容时请：
1. 严格按照人设风格说话
2. 保持语言自然流畅
3. 适当包含互动元素
4. 避免过于正式或机械化的表达
5. 确保内容准确、有吸引力
"""
        
        return base_prompt
    
    # === Advanced Generation Control Methods ===
    
    async def generate_multiple_versions(self, form: OperationalForm, persona_config: Optional[PersonaConfig] = None, versions: int = 3) -> List[LLMScriptResult]:
        """Generate multiple versions of the script for comparison"""
        versions_results = []
        
        for i in range(versions):
            # Adjust temperature for diversity
            if persona_config:
                original_temp = persona_config.consistency_temperature
                persona_config.consistency_temperature = min(1.0, original_temp + (i * 0.1))
            
            result = await self.generate_script(form, persona_config)
            versions_results.append(result)
            
            # Restore original temperature
            if persona_config:
                persona_config.consistency_temperature = original_temp
        
        logger.info(f"Generated {len(versions_results)} script versions")
        return versions_results
    
    async def regenerate_segment(self, form: OperationalForm, segment_type: ScriptSegmentType, persona_config: Optional[PersonaConfig] = None) -> Optional[LLMScriptSegment]:
        """Regenerate a specific segment with quality control"""
        try:
            if persona_config is None:
                persona_config = await self._get_default_persona_config(form)
            
            await self._initialize_llm()
            
            # Generate segment based on type
            segment = None
            if segment_type == ScriptSegmentType.OPENING:
                segment = await self._generate_opening_segment(form, persona_config)
            elif segment_type == ScriptSegmentType.PRODUCT_INTRO:
                segment = await self._generate_product_intro_segment(form, persona_config)
            elif segment_type == ScriptSegmentType.PRICE_ANNOUNCEMENT:
                segment = await self._generate_price_segment(form, persona_config)
            elif segment_type == ScriptSegmentType.CALL_TO_ACTION:
                segment = await self._generate_cta_segment(form, persona_config)
            elif segment_type == ScriptSegmentType.CLOSING:
                segment = await self._generate_closing_segment(form, persona_config)
            
            if segment and self.generation_config["enable_quality_check"]:
                quality_score = await self._assess_content_quality(segment.content)
                if quality_score < self.generation_config["quality_threshold"]:
                    # Retry with adjusted parameters
                    logger.warning(f"Segment quality below threshold: {quality_score}")
                    segment = await self._retry_generation_with_adjustment(form, segment_type, persona_config)
            
            self.generation_stats["regeneration_requests"] += 1
            return segment
            
        except Exception as e:
            logger.error(f"Failed to regenerate segment {segment_type}: {e}")
            return None
    
    async def _assess_content_quality(self, content: str) -> float:
        """Assess content quality using simple heuristics"""
        try:
            # Basic quality metrics
            word_count = len(content.split())
            sentence_count = content.count('.') + content.count('!') + content.count('?')
            avg_sentence_length = word_count / max(sentence_count, 1)
            
            # Quality factors
            factors = {
                "length_appropriate": 1.0 if 50 <= word_count <= 200 else 0.5,
                "sentence_variety": 1.0 if 5 <= avg_sentence_length <= 20 else 0.6,
                "has_interaction": 1.0 if any(word in content.lower() for word in ['吗', '呢', '怎么样', '对吧', '是不是']) else 0.7,
                "natural_flow": 1.0 if not any(phrase in content for phrase in ['###', '```', '<']) else 0.3,
                "completeness": 1.0 if content.strip() and len(content.strip()) > 20 else 0.2
            }
            
            # Calculate weighted score
            quality_score = sum(factors.values()) / len(factors)
            logger.debug(f"Content quality assessment: {quality_score:.2f}")
            
            return quality_score
            
        except Exception as e:
            logger.warning(f"Quality assessment failed: {e}")
            return 0.5  # Default neutral score
    
    async def _retry_generation_with_adjustment(self, form: OperationalForm, segment_type: ScriptSegmentType, persona_config: PersonaConfig) -> Optional[LLMScriptSegment]:
        """Retry generation with adjusted parameters for better quality"""
        try:
            # Adjust temperature for more focused generation
            original_temp = persona_config.consistency_temperature
            persona_config.consistency_temperature = max(0.1, original_temp - self.generation_config["temperature_adjustment"])
            
            # Regenerate with adjusted config
            segment = None
            if segment_type == ScriptSegmentType.OPENING:
                segment = await self._generate_opening_segment(form, persona_config)
            elif segment_type == ScriptSegmentType.PRODUCT_INTRO:
                segment = await self._generate_product_intro_segment(form, persona_config)
            elif segment_type == ScriptSegmentType.PRICE_ANNOUNCEMENT:
                segment = await self._generate_price_segment(form, persona_config)
            elif segment_type == ScriptSegmentType.CALL_TO_ACTION:
                segment = await self._generate_cta_segment(form, persona_config)
            elif segment_type == ScriptSegmentType.CLOSING:
                segment = await self._generate_closing_segment(form, persona_config)
            
            # Restore original temperature
            persona_config.consistency_temperature = original_temp
            
            self.generation_stats["quality_adjustments"] += 1
            return segment
            
        except Exception as e:
            logger.error(f"Retry generation failed: {e}")
            return None
    
    def update_generation_config(self, config_updates: Dict[str, Any]) -> None:
        """Update generation configuration parameters"""
        self.generation_config.update(config_updates)
        logger.info(f"Generation config updated: {config_updates}")
    
    def get_generation_config(self) -> Dict[str, Any]:
        """Get current generation configuration"""
        return self.generation_config.copy()
    
    def get_detailed_stats(self) -> Dict[str, Any]:
        """Get detailed generation statistics"""
        total_requests = self.generation_stats["scripts_generated"] + self.generation_stats["regeneration_requests"]
        
        return {
            **self.generation_stats,
            "total_requests": total_requests,
            "average_tokens_per_script": self.generation_stats["total_tokens_used"] / max(self.generation_stats["scripts_generated"], 1),
            "quality_adjustment_rate": self.generation_stats["quality_adjustments"] / max(total_requests, 1),
            "regeneration_rate": self.generation_stats["regeneration_requests"] / max(total_requests, 1),
            "config": self.generation_config
        }
    
    async def _generate_product_intro_segment(self, form: OperationalForm, persona_config: PersonaConfig) -> Optional[LLMScriptSegment]:
        """Generate product introduction segment using LLM"""
        try:
            system_prompt = self._build_persona_system_prompt(persona_config, "product_intro")
            
            # Build rich product context
            product_info = form.product_information
            selling_points = form.selling_points_structure.selling_points
            
            # Extract key highlights from selling points
            key_highlights = []
            for sp in selling_points[:3]:  # Top 3 selling points
                key_highlights.append(f"• {sp.title}: {sp.description}")
            
            user_prompt = f"""
请为以下产品生成详细介绍内容。

产品基础信息：
- 产品名称：{product_info.product_name}
- 品牌：{product_info.brand}
- 类别：{product_info.category}
- 主要规格：{', '.join(product_info.key_specifications)}
- 价格：{product_info.current_price}元

核心价值主张：
{form.selling_points_structure.primary_value_proposition}

产品亮点：
{chr(10).join(key_highlights)}

竞争优势：
{', '.join(form.selling_points_structure.competitive_advantages)}

要求：
1. 控制在90秒内的内容长度
2. 语调要{persona_config.voice_style.value}
3. 突出产品的独特价值和核心优势
4. 自然过渡到后续卖点介绍
5. 包含适度的互动引导
6. 符合{persona_config.name}的表达风格
7. 避免过度技术化，保持通俗易懂
8. 直接输出内容，不要包含格式标记

请生成产品介绍内容：
"""
            
            messages = [
                LLMMessage(role="system", content=system_prompt),
                LLMMessage(role="user", content=user_prompt)
            ]
            
            base_params = {
                'max_tokens': 500,
                'temperature': persona_config.consistency_temperature
            }
            params = self._prepare_generation_params(base_params)
            
            response = await self._llm_adapter.generate(messages, **params)
            
            return LLMScriptSegment(
                segment_id="product_intro_001",
                segment_type=ScriptSegmentType.PRODUCT_INTRO,
                title="产品介绍",
                content=response.content.strip(),
                estimated_duration_seconds=90,
                priority=PriorityLevel.HIGH,
                triggers=["after_opening"],
                variables={
                    "product_name": product_info.product_name,
                    "brand": product_info.brand,
                    "category": product_info.category,
                    "price": product_info.current_price,
                    "key_specifications": product_info.key_specifications,
                    "value_proposition": form.selling_points_structure.primary_value_proposition
                },
                generation_metadata={
                    "model": response.model,
                    "tokens_used": response.usage.get("total_tokens", 0),
                    "response_time_ms": response.response_time_ms,
                    "persona_id": persona_config.persona_id,
                    "generation_type": "product_introduction"
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to generate product intro segment: {e}")
            return None
    
    async def _generate_price_segment(self, form: OperationalForm, persona_config: PersonaConfig) -> Optional[LLMScriptSegment]:
        """Generate price strategy and announcement segment using LLM"""
        try:
            system_prompt = self._build_persona_system_prompt(persona_config, "price_announcement")
            
            product_info = form.product_information
            selling_structure = form.selling_points_structure
            
            # Extract urgency factors and social proof
            urgency_text = ", ".join(selling_structure.urgency_factors) if selling_structure.urgency_factors else "无特殊限时条件"
            social_proof_text = ", ".join(selling_structure.social_proof) if selling_structure.social_proof else "暂无社会证明信息"
            
            user_prompt = f"""
请为以下产品生成价格策略和价值阐述内容。

产品信息：
- 产品名称：{product_info.product_name}
- 品牌：{product_info.brand}
- 当前价格：{product_info.current_price}元

价值主张：
{selling_structure.primary_value_proposition}

竞争优势：
{', '.join(selling_structure.competitive_advantages)}

紧迫因素：
{urgency_text}

社会证明：
{social_proof_text}

异议处理参考：
{str(selling_structure.objection_handling) if selling_structure.objection_handling else '暂无'}

要求：
1. 控制在60-90秒内的内容长度
2. 语调要{persona_config.voice_style.value}
3. 重点阐述价格的合理性和价值性
4. 如有紧迫因素，自然融入稀缺性和时效性
5. 利用社会证明增强说服力
6. 预先处理可能的价格异议
7. 符合{persona_config.name}的表达风格
8. 避免过度销售，保持真诚感
9. 包含适度的行动引导
10. 直接输出内容，不要包含格式标记

请生成价格策略阐述内容：
"""
            
            messages = [
                LLMMessage(role="system", content=system_prompt),
                LLMMessage(role="user", content=user_prompt)
            ]
            
            base_params = {
                'max_tokens': 400,
                'temperature': persona_config.consistency_temperature
            }
            params = self._prepare_generation_params(base_params)
            
            response = await self._llm_adapter.generate(messages, **params)
            
            return LLMScriptSegment(
                segment_id="price_announcement_001",
                segment_type=ScriptSegmentType.PRICE_ANNOUNCEMENT,
                title="价格策略阐述",
                content=response.content.strip(),
                estimated_duration_seconds=75,
                priority=PriorityLevel.HIGH,
                triggers=["after_selling_points"],
                variables={
                    "current_price": product_info.current_price,
                    "urgency_factors": selling_structure.urgency_factors,
                    "social_proof": selling_structure.social_proof,
                    "objection_handling": selling_structure.objection_handling,
                    "competitive_advantages": selling_structure.competitive_advantages
                },
                generation_metadata={
                    "model": response.model,
                    "tokens_used": response.usage.get("total_tokens", 0),
                    "response_time_ms": response.response_time_ms,
                    "persona_id": persona_config.persona_id,
                    "generation_type": "price_strategy"
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to generate price segment: {e}")
            return None
    
    async def _generate_cta_segment(self, form: OperationalForm, persona_config: PersonaConfig) -> Optional[LLMScriptSegment]:
        """Generate call-to-action segment using LLM"""
        try:
            system_prompt = self._build_persona_system_prompt(persona_config, "call_to_action")
            
            product_info = form.product_information
            selling_structure = form.selling_points_structure
            basic_info = form.basic_information
            
            # Extract CTA options and urgency factors
            cta_options = selling_structure.call_to_actions
            urgency_factors = selling_structure.urgency_factors
            
            user_prompt = f"""
请为以下产品生成强有力的行动召唤内容。

产品信息：
- 产品名称：{product_info.product_name}
- 品牌：{product_info.brand}
- 价格：{product_info.current_price}元
- SKU：{product_info.primary_sku}

直播背景：
- 直播标题：{basic_info.stream_title}
- 目标用户：{self._get_target_audience_for_category(product_info.category, form)}
- 直播类型：{basic_info.stream_type}

预设行动召唤：
{', '.join(cta_options) if cta_options else '立即购买，享受优惠'}

紧迫因素：
{', '.join(urgency_factors) if urgency_factors else '无特殊限时因素'}

社会证明：
{', '.join(selling_structure.social_proof) if selling_structure.social_proof else '用户满意度高'}

要求：
1. 控制在45-60秒内的内容长度
2. 语调要{persona_config.voice_style.value}，充满感染力
3. 创造明确、具体的行动指引
4. 巧妙融入紧迫感和稀缺性
5. 利用社会证明降低决策风险
6. 提供多种行动选项（购买、咨询、关注等）
7. 符合{persona_config.name}的表达风格
8. 保持诚恳，避免过度夸张
9. 包含感谢和关系维护元素
10. 直接输出内容，不要包含格式标记

请生成行动召唤内容：
"""
            
            messages = [
                LLMMessage(role="system", content=system_prompt),
                LLMMessage(role="user", content=user_prompt)
            ]
            
            base_params = {
                'max_tokens': 350,
                'temperature': persona_config.consistency_temperature
            }
            params = self._prepare_generation_params(base_params)
            
            response = await self._llm_adapter.generate(messages, **params)
            
            return LLMScriptSegment(
                segment_id="cta_001",
                segment_type=ScriptSegmentType.CALL_TO_ACTION,
                title="行动召唤",
                content=response.content.strip(),
                estimated_duration_seconds=60,
                priority=PriorityLevel.HIGH,
                triggers=["after_price_announcement"],
                variables={
                    "product_name": product_info.product_name,
                    "price": product_info.current_price,
                    "sku": product_info.primary_sku,
                    "cta_options": cta_options,
                    "urgency_factors": urgency_factors,
                    "target_audience": self._get_target_audience_for_category(product_info.category, form)
                },
                generation_metadata={
                    "model": response.model,
                    "tokens_used": response.usage.get("total_tokens", 0),
                    "response_time_ms": response.response_time_ms,
                    "persona_id": persona_config.persona_id,
                    "generation_type": "call_to_action"
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to generate CTA segment: {e}")
            return None
    
    async def _generate_closing_segment(self, form: OperationalForm, persona_config: PersonaConfig) -> Optional[LLMScriptSegment]:
        """Generate closing segment using LLM"""
        try:
            system_prompt = self._build_persona_system_prompt(persona_config, "closing")
            
            product_info = form.product_information
            basic_info = form.basic_information
            selling_structure = form.selling_points_structure
            
            # Calculate planned end time for context
            planned_minutes = basic_info.planned_duration_minutes
            
            user_prompt = f"""
请为以下直播生成温馨而有效的收尾内容。

直播基本信息：
- 直播标题：{basic_info.stream_title}
- 目标观众：{self._get_target_audience_for_category(product_info.category, form)}
- 计划时长：{planned_minutes}分钟
- 直播类型：{basic_info.stream_type}

产品回顾：
- 产品名称：{product_info.product_name}
- 品牌：{product_info.brand}
- 核心价值：{selling_structure.primary_value_proposition}

关键卖点回顾：
{', '.join([sp.title for sp in selling_structure.selling_points[:3]])}

社会证明：
{', '.join(selling_structure.social_proof) if selling_structure.social_proof else '观众互动活跃'}

要求：
1. 控制在60-90秒内的内容长度
2. 语调要{persona_config.voice_style.value}，温暖而诚恳
3. 简要回顾产品核心价值和亮点
4. 感谢观众的陪伴和互动
5. 最后一次温和的行动引导
6. 预告后续内容或活动（如适用）
7. 建立持续关注的联系
8. 符合{persona_config.name}的表达风格
9. 营造温馨的告别氛围
10. 留下正面深刻的最终印象
11. 直接输出内容，不要包含格式标记

请生成直播收尾内容：
"""
            
            messages = [
                LLMMessage(role="system", content=system_prompt),
                LLMMessage(role="user", content=user_prompt)
            ]
            
            base_params = {
                'max_tokens': 400,
                'temperature': persona_config.consistency_temperature
            }
            params = self._prepare_generation_params(base_params)
            
            response = await self._llm_adapter.generate(messages, **params)
            
            return LLMScriptSegment(
                segment_id="closing_001",
                segment_type=ScriptSegmentType.CLOSING,
                title="直播收尾",
                content=response.content.strip(),
                estimated_duration_seconds=75,
                priority=PriorityLevel.HIGH,
                triggers=["after_cta", "stream_ending"],
                variables={
                    "stream_title": basic_info.stream_title,
                    "target_audience": self._get_target_audience_for_category(product_info.category, form),
                    "planned_duration": planned_minutes,
                    "product_name": product_info.product_name,
                    "brand": product_info.brand,
                    "key_selling_points": [sp.title for sp in selling_structure.selling_points[:3]],
                    "social_proof": selling_structure.social_proof
                },
                generation_metadata={
                    "model": response.model,
                    "tokens_used": response.usage.get("total_tokens", 0),
                    "response_time_ms": response.response_time_ms,
                    "persona_id": persona_config.persona_id,
                    "generation_type": "closing_remarks"
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to generate closing segment: {e}")
            return None
    
    async def _get_default_persona_config(self, form: OperationalForm) -> PersonaConfig:
        """Get default persona configuration"""
        # Create a basic persona config if none provided
        from ..models.persona import PersonaType, VoiceStyle
        
        return PersonaConfig(
            persona_id="default_ai_host",
            name="AI主播小助手",
            description="专业、友好、有亲和力的直播主播",
            persona_type=PersonaType.TEMPLATE_BASED,
            voice_style=VoiceStyle.WARM,
            consistency_temperature=0.3
        )
    
    def _extract_persona_info(self, persona_config: PersonaConfig) -> Dict[str, Any]:
        """Extract persona information for result"""
        return {
            "persona_id": persona_config.persona_id,
            "name": persona_config.name,
            "voice_style": persona_config.voice_style.value,
            "speaking_rate": persona_config.speaking_rate,
            "persona_type": persona_config.persona_type.value
        }
    
    # === Error Handling and Recovery Methods ===
    
    async def _initialize_llm_with_fallback(self):
        """Initialize LLM with fallback handling"""
        max_attempts = 3
        last_error = None
        
        for attempt in range(max_attempts):
            try:
                await self._initialize_llm()
                return
            except Exception as e:
                last_error = e
                logger.warning(f"LLM initialization attempt {attempt + 1} failed: {e}")
                if attempt < max_attempts - 1:
                    await asyncio.sleep(1 * (attempt + 1))  # Progressive delay
        
        raise ServiceError(f"Failed to initialize LLM after {max_attempts} attempts: {last_error}")
    
    def _validate_form_for_generation(self, form: OperationalForm) -> List[str]:
        """Validate form data for script generation"""
        errors = []
        
        try:
            # Check basic information
            if not form.basic_information:
                errors.append("Missing basic information")
            else:
                if not form.basic_information.stream_title or len(form.basic_information.stream_title) < 5:
                    errors.append("Stream title too short or missing")
            
            # Check product information
            if not form.product_information:
                errors.append("Missing product information")
            else:
                if not form.product_information.product_name or len(form.product_information.product_name) < 3:
                    errors.append("Product name too short or missing")
                if not form.product_information.brand:
                    errors.append("Brand information missing")
            
            # Check selling points
            if not form.selling_points_structure:
                errors.append("Missing selling points structure")
            else:
                if not form.selling_points_structure.selling_points:
                    errors.append("No selling points defined")
                elif len(form.selling_points_structure.selling_points) < 2:
                    errors.append("At least 2 selling points required for quality generation")
        
        except AttributeError as e:
            errors.append(f"Form structure error: {e}")
        
        return errors
    
    async def _generate_all_segments_with_recovery(self, form: OperationalForm, persona_config: PersonaConfig, retry_on_failure: bool) -> List[LLMScriptSegment]:
        """Generate all segments with error recovery"""
        segments = []
        failed_segments = []
        
        segment_generators = [
            ("opening", self._generate_opening_segment),
            ("product_intro", self._generate_product_intro_segment),
            ("selling_points", self._generate_selling_point_segments_safe),
            ("price", self._generate_price_segment),
            ("cta", self._generate_cta_segment),
            ("closing", self._generate_closing_segment)
        ]
        
        for segment_name, generator_func in segment_generators:
            try:
                if segment_name == "selling_points":
                    # Special handling for multiple selling point segments
                    sp_segments = await self._generate_selling_point_segments_safe(form, persona_config)
                    segments.extend(sp_segments)
                else:
                    segment = await generator_func(form, persona_config)
                    if segment:
                        segments.append(segment)
                    else:
                        failed_segments.append(segment_name)
                        
            except Exception as e:
                logger.error(f"Failed to generate {segment_name} segment: {e}")
                failed_segments.append(segment_name)
                
                # Retry critical segments if enabled
                if retry_on_failure and segment_name in ["opening", "product_intro"]:
                    try:
                        logger.info(f"Retrying critical segment: {segment_name}")
                        segment = await generator_func(form, persona_config)
                        if segment:
                            segments.append(segment)
                            failed_segments.remove(segment_name)
                    except Exception as retry_e:
                        logger.error(f"Retry failed for {segment_name}: {retry_e}")
        
        # Log generation summary
        logger.info(f"Generated {len(segments)} segments, {len(failed_segments)} failed: {failed_segments}")
        return segments
    
    async def _generate_selling_point_segments_safe(self, form: OperationalForm, persona_config: PersonaConfig) -> List[LLMScriptSegment]:
        """Safely generate selling point segments with error handling"""
        segments = []
        
        for i, selling_point in enumerate(form.selling_points_structure.selling_points):
            try:
                segment = await self._generate_selling_point_segment(form, selling_point, persona_config, i)
                if segment:
                    segments.append(segment)
            except Exception as e:
                logger.warning(f"Failed to generate selling point segment {i}: {e}")
                # Continue with other selling points
                continue
        
        return segments
    
    # === Batch Generation Optimization Methods ===
    
    async def generate_script_batch(self, forms: List[OperationalForm], persona_configs: Optional[List[PersonaConfig]] = None) -> List[LLMScriptResult]:
        """Generate scripts for multiple forms concurrently"""
        if not forms:
            return []
        
        # Prepare persona configs
        if persona_configs is None:
            persona_configs = [None] * len(forms)
        elif len(persona_configs) != len(forms):
            # Extend or truncate to match forms length
            persona_configs = (persona_configs * len(forms))[:len(forms)]
        
        # Create concurrent tasks
        tasks = []
        for form, persona_config in zip(forms, persona_configs):
            task = self.generate_script(form, persona_config)
            tasks.append(task)
        
        # Execute batch with controlled concurrency
        max_concurrent = min(5, len(tasks))  # Limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def bounded_generate(task):
            async with semaphore:
                return await task
        
        bounded_tasks = [bounded_generate(task) for task in tasks]
        results = await asyncio.gather(*bounded_tasks, return_exceptions=True)
        
        # Process results and handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Batch generation failed for form {i}: {result}")
                # Create error result
                error_result = LLMScriptResult(
                    success=False,
                    form_id=forms[i].form_id,
                    segments=[],
                    total_duration_seconds=0,
                    persona_info={},
                    generation_stats={},
                    error_messages=[f"Batch generation failed: {str(result)}"],
                    warnings=[]
                )
                final_results.append(error_result)
            else:
                final_results.append(result)
        
        logger.info(f"Batch generation completed: {len(final_results)} scripts processed")
        return final_results
    
    async def generate_segments_concurrently(self, form: OperationalForm, persona_config: PersonaConfig) -> List[LLMScriptSegment]:
        """Generate script segments concurrently for faster performance"""
        try:
            # Create tasks for independent segments (non-selling points)
            tasks = {
                "opening": self._generate_opening_segment(form, persona_config),
                "product_intro": self._generate_product_intro_segment(form, persona_config), 
                "price": self._generate_price_segment(form, persona_config),
                "cta": self._generate_cta_segment(form, persona_config),
                "closing": self._generate_closing_segment(form, persona_config)
            }
            
            # Execute concurrent generation
            results = await asyncio.gather(*tasks.values(), return_exceptions=True)
            
            # Process results
            segments = []
            for segment_type, result in zip(tasks.keys(), results):
                if isinstance(result, Exception):
                    logger.warning(f"Concurrent generation failed for {segment_type}: {result}")
                elif result:
                    segments.append(result)
            
            # Generate selling point segments sequentially (they depend on each other)
            selling_point_segments = await self._generate_selling_point_segments_safe(form, persona_config)
            segments.extend(selling_point_segments)
            
            # Sort segments by their logical order
            segment_order = {
                ScriptSegmentType.OPENING: 1,
                ScriptSegmentType.PRODUCT_INTRO: 2, 
                ScriptSegmentType.SELLING_POINT: 3,
                ScriptSegmentType.PRICE_ANNOUNCEMENT: 4,
                ScriptSegmentType.CALL_TO_ACTION: 5,
                ScriptSegmentType.CLOSING: 6
            }
            
            segments.sort(key=lambda s: segment_order.get(s.segment_type, 99))
            
            logger.info(f"Concurrent generation completed: {len(segments)} segments")
            return segments
            
        except Exception as e:
            logger.error(f"Concurrent generation failed: {e}")
            return []
    
    async def optimize_generation_pipeline(self, form: OperationalForm, persona_config: Optional[PersonaConfig] = None) -> LLMScriptResult:
        """Optimized script generation with concurrency and error recovery"""
        start_time = datetime.utcnow()
        
        # Generate with optimizations
        result = LLMScriptResult(
            success=False,
            form_id=form.form_id,
            segments=[],
            total_duration_seconds=0,
            persona_info={},
            generation_stats={},
            error_messages=[],
            warnings=[]
        )
        
        try:
            # Initialize and validate
            await self._initialize_llm_with_fallback()
            
            if persona_config is None:
                persona_config = await self._get_default_persona_config(form)
            
            # Use concurrent generation for better performance
            segments = await self.generate_segments_concurrently(form, persona_config)
            
            if not segments:
                result.error_messages.append("No segments generated")
                return result
            
            # Build successful result
            total_duration = sum(seg.estimated_duration_seconds for seg in segments)
            
            result.success = True
            result.segments = segments
            result.total_duration_seconds = total_duration
            result.persona_info = self._extract_persona_info(persona_config)
            result.generation_stats = {
                "generation_time_seconds": (datetime.utcnow() - start_time).total_seconds(),
                "segments_count": len(segments),
                "total_duration_seconds": total_duration,
                "persona_type": persona_config.persona_type.value if persona_config else "default",
                "concurrent_generation": True
            }
            
            # Update stats
            self.generation_stats["scripts_generated"] += 1
            self.generation_stats["persona_adaptations"] += 1 if persona_config else 0
            self.generation_stats["total_tokens_used"] += sum(seg.generation_metadata.get("tokens_used", 0) for seg in segments)
            
            logger.info(f"Optimized generation completed for form {form.form_id}")
            return result
            
        except Exception as e:
            logger.error(f"Optimized generation failed: {e}")
            result.error_messages.append(f"Generation failed: {str(e)}")
            return result
    
    # === Enhanced Personalization Control Methods ===
    
    def create_industry_specific_persona(self, industry: str, base_persona: Optional[PersonaConfig] = None) -> PersonaConfig:
        """Create industry-specific persona configuration"""
        from ..models.persona import PersonaType, VoiceStyle
        from ..core.config import cfg
        
        # Get industry-specific configurations from config
        industry_personas = cfg.get_yaml_config('business.industry_personas', {})
        industry_config = industry_personas.get(industry.lower(), {})
        
        # Voice style mapping
        voice_style_map = {
            "warm": VoiceStyle.WARM,
            "professional": VoiceStyle.PROFESSIONAL,
            "energetic": VoiceStyle.ENERGETIC,
            "casual": VoiceStyle.CASUAL,
            "soothing": VoiceStyle.SOOTHING
        }
        
        # Use base persona or create new one
        if base_persona:
            persona_config = base_persona
        else:
            persona_config = PersonaConfig(
                persona_id=f"industry_{industry}",
                name="通用主播",
                description="通用产品介绍主播",
                persona_type=PersonaType.TEMPLATE_BASED,
                voice_style=VoiceStyle.WARM,
                consistency_temperature=0.3
            )
        
        # Apply industry-specific modifications from config
        if industry_config:
            # Set voice style
            voice_style_str = industry_config.get("voice_style", "warm")
            persona_config.voice_style = voice_style_map.get(voice_style_str, VoiceStyle.WARM)
            
            # Set name
            persona_config.name = industry_config.get("name", "通用主播")
            
            # Set speaking rate if available
            speaking_rate = industry_config.get("speaking_rate", 1.0)
            if hasattr(persona_config, 'speaking_rate'):
                persona_config.speaking_rate = speaking_rate
            
            # Set energy level if available
            energy_level = industry_config.get("energy_level", "medium")
            if hasattr(persona_config, 'energy_level'):
                persona_config.energy_level = energy_level
            
            # Set tone keywords if available
            tone_keywords = industry_config.get("tone_keywords", [])
            if hasattr(persona_config, 'tone_keywords'):
                persona_config.tone_keywords = tone_keywords
            
        logger.info(f"Created {industry} industry persona: {persona_config.name} (from config)")
        return persona_config
    
    def _get_target_audience_for_category(self, category: str, form: OperationalForm) -> str:
        """Derive target audience from product category configuration"""
        from ..core.config import cfg
        
        # Get target audience mapping from config
        category_audiences = cfg.get_yaml_config('business.target_audience_mapping', {})
        
        # Try exact match first
        audience = category_audiences.get(category)
        if audience:
            return audience
        
        # Try case-insensitive match
        audience = category_audiences.get(category.lower())
        if audience:
            return audience
        
        # Try partial match for Chinese categories
        for cat_key, aud in category_audiences.items():
            if cat_key in category or category in cat_key:
                return aud
        
        # Default based on stream type if no category match
        stream_type_audiences = cfg.get_yaml_config('business.stream_type_audience', {})
        stream_type = form.basic_information.stream_type.value
        audience = stream_type_audiences.get(stream_type)
        if audience:
            return audience
        
        # Final fallback from config
        return cfg.get_yaml_config('business.default_audience', "18-45岁消费者")