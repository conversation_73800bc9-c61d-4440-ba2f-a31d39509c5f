"""
Capsule Templates for Microcycle Narrative Model

Provides templates for different types of content capsules used in continuous
live streaming script generation.
"""

from typing import Dict, List, Optional, Any
from enum import Enum
from dataclasses import dataclass


class CapsuleType(Enum):
    """Types of content capsules"""
    OPENING = "opening"
    SELLING_POINT = "selling_point"
    PRICE_VALUE = "price_value"
    PROOF_DEMO = "proof_demo"
    BRAND_STORY = "brand_story"
    HALF_TIME_SUMMARY = "half_time_summary"
    QA = "qa"
    CLOSING = "closing"
    TRANSITION = "transition"


@dataclass
class CapsuleTemplate:
    """Template for a content capsule"""
    capsule_type: CapsuleType
    title: str
    structure: List[str]  # Hook, Unpack, Proof, CTA, Transition
    duration_seconds: tuple  # (min, max) duration
    weight: float  # Selection weight
    cooldown_seconds: int  # Cooldown period before reuse
    variables: Dict[str, Any]  # Template variables
    system_prompt: str
    user_prompt_template: str


class CapsuleTemplateLibrary:
    """Library of capsule templates for content generation"""
    
    def __init__(self):
        self.templates = self._initialize_templates()
        
    def _initialize_templates(self) -> Dict[CapsuleType, List[CapsuleTemplate]]:
        """Initialize all capsule templates"""
        return {
            CapsuleType.SELLING_POINT: self._create_selling_point_templates(),
            CapsuleType.PRICE_VALUE: self._create_price_value_templates(),
            CapsuleType.PROOF_DEMO: self._create_proof_demo_templates(),
            CapsuleType.BRAND_STORY: self._create_brand_story_templates(),
            CapsuleType.HALF_TIME_SUMMARY: self._create_halftime_templates(),
            CapsuleType.OPENING: self._create_opening_templates(),
            CapsuleType.CLOSING: self._create_closing_templates(),
            CapsuleType.QA: self._create_qa_templates(),
        }
    
    def _create_selling_point_templates(self) -> List[CapsuleTemplate]:
        """Create selling point capsule templates"""
        return [
            CapsuleTemplate(
                capsule_type=CapsuleType.SELLING_POINT,
                title="卖点胶囊-变体1",
                structure=["Hook", "Unpack", "Proof", "CTA", "Transition"],
                duration_seconds=(60, 120),
                weight=1.0,
                cooldown_seconds=300,
                variables={},
                system_prompt="""[系统角色]
你是一名中文直播主播，风格自然口语化。请以"内容胶囊（1-2分钟）"的形式输出：
Hook → Unpack → Proof → CTA → Transition。

[输出要求]
1) 只输出口播内容；禁止Markdown/项目符号/代码块
2) 严格遵循"Hook→Unpack→Proof→CTA→Transition"结构，标点分句自然
3) 总时长建议1-2分钟；每句长度适中，便于句级TTS播报
4) 内容必须与当前胶囊的主题强相关""",
                user_prompt_template="""[胶囊类型]
Selling Point（主题：{{selling_point.title}}）

[人设]
- 名称：{{persona.name}}
- 语气/口条：{{persona.tone}}；语速：{{persona.speaking_rate}}倍

[产品事实表]
- 品类：{{product.category}}
- 目标用户：{{product.target}}
- 关键卖点：{{product.key_points}}
- 核心参数：{{product.specs}}
- 价格与优惠：{{product.price}} / {{product.promo}}

[承上与启下]
- 承上（≤50字）：{{recent_summary_short}}
- 启下预告（≤30字）：{{next_topic_preview}}

[Hook]
一句话指出该卖点解决的核心痛点/价值

[Unpack]
围绕 {{selling_point.title}} 展开细节（参数/机制/场景），3-5句

[Proof]
提供1-2个证据（数据对比/用户评价/演示）

[CTA]
低门槛行动号召（轻CTA，1句）

[Transition]
1句自然过渡，引出下一个主题：{{next_topic_preview}}"""
            ),
            CapsuleTemplate(
                capsule_type=CapsuleType.SELLING_POINT,
                title="卖点胶囊-变体2",
                structure=["Hook", "Unpack", "Proof", "CTA", "Transition"],
                duration_seconds=(60, 120),
                weight=1.0,
                cooldown_seconds=300,
                variables={},
                system_prompt="""[系统角色]
你是一名专业的中文直播主播，善于用故事和场景打动观众。输出格式为连贯的口播内容。""",
                user_prompt_template="""[任务]
为产品卖点"{{selling_point.title}}"创建一个60-90秒的介绍片段。

[产品信息]
{{product.description}}

[要求]
1. 从用户痛点或生活场景开始
2. 自然引入卖点的价值
3. 用具体例子或数据支撑
4. 给出简单的行动建议
5. 平滑过渡到下一个话题

请直接输出口播内容，不要使用标题或分段标记。"""
            )
        ]
    
    def _create_price_value_templates(self) -> List[CapsuleTemplate]:
        """Create price/value capsule templates"""
        return [
            CapsuleTemplate(
                capsule_type=CapsuleType.PRICE_VALUE,
                title="价格价值胶囊",
                structure=["Hook", "Unpack", "Proof", "CTA", "Transition"],
                duration_seconds=(60, 90),
                weight=0.8,
                cooldown_seconds=600,
                variables={},
                system_prompt="""[系统角色]
你是一名中文直播主播，擅长展示产品的价值和性价比。请生成自然流畅的口播内容。""",
                user_prompt_template="""[任务]
展示产品的价格合理性和超值之处

[产品价格信息]
- 原价：{{product.original_price}}
- 现价：{{product.current_price}}
- 优惠：{{product.discount}}
- 赠品：{{product.gifts}}

[价值点]
{{product.value_points}}

[输出要求]
1. 强调性价比和限时优惠
2. 对比市场价或竞品
3. 突出额外价值（赠品、服务等）
4. 营造紧迫感但不过度
5. 自然过渡到下一话题

直接输出60-90秒的口播内容。"""
            )
        ]
    
    def _create_proof_demo_templates(self) -> List[CapsuleTemplate]:
        """Create proof/demo capsule templates"""
        return [
            CapsuleTemplate(
                capsule_type=CapsuleType.PROOF_DEMO,
                title="证明演示胶囊",
                structure=["Hook", "Demo", "Result", "CTA", "Transition"],
                duration_seconds=(90, 120),
                weight=0.7,
                cooldown_seconds=900,
                variables={},
                system_prompt="""[系统角色]
你是一名中文直播主播，擅长通过演示和证据展示产品效果。""",
                user_prompt_template="""[任务]
通过具体的演示或用户案例证明产品效果

[演示/案例内容]
{{proof.content}}

[数据支撑]
{{proof.data}}

[用户反馈]
{{proof.testimonials}}

[输出要求]
1. 生动描述演示过程或用户故事
2. 强调前后对比
3. 引用具体数据或评价
4. 让观众感受到真实效果
5. 引导尝试或购买

输出90-120秒的自然口播内容。"""
            )
        ]
    
    def _create_brand_story_templates(self) -> List[CapsuleTemplate]:
        """Create brand story capsule templates"""
        return [
            CapsuleTemplate(
                capsule_type=CapsuleType.BRAND_STORY,
                title="品牌故事胶囊",
                structure=["Hook", "Story", "Values", "Connection", "Transition"],
                duration_seconds=(60, 90),
                weight=0.3,
                cooldown_seconds=1800,
                variables={},
                system_prompt="""[系统角色]
你是一名有温度的中文直播主播，善于讲述品牌故事，建立情感连接。""",
                user_prompt_template="""[任务]
分享品牌背后的故事或理念

[品牌信息]
- 品牌名：{{brand.name}}
- 创立故事：{{brand.story}}
- 核心理念：{{brand.values}}
- 特色：{{brand.uniqueness}}

[输出要求]
1. 用故事开场，吸引注意
2. 展现品牌的人文关怀
3. 连接产品与价值观
4. 建立信任和认同
5. 自然回到产品介绍

输出60-90秒的温暖口播内容。"""
            )
        ]
    
    def _create_halftime_templates(self) -> List[CapsuleTemplate]:
        """Create half-time summary capsule templates"""
        return [
            CapsuleTemplate(
                capsule_type=CapsuleType.HALF_TIME_SUMMARY,
                title="半场总结胶囊",
                structure=["Summary", "Highlight", "Preview", "CTA", "Transition"],
                duration_seconds=(45, 60),
                weight=0.5,
                cooldown_seconds=1200,
                variables={},
                system_prompt="""[系统角色]
你是一名中文直播主播，擅长承上启下，保持观众注意力。""",
                user_prompt_template="""[任务]
进行阶段性总结，承上启下

[已介绍内容]
{{summary.covered_points}}

[接下来内容]
{{summary.upcoming_points}}

[重点回顾]
{{summary.key_highlights}}

[输出要求]
1. 简要回顾已讲重点
2. 强调核心价值
3. 预告精彩内容
4. 保持期待感
5. 平滑过渡继续

输出45-60秒的总结性口播内容。"""
            )
        ]
    
    def _create_opening_templates(self) -> List[CapsuleTemplate]:
        """Create opening capsule templates"""
        return [
            CapsuleTemplate(
                capsule_type=CapsuleType.OPENING,
                title="开场胶囊",
                structure=["Welcome", "Hook", "Overview", "Value", "Start"],
                duration_seconds=(30, 45),
                weight=1.0,
                cooldown_seconds=3600,
                variables={},
                system_prompt="""[系统角色]
你是一名充满活力的中文直播主播，擅长开场和吸引观众。""",
                user_prompt_template="""[任务]
创建吸引人的开场白

[直播主题]
{{stream.topic}}

[产品亮点]
{{product.highlights}}

[今日特惠]
{{today.special_offers}}

[输出要求]
1. 热情欢迎，营造氛围
2. 快速勾起兴趣
3. 概述今日重点
4. 强调独特价值
5. 自然进入正题

输出30-45秒的精彩开场白。"""
            )
        ]
    
    def _create_closing_templates(self) -> List[CapsuleTemplate]:
        """Create closing capsule templates"""
        return [
            CapsuleTemplate(
                capsule_type=CapsuleType.CLOSING,
                title="结束胶囊",
                structure=["Summary", "LastCTA", "Thanks", "Preview", "Goodbye"],
                duration_seconds=(30, 45),
                weight=1.0,
                cooldown_seconds=3600,
                variables={},
                system_prompt="""[系统角色]
你是一名专业的中文直播主播，擅长优雅收尾，给观众留下美好印象。""",
                user_prompt_template="""[任务]
创建温暖的结束语

[本场重点回顾]
{{session.key_points}}

[最终优惠提醒]
{{final.offers}}

[感谢内容]
{{thanks.message}}

[输出要求]
1. 简要总结精华
2. 最后的行动号召
3. 真诚感谢观众
4. 预告下次内容（可选）
5. 温暖道别

输出30-45秒的优雅结束语。"""
            )
        ]
    
    def _create_qa_templates(self) -> List[CapsuleTemplate]:
        """Create Q&A capsule templates"""
        return [
            CapsuleTemplate(
                capsule_type=CapsuleType.QA,
                title="问答胶囊",
                structure=["Transition", "Question", "Answer", "Extension", "BackToMain"],
                duration_seconds=(30, 60),
                weight=0.6,
                cooldown_seconds=180,
                variables={},
                system_prompt="""[系统角色]
你是一名中文直播主播，擅长回答观众问题并自然过渡。""",
                user_prompt_template="""[任务]
回答观众问题并平滑过渡

[问题]
{{qa.question}}

[答案要点]
{{qa.answer_points}}

[相关信息]
{{qa.related_info}}

[主线话题]
{{main.topic}}

[输出要求]
1. 自然引入问题
2. 清晰准确回答
3. 适当扩展价值
4. 连接产品优势
5. 回归主线内容

输出30-60秒的问答内容。"""
            )
        ]
    
    def get_templates(self, capsule_type: CapsuleType) -> List[CapsuleTemplate]:
        """Get all templates for a specific capsule type"""
        return self.templates.get(capsule_type, [])
    
    def get_template(self, capsule_type: CapsuleType, variant: int = 0) -> Optional[CapsuleTemplate]:
        """Get a specific template variant"""
        templates = self.get_templates(capsule_type)
        if templates and 0 <= variant < len(templates):
            return templates[variant]
        return None
    
    def get_all_types(self) -> List[CapsuleType]:
        """Get all available capsule types"""
        return list(CapsuleType)