"""主动预合成服务

在客户端请求之前主动合成音频内容，减少响应延迟。

Author: Claude Code
Date: 2025-08-08
"""

import asyncio
import time
from typing import Set, Dict, Any, Optional, List, Tuple, TYPE_CHECKING
from dataclasses import dataclass
from datetime import datetime
from loguru import logger
from collections import deque

from ..core.streaming_config import StreamingConfig
from ..models.playlist_models import PlaylistItem
from .synthesis_event_bus import SynthesisRequest, get_synthesis_event_bus

if TYPE_CHECKING:
    from .streaming_content_provider import StreamingContentProvider
    from .client_state_tracker import ClientStateTracker
    from .playlist_manager import PlaylistManager


@dataclass
class SynthesisTask:
    """合成任务"""
    task_id: str
    index: int
    item: PlaylistItem
    cache_key: str
    priority: int  # 数字越小优先级越高
    created_at: datetime
    estimated_duration_ms: float
    is_qa: bool = False
    callback: Optional[asyncio.Future] = None  # For event bus requests
    
    def __lt__(self, other):
        """支持优先队列排序"""
        # 首先按优先级排序（QA优先）
        if self.is_qa != other.is_qa:
            return self.is_qa  # QA任务优先级更高
        return self.priority < other.priority


# FutureLease类已删除
# 新架构使用队列模式，不再需要Future租约管理


@dataclass
class SynthesisStats:
    """合成统计"""
    total_tasks_created: int = 0
    total_tasks_completed: int = 0
    total_tasks_failed: int = 0
    total_tasks_skipped: int = 0  # 已存在于缓存
    active_tasks: int = 0
    average_synthesis_time_ms: float = 0.0
    cache_hit_improvement: float = 0.0  # 预合成提升的缓存命中率
    
    @property
    def completion_rate(self) -> float:
        """完成率"""
        if self.total_tasks_created == 0:
            return 0.0
        return self.total_tasks_completed / self.total_tasks_created
        
    @property
    def success_rate(self) -> float:
        """成功率"""
        total_processed = self.total_tasks_completed + self.total_tasks_failed
        if total_processed == 0:
            return 0.0
        return self.total_tasks_completed / total_processed
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_tasks_created": self.total_tasks_created,
            "total_tasks_completed": self.total_tasks_completed,
            "total_tasks_failed": self.total_tasks_failed,
            "total_tasks_skipped": self.total_tasks_skipped,
            "active_tasks": self.active_tasks,
            "completion_rate": round(self.completion_rate, 4),
            "success_rate": round(self.success_rate, 4),
            "average_synthesis_time_ms": round(self.average_synthesis_time_ms, 2),
            "cache_hit_improvement": round(self.cache_hit_improvement, 4)
        }


class ProactiveSynthesizer:
    """
    主动预合成服务
    
    核心功能：
    1. 监控客户端播放进度，预测即将需要的内容
    2. 在后台预先合成音频，存入缓存
    3. 支持优先级调度，QA内容优先合成
    4. 智能控制并发数量，避免资源过载
    """
    
    def __init__(self, content_provider: 'StreamingContentProvider',
                 client_tracker: 'ClientStateTracker',
                 playlist_manager: 'PlaylistManager',
                 config: StreamingConfig):
        self.content_provider = content_provider
        self.client_tracker = client_tracker
        self.playlist_manager = playlist_manager
        self.config = config
        
        # 双优先级队列系统
        self._high_priority_queue = asyncio.Queue()  # 客户端请求（优先级0）
        self._normal_queue = asyncio.Queue()  # 预合成任务（优先级1+）
        self._active_tasks: Dict[str, asyncio.Task] = {}
        # 使用 deque 实现自动滑动窗口，避免内存无限增长
        self._processed_items: deque = deque(maxlen=100)  # 最多记录100个已处理项目
        
        # 工作协程
        self._workers: List[asyncio.Task] = []
        self._monitor_task: Optional[asyncio.Task] = None
        self._guardian_task: Optional[asyncio.Task] = None
        self._running = False
        self._monitor_restart_count = 0  # 监控任务重启次数
        
        # 新架构：不再需要Future租约管理
        
        # 事件总线
        self.event_bus = get_synthesis_event_bus()
        
        # TTS引擎从content_provider获取
        self.tts_engine = content_provider.tts_engine
        
        # 统计信息
        self._stats = SynthesisStats()
        self._start_time: Optional[datetime] = None
        
        # 并发控制
        self._semaphore: Optional[asyncio.Semaphore] = None
        
        logger.info(f"主动预合成服务初始化完成，最大并发数: {self.max_concurrent}")
        
    @property
    def max_concurrent(self) -> int:
        """最大并发合成数"""
        return self.config.proactive_max_concurrent
        
    @property
    def look_ahead_items(self) -> int:
        """前瞻项目数"""
        return self.config.proactive_look_ahead_items
        
    @property
    def check_interval(self) -> int:
        """检查间隔（秒）"""
        return self.config.proactive_check_interval
        
    @property
    def is_running(self) -> bool:
        """是否正在运行"""
        return self._running
        
    async def start(self) -> None:
        """启动预合成服务"""
        if not self.config.proactive_synthesis_enabled:
            logger.info("预合成服务已禁用")
            return
            
        if self._running:
            logger.warning("预合成服务已在运行")
            return
            
        self._running = True
        self._start_time = datetime.utcnow()
        
        # 启动事件总线
        await self.event_bus.start()
        
        # 创建信号量控制并发
        self._semaphore = asyncio.Semaphore(self.max_concurrent)
        
        # 启动工作协程
        for i in range(self.max_concurrent):
            worker = asyncio.create_task(self._synthesis_worker(i))
            self._workers.append(worker)
            
        # 启动监控协程
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        
        # 启动守护任务
        self._guardian_task = asyncio.create_task(self._guardian_monitor())
        
        logger.info(f"预合成服务已启动，工作线程: {len(self._workers)} 个，守护任务已激活")
        
    async def stop(self) -> None:
        """停止预合成服务"""
        if not self._running:
            return
            
        logger.info("正在停止预合成服务...")
        self._running = False
        
        # 停止监控任务
        if self._monitor_task and not self._monitor_task.done():
            self._monitor_task.cancel()
            
        # 停止守护任务
        if self._guardian_task and not self._guardian_task.done():
            self._guardian_task.cancel()
            
        # 等待监控任务完成
        if self._monitor_task:
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
                
        # 等待守护任务完成
        if self._guardian_task:
            try:
                await self._guardian_task
            except asyncio.CancelledError:
                pass
                
        # 取消所有工作协程
        for worker in self._workers:
            worker.cancel()
            
        # 等待工作协程完成
        if self._workers:
            await asyncio.gather(*self._workers, return_exceptions=True)
            
        # 取消活跃的合成任务
        for task in self._active_tasks.values():
            task.cancel()
            
        if self._active_tasks:
            await asyncio.gather(*self._active_tasks.values(), return_exceptions=True)
            
        # 清理状态
        self._workers.clear()
        self._active_tasks.clear()
        self._processed_items.clear()
        
        # 清空队列
        for queue in [self._high_priority_queue, self._normal_queue]:
            while not queue.empty():
                try:
                    queue.get_nowait()
                    queue.task_done()
                except asyncio.QueueEmpty:
                    break
        
        # 停止事件总线
        await self.event_bus.stop()
                
        logger.info("预合成服务已停止")
        
    async def trigger_qa_synthesis(self, qa_items: List[PlaylistItem]) -> None:
        """
        触发QA内容的优先合成
        
        Args:
            qa_items: QA播放项列表
        """
        if not self._running:
            logger.warning("预合成服务未运行，无法处理QA合成请求")
            return
            
        logger.info(f"触发QA优先合成: {len(qa_items)} 项")
        
        for item in qa_items:
            cache_key = item.get_cache_key()
            
            # 不再检查缓存（由引擎内部处理）
                
            # 创建高优先级任务
            task = SynthesisTask(
                task_id=f"qa_{item.item_id}_{int(time.time())}",
                index=-1,  # QA没有固定索引
                item=item,
                cache_key=cache_key,
                priority=0,  # 最高优先级
                created_at=datetime.utcnow(),
                estimated_duration_ms=item.estimate_duration_ms(),
                is_qa=True
            )
            
            # QA任务放入高优先级队列
            await self._high_priority_queue.put(task)
            self._stats.total_tasks_created += 1
            
            logger.debug(f"QA合成任务已加入队列: {item.item_id}")
            
    async def _guardian_monitor(self) -> None:
        """监控循环的守护者，确保核心监控任务始终运行"""
        check_interval = 30  # 守护任务检查间隔（秒）
        logger.info(f"守护任务已启动，检查间隔: {check_interval}秒")
        
        while self._running:
            try:
                await asyncio.sleep(check_interval)
                
                if not self._running:
                    break
                    
                # 检查监控任务状态
                if self._monitor_task and self._monitor_task.done():
                    # 监控任务异常退出
                    exc_info = None
                    try:
                        exc = self._monitor_task.exception()
                        exc_info = f"异常: {exc}"
                    except:
                        exc_info = "未知异常"
                    
                    logger.error(f"🚨 监控循环异常退出！{exc_info}")
                    
                    # 重启监控循环
                    logger.warning("🔄 正在重启预合成监控循环...")
                    self._monitor_task = asyncio.create_task(self._monitor_loop())
                    logger.info("✅ 预合成监控循环已重启")
                    
                    # 记录重启次数（用于监控）
                    self._monitor_restart_count += 1
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"守护任务错误: {e}")
                
        logger.info("守护任务已退出")
    
    async def _monitor_loop(self) -> None:
        """监控循环，决定需要预合成的内容"""
        logger.info("预合成监控循环已启动")
        
        while self._running:
            try:
                await asyncio.sleep(self.check_interval)
                
                if not self._running:
                    break
                    
                # 获取客户端播放进度
                stats = await self.client_tracker.get_aggregated_stats()
                
                # 统一初始化 max_playing_index，避免后续使用时未定义
                max_playing_index = -1  # 默认值，表示无客户端播放
                
                if stats.active_clients == 0:
                    # 无客户端时预合成前 N 个内容，提升"秒开"体验
                    playlist_length = await self.playlist_manager.get_playlist_length()
                    if playlist_length > 0:
                        start_index = 0
                        end_index = min(self.look_ahead_items, playlist_length)
                        logger.info(f"🚀 无活跃客户端，预热缓存：合成前 {end_index} 个内容")
                        # deque 会自动管理大小，无需手动清理
                    else:
                        continue  # 播放列表为空，跳过
                else:
                    # 有客户端时的正常逻辑
                    max_playing_index = stats.playing_index_range[1]
                    start_index = max(0, max_playing_index + 1)
                    end_index = min(start_index + self.look_ahead_items, 
                                  await self.playlist_manager.get_playlist_length())
                
                # 提升日志级别，从 trace 改为 info
                if stats.active_clients > 0:
                    logger.info(f"预合成检查: 客户端最大播放索引={max_playing_index}, "
                               f"预合成范围=[{start_index}, {end_index})")
                else:
                    logger.info(f"预合成检查: 无活跃客户端, 预合成范围=[{start_index}, {end_index})")
                
                # 检查每个位置是否需要合成
                tasks_added = 0
                
                for index in range(start_index, end_index):
                    item = await self.playlist_manager.get_item_at(index)
                    
                    if not item:
                        continue
                        
                    cache_key = item.get_cache_key()
                    
                    # 跳过已处理的项目
                    if cache_key in self._processed_items:
                        continue
                        
                    # 不再检查缓存（由引擎内部处理）
                        
                    # 计算优先级（距离播放越近优先级越高）
                    priority = index - max_playing_index
                    
                    # 创建合成任务
                    task = SynthesisTask(
                        task_id=f"proactive_{item.item_id}_{int(time.time())}",
                        index=index,
                        item=item,
                        cache_key=cache_key,
                        priority=priority,
                        created_at=datetime.utcnow(),
                        estimated_duration_ms=item.estimate_duration_ms(),
                        is_qa=item.type.is_qa_related
                    )
                    
                    await self._normal_queue.put(task)
                    self._processed_items.append(cache_key)  # 使用 append 而不是 add
                    self._stats.total_tasks_created += 1
                    tasks_added += 1
                    
                if tasks_added > 0:
                    logger.debug(f"添加了 {tasks_added} 个预合成任务")
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"预合成监控循环错误: {e}")
                # 错误后稍微延长等待时间
                await asyncio.sleep(self.check_interval * 2)
                
        logger.info("预合成监控循环已退出")
        
    async def _get_next_task(self) -> Optional[SynthesisTask]:
        """获取下一个任务，优先从高优先级队列获取"""
        # 先尝试从高优先级队列获取
        try:
            task = await asyncio.wait_for(
                self._high_priority_queue.get(),
                timeout=0.1  # 短超时，快速检查
            )
            logger.debug(f"获取高优先级任务: {task.item.item_id}")
            return task
        except asyncio.TimeoutError:
            pass
        
        # 再尝试从普通队列获取
        try:
            task = await asyncio.wait_for(
                self._normal_queue.get(),
                timeout=5.0  # 较长超时，等待新任务
            )
            logger.debug(f"获取普通任务: {task.item.item_id}")
            return task
        except asyncio.TimeoutError:
            return None
    
        
    async def _synthesis_worker(self, worker_id: int) -> None:
        """合成工作线程（新架构：基于队列）"""
        logger.info(f"工作线程 {worker_id} 已启动（新架构）")
        
        while self._running:
            try:
                # 从事件总线的请求队列获取任务
                request = await self.event_bus.request_queue.get()
                
                # 预检查：请求方是否已离开
                async with self.event_bus._reply_map_lock:
                    if request.request_id not in self.event_bus.reply_map:
                        logger.debug(f"⚠️ 请求方已离开 [{request.request_id}]")
                        self.event_bus.request_queue.task_done()
                        continue
                
                logger.debug(f"工作线程 {worker_id} 处理: {request.item.item_id}")
                
                # 更新活跃任务统计
                self._stats.active_tasks += 1
                
                # 执行合成
                try:
                    audio_data, duration_ms = await self._do_synthesis(request.item)
                    result = (audio_data, duration_ms)
                    logger.info(f"✅ 合成成功: {request.cache_key}")
                    self._stats.total_tasks_completed += 1
                except Exception as e:
                    result = e
                    logger.error(f"❌ 合成失败: {request.cache_key}, {e}")
                    self._stats.total_tasks_failed += 1
                
                # 向所有订阅者发送结果
                notified = await self.event_bus.send_synthesis_result(
                    request.cache_key, result
                )
                logger.debug(f"📨 已向 {notified} 个订阅者发送结果")
                
                # 标记任务完成
                self.event_bus.request_queue.task_done()
                self._stats.active_tasks -= 1
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"工作线程 {worker_id} 错误: {e}")
                self._stats.active_tasks -= 1
        
        logger.info(f"工作线程 {worker_id} 已停止")
        
    async def _do_synthesis(self, item: PlaylistItem) -> Tuple[bytes, float]:
        """
        执行实际的TTS合成（新架构）
        
        Args:
            item: 要合成的播放列表项
            
        Returns:
            (audio_data, duration_ms) 元组
            
        Raises:
            Exception: 合成失败时抛出异常
        """
        start_time = time.time()
        
        try:
            # 直接合成（缓存由引擎内部处理）
            audio_chunks = []
            
            async for chunk in self.tts_engine.synthesize_streaming(item.content):
                if chunk.audio_stream and chunk.audio_stream.audio_data:
                    audio_chunks.append(chunk.audio_stream.audio_data)
                    
            if not audio_chunks:
                raise ValueError("TTS引擎返回空音频数据")
                
            audio_data = b''.join(audio_chunks)
            duration_ms = len(audio_data) / 48  # 24kHz, 16bit估算
            
            # 计算合成时间
            synthesis_time = (time.time() - start_time) * 1000
            
            # 更新平均合成时间
            if self._stats.total_tasks_completed > 0:
                alpha = 0.1  # 平滑因子
                self._stats.average_synthesis_time_ms = (
                    (1 - alpha) * self._stats.average_synthesis_time_ms + 
                    alpha * synthesis_time
                )
            else:
                self._stats.average_synthesis_time_ms = synthesis_time
                
            logger.debug(f"完成合成: {item.item_id}, "
                        f"耗时: {synthesis_time:.1f}ms, "
                        f"大小: {len(audio_data)} bytes")
            
            return audio_data, duration_ms
            
        except Exception as e:
            synthesis_time = (time.time() - start_time) * 1000
            logger.error(f"合成失败: {item.item_id}, "
                        f"错误: {e}, 耗时: {synthesis_time:.1f}ms")
            raise
    
            
    async def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        high_priority_size = self._high_priority_queue.qsize()
        normal_size = self._normal_queue.qsize()
        queue_size = high_priority_size + normal_size
        uptime = 0.0
        
        if self._start_time:
            uptime = (datetime.utcnow() - self._start_time).total_seconds() / 60
            
        base_stats = self._stats.to_dict()
        base_stats.update({
            "is_running": self._running,
            "queue_size": queue_size,
            "max_concurrent": self.max_concurrent,
            "look_ahead_items": self.look_ahead_items,
            "worker_count": len(self._workers),
            "uptime_minutes": round(uptime, 1),
            "tasks_per_minute": round(
                self._stats.total_tasks_completed / uptime if uptime > 0 else 0, 2
            ),
            "processed_items_cache_size": len(self._processed_items),
            "monitor_task_healthy": (
                self._monitor_task is not None and 
                not self._monitor_task.done()
            ),
            "monitor_restart_count": self._monitor_restart_count,
            "guardian_task_healthy": (
                self._guardian_task is not None and 
                not self._guardian_task.done()
            )
        })
        
        return base_stats
        
    async def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        high_priority_size = self._high_priority_queue.qsize()
        normal_size = self._normal_queue.qsize()
        return {
            "queue_size": high_priority_size + normal_size,
            "high_priority_queue_size": high_priority_size,
            "normal_queue_size": normal_size,
            "active_tasks": len(self._active_tasks),
            "max_concurrent": self.max_concurrent,
            "is_queue_empty": (high_priority_size == 0 and normal_size == 0),
            "queue_based_architecture": True  # 新架构标识
        }
        
    async def clear_processed_cache(self) -> int:
        """清空已处理项目缓存"""
        cleared_count = len(self._processed_items)
        self._processed_items.clear()
        
        logger.info(f"清空已处理项目缓存: {cleared_count} 项")
        return cleared_count
        
    async def force_synthesis(self, item: PlaylistItem, priority: int = 1) -> bool:
        """
        强制合成指定项目
        
        Args:
            item: 播放项
            priority: 优先级
            
        Returns:
            是否成功加入队列
        """
        if not self._running:
            logger.warning("预合成服务未运行，无法强制合成")
            return False
            
        cache_key = item.get_cache_key()
        
        # 创建强制合成任务
        task = SynthesisTask(
            task_id=f"force_{item.item_id}_{int(time.time())}",
            index=-1,
            item=item,
            cache_key=cache_key,
            priority=priority,
            created_at=datetime.utcnow(),
            estimated_duration_ms=item.estimate_duration_ms(),
            is_qa=item.type.is_qa_related
        )
        
        await self._normal_queue.put(task)
        self._stats.total_tasks_created += 1
        
        logger.info(f"强制合成任务已加入队列: {item.item_id}")
        return True
        
    async def pause_synthesis(self) -> None:
        """暂停预合成（保留队列）"""
        logger.info("暂停预合成处理")
        
        # 取消监控任务
        if self._monitor_task and not self._monitor_task.done():
            self._monitor_task.cancel()
            
    async def resume_synthesis(self) -> None:
        """恢复预合成"""
        if not self._running:
            return
            
        logger.info("恢复预合成处理")
        
        # 重启监控任务
        self._monitor_task = asyncio.create_task(self._monitor_loop())


# 工厂函数

def create_proactive_synthesizer(content_provider: 'StreamingContentProvider',
                                client_tracker: 'ClientStateTracker',
                                playlist_manager: 'PlaylistManager',
                                config: StreamingConfig) -> ProactiveSynthesizer:
    """创建主动预合成服务实例"""
    return ProactiveSynthesizer(content_provider, client_tracker, playlist_manager, config)