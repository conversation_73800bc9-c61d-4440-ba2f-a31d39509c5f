"""Database manager for SQLite persistence

Handles SQLite database connection, schema creation, and basic operations
following CLAUDE.md fail-fast principle and logging standards.
"""

import sqlite3
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from contextlib import contextmanager
from loguru import logger

from ...core.exceptions import ServiceError, ConfigError


# Database schema constants
SCHEMA_VERSION = "1.2"
DEFAULT_DB_PATH = "data/operational_forms.db"

# SQL schema definitions
CREATE_TABLES_SQL = [
    """
    CREATE TABLE IF NOT EXISTS operational_forms (
        id TEXT PRIMARY KEY,
        created_by TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        form_version TEXT DEFAULT '1.0',
        current_section INTEGER DEFAULT 1,
        completion_percentage REAL DEFAULT 0.0,
        is_complete BOOLEAN DEFAULT FALSE,
        is_submitted BOOLEAN DEFAULT FALSE,
        is_processed BOOLEAN DEFAULT FALSE,
        processed_at TIMESTAMP NULL,
        processing_status TEXT DEFAULT 'pending'
    )
    """,
    """
    CREATE TABLE IF NOT EXISTS form_sections (
        form_id TEXT NOT NULL,
        section_number INTEGER NOT NULL,
        section_name TEXT NOT NULL,
        content_json TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (form_id, section_number),
        FOREIGN KEY (form_id) REFERENCES operational_forms(id) ON DELETE CASCADE
    )
    """,
    """
    CREATE TABLE IF NOT EXISTS form_sessions (
        session_id TEXT PRIMARY KEY,
        form_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        current_section INTEGER DEFAULT 1,
        unsaved_changes BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (form_id) REFERENCES operational_forms(id) ON DELETE CASCADE
    )
    """,
    """
    CREATE TABLE IF NOT EXISTS script_previews (
        form_id TEXT PRIMARY KEY,
        preview_data TEXT NOT NULL,
        preview_html TEXT NOT NULL,
        estimated_metrics TEXT NULL,
        warnings TEXT NULL,
        generation_time_seconds REAL DEFAULT 0.0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (form_id) REFERENCES operational_forms(id) ON DELETE CASCADE
    )
    """,
    """
    CREATE TABLE IF NOT EXISTS schema_version (
        version TEXT PRIMARY KEY,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """,
    """
    CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sku TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        description TEXT,
        price REAL,
        stock INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """,
    """
    CREATE TABLE IF NOT EXISTS product_qa (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        question TEXT NOT NULL,
        answer TEXT NOT NULL,
        category TEXT,
        tags TEXT,
        hit_count INTEGER DEFAULT 0,
        confidence_score REAL DEFAULT 0.5,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
    )
    """
]

# Indexes for performance
CREATE_INDEXES_SQL = [
    "CREATE INDEX IF NOT EXISTS idx_forms_created_by ON operational_forms(created_by)",
    "CREATE INDEX IF NOT EXISTS idx_forms_status ON operational_forms(processing_status)",
    "CREATE INDEX IF NOT EXISTS idx_sessions_form_id ON form_sessions(form_id)",
    "CREATE INDEX IF NOT EXISTS idx_sessions_expires ON form_sessions(expires_at)",
    "CREATE INDEX IF NOT EXISTS idx_sections_form_id ON form_sections(form_id)",
    "CREATE INDEX IF NOT EXISTS idx_script_previews_created_at ON script_previews(created_at)",
    "CREATE INDEX IF NOT EXISTS idx_script_previews_updated_at ON script_previews(updated_at)",
    "CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku)",
    "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)",
    "CREATE INDEX IF NOT EXISTS idx_product_qa_product_id ON product_qa(product_id)",
    "CREATE INDEX IF NOT EXISTS idx_product_qa_category ON product_qa(category)"
]


class DatabaseManager:
    """SQLite database manager with transaction support and error handling"""
    
    def __init__(self, db_path: Optional[str] = None) -> None:
        """Initialize database manager
        
        Args:
            db_path: Path to SQLite database file, defaults to DEFAULT_DB_PATH
            
        Raises:
            ConfigError: If database path is invalid or inaccessible
        """
        self.db_path = db_path or DEFAULT_DB_PATH
        self._connection: Optional[sqlite3.Connection] = None
        
        # Ensure database directory exists
        db_dir = Path(self.db_path).parent
        if not db_dir.exists():
            try:
                db_dir.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created database directory: {db_dir}")
            except OSError as e:
                raise ConfigError(f"Failed to create database directory {db_dir}: {e}")
        
        # Test database accessibility
        try:
            self._test_database_access()
        except Exception as e:
            raise ConfigError(f"Database not accessible at {self.db_path}: {e}")
        
        logger.info(f"Database manager initialized with path: {self.db_path}")
    
    def _test_database_access(self) -> None:
        """Test database file accessibility"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("SELECT 1")
        except sqlite3.Error as e:
            raise ServiceError(f"Database access test failed: {e}", "database_manager")
    
    @contextmanager
    def get_connection(self):
        """Get database connection with automatic transaction management"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable named column access
            conn.execute("PRAGMA foreign_keys = ON")  # Enable foreign key constraints
            yield conn
            conn.commit()
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database operation failed: {e}")
            raise ServiceError(f"Database operation failed: {e}", "database_manager")
        finally:
            if conn:
                conn.close()
    
    def initialize_schema(self) -> None:
        """Initialize database schema with tables and indexes (idempotent)
        
        Raises:
            ServiceError: If schema initialization fails
        """
        # Check if already initialized to avoid duplicate operations
        if hasattr(self, '_schema_initialized') and self._schema_initialized:
            logger.debug("Database schema already initialized, skipping duplicate initialization")
            return
            
        try:
            with self.get_connection() as conn:
                # Create tables
                for table_sql in CREATE_TABLES_SQL:
                    conn.execute(table_sql)
                
                # Create indexes
                for index_sql in CREATE_INDEXES_SQL:
                    conn.execute(index_sql)
                
                # Record schema version
                conn.execute(
                    "INSERT OR REPLACE INTO schema_version (version) VALUES (?)",
                    (SCHEMA_VERSION,)
                )
                
            logger.info("Database schema initialized successfully (first time)")
            self._schema_initialized = True
            
        except Exception as e:
            raise ServiceError(f"Schema initialization failed: {e}", "database_manager")
    
    def get_schema_version(self) -> Optional[str]:
        """Get current database schema version
        
        Returns:
            Schema version string or None if not set
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("SELECT version FROM schema_version ORDER BY applied_at DESC LIMIT 1")
                row = cursor.fetchone()
                return row["version"] if row else None
        except sqlite3.OperationalError:
            # Schema version table doesn't exist yet
            return None
        except Exception as e:
            logger.error(f"Failed to get schema version: {e}")
            raise ServiceError(f"Failed to get schema version: {e}", "database_manager")
    
    def execute_query(self, sql: str, params: Tuple = ()) -> List[sqlite3.Row]:
        """Execute SELECT query and return results
        
        Args:
            sql: SQL query string
            params: Query parameters tuple
            
        Returns:
            List of result rows
            
        Raises:
            ServiceError: If query execution fails
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Query execution failed: {sql}, params: {params}, error: {e}")
            raise ServiceError(f"Query execution failed: {e}", "database_manager")
    
    def execute_update(self, sql: str, params: Tuple = ()) -> int:
        """Execute INSERT/UPDATE/DELETE query
        
        Args:
            sql: SQL query string
            params: Query parameters tuple
            
        Returns:
            Number of affected rows
            
        Raises:
            ServiceError: If query execution fails
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(sql, params)
                affected_rows = cursor.rowcount
                logger.debug(f"Query executed successfully, affected rows: {affected_rows}")
                return affected_rows
        except Exception as e:
            logger.error(f"Update execution failed: {sql}, params: {params}, error: {e}")
            raise ServiceError(f"Update execution failed: {e}", "database_manager")
    
    def execute_batch(self, sql: str, params_list: List[Tuple]) -> int:
        """Execute batch INSERT/UPDATE operations
        
        Args:
            sql: SQL query string
            params_list: List of parameter tuples
            
        Returns:
            Total number of affected rows
            
        Raises:
            ServiceError: If batch execution fails
        """
        if not params_list:
            return 0
        
        try:
            with self.get_connection() as conn:
                cursor = conn.executemany(sql, params_list)
                affected_rows = cursor.rowcount
                logger.debug(f"Batch query executed, total affected rows: {affected_rows}")
                return affected_rows
        except Exception as e:
            logger.error(f"Batch execution failed: {sql}, error: {e}")
            raise ServiceError(f"Batch execution failed: {e}", "database_manager")
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """Get table schema information
        
        Args:
            table_name: Name of the table
            
        Returns:
            List of column information dictionaries
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                return [dict(row) for row in columns]
        except Exception as e:
            logger.error(f"Failed to get table info for {table_name}: {e}")
            raise ServiceError(f"Failed to get table info: {e}", "database_manager")
    
    def vacuum_database(self) -> None:
        """Optimize database by running VACUUM command
        
        Raises:
            ServiceError: If vacuum operation fails
        """
        try:
            # VACUUM cannot be run inside a transaction
            conn = sqlite3.connect(self.db_path)
            conn.execute("VACUUM")
            conn.close()
            logger.info("Database vacuum completed successfully")
        except Exception as e:
            logger.error(f"Database vacuum failed: {e}")
            raise ServiceError(f"Database vacuum failed: {e}", "database_manager")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database usage statistics
        
        Returns:
            Dictionary with database statistics
        """
        try:
            with self.get_connection() as conn:
                # Get table row counts
                stats = {}
                tables = ["operational_forms", "form_sections", "form_sessions", "script_previews"]
                
                for table in tables:
                    cursor = conn.execute(f"SELECT COUNT(*) as count FROM {table}")
                    row = cursor.fetchone()
                    stats[f"{table}_count"] = row["count"]
                
                # Get database file size
                db_file = Path(self.db_path)
                if db_file.exists():
                    stats["database_size_bytes"] = db_file.stat().st_size
                else:
                    stats["database_size_bytes"] = 0
                
                # Get schema version
                stats["schema_version"] = self.get_schema_version()
                
                return stats
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            raise ServiceError(f"Failed to get database stats: {e}", "database_manager")
    
    def close(self) -> None:
        """Close database connection if open"""
        if self._connection:
            try:
                self._connection.close()
                self._connection = None
                logger.debug("Database connection closed")
            except Exception as e:
                logger.warning(f"Error closing database connection: {e}")
    
    def __del__(self) -> None:
        """Cleanup database connection on object destruction"""
        self.close()