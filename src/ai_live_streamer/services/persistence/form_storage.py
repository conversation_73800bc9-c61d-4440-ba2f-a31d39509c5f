"""Form storage service for operational forms persistence

Provides high-level interface for storing and retrieving operational forms
using SQLite backend with JSON serialization for complex form data.
"""

import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from loguru import logger

from .database_manager import DatabaseManager
from ...models.forms import OperationalForm, BasicInformation, ProductInformation, SellingPointsStructure, PersonaConfiguration, AdvancedSettings, ReviewAndValidation
from ...core.exceptions import ServiceError, ValidationError
from ...utils.validation_error_parser import ValidationErrorParser
from pydantic import ValidationError as PydanticValidationError
from decimal import Decimal, InvalidOperation


# Section name mapping for database storage
SECTION_NAMES = {
    1: "basic_information",
    2: "product_information", 
    3: "selling_points_structure",
    4: "persona_configuration",
    5: "advanced_settings",
    6: "review_and_validation"
}

# Section model mapping for deserialization
SECTION_MODELS = {
    1: BasicInformation,
    2: ProductInformation,
    3: SellingPointsStructure,
    4: PersonaConfiguration,
    5: AdvancedSettings,
    6: ReviewAndValidation
}


class FormStorage:
    """High-level interface for operational form persistence"""
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None) -> None:
        """Initialize form storage service
        
        Args:
            db_manager: Database manager instance, creates new one if None
        """
        self.db_manager = db_manager or DatabaseManager()
        logger.info("Form storage service initialized")
    
    def create_form(self, form: OperationalForm) -> str:
        """Create new operational form in database
        
        Args:
            form: OperationalForm instance to store
            
        Returns:
            Form ID of created form
            
        Raises:
            ServiceError: If form creation fails
            ValidationError: If form data is invalid
        """
        try:
            # Extract product association info from form
            selected_product_id = None
            product_price_config = None
            product_associated_at = None
            
            if hasattr(form.product_information, 'selected_product_id') and form.product_information.selected_product_id:
                selected_product_id = form.product_information.selected_product_id
                
                # Build price configuration
                price_config = {
                    "use_custom_price": getattr(form.product_information, 'use_custom_price', False),
                    "custom_streaming_price": getattr(form.product_information, 'custom_streaming_price', None)
                }
                product_price_config = json.dumps(price_config)
                product_associated_at = datetime.utcnow().isoformat()
            
            # Insert main form record with product association
            form_sql = """
                INSERT INTO operational_forms (
                    id, created_by, created_at, last_modified_at, form_version,
                    current_section, completion_percentage, is_complete,
                    is_submitted, is_processed, processed_at, processing_status,
                    selected_product_id, product_price_config, product_associated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            form_params = (
                form.form_id,
                form.created_by,
                form.created_at.isoformat(),
                form.last_modified_at.isoformat(),
                form.form_version,
                form.current_section,
                form.completion_percentage,
                form.is_complete,
                form.is_submitted,
                form.is_processed,
                form.processed_at.isoformat() if form.processed_at else None,
                form.processing_status,
                selected_product_id,
                product_price_config,
                product_associated_at
            )
            
            affected_rows = self.db_manager.execute_update(form_sql, form_params)
            if affected_rows != 1:
                raise ServiceError(f"Failed to create form, affected rows: {affected_rows}")
            
            # Insert form sections
            self._store_form_sections(form)
            
            logger.info(f"Created form {form.form_id} successfully")
            return form.form_id
            
        except Exception as e:
            logger.error(f"Failed to create form {form.form_id}: {e}")
            raise ServiceError(f"Failed to create form: {e}", "form_storage")
    
    def get_form(self, form_id: str) -> Optional[OperationalForm]:
        """Retrieve operational form by ID
        
        Args:
            form_id: Unique form identifier
            
        Returns:
            OperationalForm instance or None if not found
            
        Raises:
            ServiceError: If database operation fails
        """
        try:
            # Get main form record
            form_sql = "SELECT * FROM operational_forms WHERE id = ?"
            form_rows = self.db_manager.execute_query(form_sql, (form_id,))
            
            if not form_rows:
                return None
            
            form_row = form_rows[0]
            
            # Get form sections
            sections_data = self._load_form_sections(form_id)
            
            # Reconstruct form object
            form = self._reconstruct_form(dict(form_row), sections_data)
            
            logger.debug(f"Retrieved form {form_id} successfully")
            return form
            
        except Exception as e:
            logger.error(f"Failed to get form {form_id}: {e}")
            raise ServiceError(f"Failed to get form: {e}", "form_storage")
    
    def update_form(self, form: OperationalForm) -> None:
        """Update existing operational form
        
        Args:
            form: Updated OperationalForm instance
            
        Raises:
            ServiceError: If update operation fails
        """
        try:
            # Extract product association info for update
            selected_product_id = None
            product_price_config = None
            product_associated_at = None
            
            if hasattr(form.product_information, 'selected_product_id') and form.product_information.selected_product_id:
                selected_product_id = form.product_information.selected_product_id
                
                # Build price configuration
                price_config = {
                    "use_custom_price": getattr(form.product_information, 'use_custom_price', False),
                    "custom_streaming_price": getattr(form.product_information, 'custom_streaming_price', None)
                }
                product_price_config = json.dumps(price_config)
                
                # Only update association timestamp if product changed
                current_product = self.db_manager.execute_query(
                    "SELECT selected_product_id FROM operational_forms WHERE id = ?",
                    (form.form_id,)
                )
                if not current_product or current_product[0][0] != selected_product_id:
                    product_associated_at = datetime.utcnow().isoformat()
            
            # Update main form record with product association
            form_sql = """
                UPDATE operational_forms SET
                    last_modified_at = ?, current_section = ?, completion_percentage = ?,
                    is_complete = ?, is_submitted = ?, is_processed = ?,
                    processed_at = ?, processing_status = ?,
                    selected_product_id = ?, product_price_config = ?, 
                    product_associated_at = COALESCE(?, product_associated_at)
                WHERE id = ?
            """
            
            form_params = (
                form.last_modified_at.isoformat(),
                form.current_section,
                form.completion_percentage,
                form.is_complete,
                form.is_submitted,
                form.is_processed,
                form.processed_at.isoformat() if form.processed_at else None,
                form.processing_status,
                selected_product_id,
                product_price_config,
                product_associated_at,
                form.form_id
            )
            
            affected_rows = self.db_manager.execute_update(form_sql, form_params)
            if affected_rows != 1:
                raise ServiceError(f"Form not found or update failed: {form.form_id}")
            
            # Update form sections
            self._update_form_sections(form)
            
            logger.info(f"Updated form {form.form_id} successfully")
            
        except Exception as e:
            logger.error(f"Failed to update form {form.form_id}: {e}")
            raise ServiceError(f"Failed to update form: {e}", "form_storage")
    
    def delete_form(self, form_id: str) -> bool:
        """Delete operational form and all associated data
        
        Args:
            form_id: Form ID to delete
            
        Returns:
            True if form was deleted, False if form not found
            
        Raises:
            ServiceError: If delete operation fails
        """
        try:
            # Delete form (cascade will delete sections and sessions)
            form_sql = "DELETE FROM operational_forms WHERE id = ?"
            affected_rows = self.db_manager.execute_update(form_sql, (form_id,))
            
            if affected_rows == 0:
                return False
            
            logger.info(f"Deleted form {form_id} and associated data")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete form {form_id}: {e}")
            raise ServiceError(f"Failed to delete form: {e}", "form_storage")
    
    def list_forms(self, created_by: Optional[str] = None, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """List operational forms with optional filtering
        
        Args:
            created_by: Filter by form creator, None for all forms
            limit: Maximum number of forms to return
            offset: Number of forms to skip
            
        Returns:
            List of form summary dictionaries
            
        Raises:
            ServiceError: If query fails
        """
        try:
            base_sql = """
                SELECT id, created_by, created_at, last_modified_at, current_section,
                       completion_percentage, is_complete, is_submitted, is_processed,
                       processing_status
                FROM operational_forms
            """
            
            params = []
            if created_by:
                base_sql += " WHERE created_by = ?"
                params.append(created_by)
            
            base_sql += " ORDER BY last_modified_at DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            rows = self.db_manager.execute_query(base_sql, tuple(params))
            
            forms = []
            for row in rows:
                form_dict = dict(row)
                # Parse datetime strings
                form_dict['created_at'] = datetime.fromisoformat(form_dict['created_at'])
                form_dict['last_modified_at'] = datetime.fromisoformat(form_dict['last_modified_at'])
                forms.append(form_dict)
            
            logger.debug(f"Listed {len(forms)} forms")
            return forms
            
        except Exception as e:
            logger.error(f"Failed to list forms: {e}")
            raise ServiceError(f"Failed to list forms: {e}", "form_storage")
    
    def update_form_section(self, form_id: str, section_number: int, section_data: Dict[str, Any]) -> None:
        """Update specific form section with atomic transaction and conflict detection
        
        Args:
            form_id: Form ID to update
            section_number: Section number (1-6)
            section_data: Section data dictionary
            
        Raises:
            ServiceError: If update fails
            ValidationError: If section data is invalid
        """
        if section_number < 1 or section_number > 6:
            raise ValidationError("Section number must be between 1 and 6")
        
        try:
            # Preprocess section data to handle type conversions
            preprocessed_data = self._preprocess_section_data(section_number, section_data)

            # Validate section data by creating model instance
            section_model = SECTION_MODELS[section_number]
            logger.info(f"Validating form {form_id} section {section_number} with data keys: {list(preprocessed_data.keys())}")
            logger.debug(f"Original section data: {json.dumps(section_data, indent=2, default=str)}")
            logger.debug(f"Preprocessed section data: {json.dumps(preprocessed_data, indent=2, default=str)}")
            validated_section = section_model(**preprocessed_data)
            
            # Use single atomic transaction for both operations
            with self.db_manager.get_connection() as conn:
                current_time = datetime.utcnow().isoformat()
                
                # Check if form exists and get current modification time for conflict detection
                form_check_sql = "SELECT last_modified_at FROM operational_forms WHERE id = ?"
                cursor = conn.execute(form_check_sql, (form_id,))
                form_row = cursor.fetchone()
                
                if not form_row:
                    raise ServiceError(f"Form {form_id} not found")
                
                # Store section with atomic transaction
                section_sql = """
                    INSERT OR REPLACE INTO form_sections 
                    (form_id, section_number, section_name, content_json, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """
                
                section_params = (
                    form_id,
                    section_number,
                    SECTION_NAMES[section_number],
                    json.dumps(validated_section.dict(), default=self._json_serializer),
                    current_time
                )
                
                section_cursor = conn.execute(section_sql, section_params)
                if section_cursor.rowcount == 0:
                    raise ServiceError("Failed to update form section")
                
                # Update form's last modified time in same transaction
                form_sql = "UPDATE operational_forms SET last_modified_at = ? WHERE id = ?"
                form_cursor = conn.execute(form_sql, (current_time, form_id))
                if form_cursor.rowcount == 0:
                    raise ServiceError("Failed to update form modification time")
                
                logger.info(f"Atomically updated form {form_id} section {section_number}")
            
        except PydanticValidationError as pve:
            # Parse Pydantic validation error into structured format
            parsed_errors = ValidationErrorParser.parse_pydantic_error(pve)
            error_message = ValidationErrorParser.format_errors_for_display(parsed_errors)

            # Log detailed validation error
            logger.error(f"Pydantic validation failed for form {form_id}, section {section_number}")
            logger.error(f"Parsed errors: {json.dumps(parsed_errors, indent=2, ensure_ascii=False)}")
            logger.error(f"Failed section data: {json.dumps(section_data, indent=2, default=str)}")

            # Raise custom ValidationError with structured information
            raise ValidationError(
                message=error_message,
                field_name=None,
                invalid_value=section_data,
                context={
                    'parsed_errors': parsed_errors,
                    'section_number': section_number,
                    'form_id': form_id
                }
            )
        except ValidationError:
            # Re-raise our custom validation errors
            raise
        except Exception as e:
            logger.error(f"Failed to update form section {form_id}/{section_number}: {e}")
            logger.error(f"Section data that caused error: {json.dumps(section_data, indent=2, default=str)}")
            raise ServiceError(f"Failed to update form section: {e}", "form_storage")
    
    def get_form_stats(self) -> Dict[str, Any]:
        """Get form storage statistics
        
        Returns:
            Dictionary with form statistics
        """
        try:
            stats_sql = """
                SELECT 
                    COUNT(*) as total_forms,
                    COUNT(CASE WHEN is_complete = 1 THEN 1 END) as complete_forms,
                    COUNT(CASE WHEN is_submitted = 1 THEN 1 END) as submitted_forms,
                    COUNT(CASE WHEN is_processed = 1 THEN 1 END) as processed_forms,
                    AVG(completion_percentage) as avg_completion
                FROM operational_forms
            """
            
            rows = self.db_manager.execute_query(stats_sql)
            stats = dict(rows[0]) if rows else {}
            
            # Add database stats
            db_stats = self.db_manager.get_database_stats()
            stats.update(db_stats)
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get form stats: {e}")
            raise ServiceError(f"Failed to get form stats: {e}", "form_storage")
    
    def _store_form_sections(self, form: OperationalForm) -> None:
        """Store all form sections as JSON data"""
        sections = [
            (1, form.basic_information),
            (2, form.product_information),
            (3, form.selling_points_structure),
            (4, form.persona_configuration),
            (5, form.advanced_settings),
            (6, form.review_and_validation)
        ]
        
        section_sql = """
            INSERT INTO form_sections (form_id, section_number, section_name, content_json, updated_at)
            VALUES (?, ?, ?, ?, ?)
        """
        
        section_params = []
        for section_num, section_obj in sections:
            section_params.append((
                form.form_id,
                section_num,
                SECTION_NAMES[section_num],
                json.dumps(section_obj.dict(), default=self._json_serializer),
                datetime.utcnow().isoformat()
            ))
        
        self.db_manager.execute_batch(section_sql, section_params)
    
    def _update_form_sections(self, form: OperationalForm) -> None:
        """Update all form sections"""
        # Delete existing sections
        delete_sql = "DELETE FROM form_sections WHERE form_id = ?"
        self.db_manager.execute_update(delete_sql, (form.form_id,))
        
        # Insert updated sections
        self._store_form_sections(form)
    
    def _load_form_sections(self, form_id: str) -> Dict[int, Dict[str, Any]]:
        """Load form sections data from database"""
        sections_sql = "SELECT section_number, content_json FROM form_sections WHERE form_id = ?"
        rows = self.db_manager.execute_query(sections_sql, (form_id,))
        
        sections_data = {}
        for row in rows:
            section_number = row["section_number"]
            content_json = row["content_json"]
            sections_data[section_number] = json.loads(content_json)
        
        return sections_data
    
    def _reconstruct_form(self, form_data: Dict[str, Any], sections_data: Dict[int, Dict[str, Any]]) -> OperationalForm:
        """Reconstruct OperationalForm object from database data"""
        # Parse datetime fields
        form_data['created_at'] = datetime.fromisoformat(form_data['created_at'])
        form_data['last_modified_at'] = datetime.fromisoformat(form_data['last_modified_at'])
        
        if form_data['processed_at']:
            form_data['processed_at'] = datetime.fromisoformat(form_data['processed_at'])
        
        # Reconstruct sections
        section_objects = {}
        for section_num, section_model in SECTION_MODELS.items():
            if section_num in sections_data:
                section_objects[SECTION_NAMES[section_num]] = section_model(**sections_data[section_num])
            else:
                # Create default section if missing
                section_objects[SECTION_NAMES[section_num]] = section_model()
        
        # Create form object
        form_dict = {
            'form_id': form_data['id'],
            'created_by': form_data['created_by'],
            'created_at': form_data['created_at'],
            'last_modified_at': form_data['last_modified_at'],
            'form_version': form_data['form_version'],
            'current_section': form_data['current_section'],
            'completion_percentage': form_data['completion_percentage'],
            'is_complete': bool(form_data['is_complete']),
            'is_submitted': bool(form_data['is_submitted']),
            'is_processed': bool(form_data['is_processed']),
            'processed_at': form_data['processed_at'],
            'processing_status': form_data['processing_status'],
            **section_objects
        }
        
        return OperationalForm(**form_dict)
    
    def _json_serializer(self, obj: Any) -> str:
        """Custom JSON serializer for special types"""
        if hasattr(obj, 'value'):  # Enum types
            return obj.value
        return str(obj)

    def _preprocess_section_data(self, section_number: int, section_data: Dict[str, Any]) -> Dict[str, Any]:
        """Preprocess section data to handle type conversions and format issues

        Args:
            section_number: Section number (1-6)
            section_data: Raw section data from frontend

        Returns:
            Preprocessed section data with correct types
        """
        processed_data = section_data.copy()

        try:
            if section_number == 2:  # Product Information
                # Handle price fields - convert strings to Decimal
                for price_field in ['current_price', 'original_price']:
                    if price_field in processed_data and processed_data[price_field] is not None:
                        value = processed_data[price_field]
                        if isinstance(value, str):
                            if value.strip() == '':
                                processed_data[price_field] = None
                            else:
                                try:
                                    processed_data[price_field] = Decimal(value.strip())
                                except (InvalidOperation, ValueError) as e:
                                    logger.warning(f"Invalid price value '{value}' for {price_field}: {e}")
                                    # Keep original value to let Pydantic handle the error
                        elif isinstance(value, (int, float)):
                            processed_data[price_field] = Decimal(str(value))

                # Handle key_specifications - ensure it's a list
                if 'key_specifications' in processed_data:
                    specs = processed_data['key_specifications']
                    if isinstance(specs, str):
                        # Split by newlines or commas, filter empty strings
                        if '\n' in specs:
                            processed_data['key_specifications'] = [s.strip() for s in specs.split('\n') if s.strip()]
                        elif ',' in specs:
                            processed_data['key_specifications'] = [s.strip() for s in specs.split(',') if s.strip()]
                        else:
                            processed_data['key_specifications'] = [specs.strip()] if specs.strip() else []
                    elif not isinstance(specs, list):
                        processed_data['key_specifications'] = []

                # Handle related_skus - ensure it's a list
                if 'related_skus' in processed_data:
                    skus = processed_data['related_skus']
                    if isinstance(skus, str):
                        if '\n' in skus:
                            processed_data['related_skus'] = [s.strip() for s in skus.split('\n') if s.strip()]
                        elif ',' in skus:
                            processed_data['related_skus'] = [s.strip() for s in skus.split(',') if s.strip()]
                        else:
                            processed_data['related_skus'] = [skus.strip()] if skus.strip() else []
                    elif not isinstance(skus, list):
                        processed_data['related_skus'] = []

            elif section_number == 3:  # Selling Points Structure
                # Handle call_to_actions - ensure it's a list
                if 'call_to_actions' in processed_data:
                    ctas = processed_data['call_to_actions']
                    if isinstance(ctas, str):
                        if '\n' in ctas:
                            processed_data['call_to_actions'] = [s.strip() for s in ctas.split('\n') if s.strip()]
                        elif ',' in ctas:
                            processed_data['call_to_actions'] = [s.strip() for s in ctas.split(',') if s.strip()]
                        else:
                            processed_data['call_to_actions'] = [ctas.strip()] if ctas.strip() else []
                    elif not isinstance(ctas, list):
                        processed_data['call_to_actions'] = []

            logger.debug(f"Preprocessed section {section_number} data successfully")
            return processed_data

        except Exception as e:
            logger.error(f"Error preprocessing section {section_number} data: {e}")
            # Return original data if preprocessing fails
            return section_data
    
    # Script Preview Persistence Methods
    
    def create_script_preview(self, form_id: str, preview_data: Dict[str, Any], 
                             preview_html: str, estimated_metrics: Optional[Dict[str, Any]] = None,
                             warnings: Optional[List[str]] = None, 
                             generation_time_seconds: float = 0.0) -> None:
        """Create or update script preview for a form
        
        Args:
            form_id: Form ID to associate with the preview
            preview_data: Serialized script timeline and preview result data
            preview_html: Generated HTML content for the preview
            estimated_metrics: Optional metrics data
            warnings: Optional list of warnings
            generation_time_seconds: Time taken to generate the preview
            
        Raises:
            ServiceError: If script preview creation fails
        """
        try:
            script_sql = """
                INSERT OR REPLACE INTO script_previews (
                    form_id, preview_data, preview_html, estimated_metrics,
                    warnings, generation_time_seconds, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            current_time = datetime.utcnow().isoformat()
            
            script_params = (
                form_id,
                json.dumps(preview_data, default=self._json_serializer),
                preview_html,
                json.dumps(estimated_metrics, default=self._json_serializer) if estimated_metrics else None,
                json.dumps(warnings) if warnings else None,
                generation_time_seconds,
                current_time,
                current_time
            )
            
            affected_rows = self.db_manager.execute_update(script_sql, script_params)
            if affected_rows == 0:
                raise ServiceError(f"Failed to create script preview for form {form_id}")
            
            logger.info(f"Created/updated script preview for form {form_id}")
            
        except Exception as e:
            logger.error(f"Failed to create script preview for form {form_id}: {e}")
            raise ServiceError(f"Failed to create script preview: {e}", "form_storage")
    
    def get_script_preview(self, form_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve script preview data for a form
        
        Args:
            form_id: Form ID to get preview for
            
        Returns:
            Dictionary with preview data or None if not found
            
        Raises:
            ServiceError: If database operation fails
        """
        try:
            preview_sql = """
                SELECT preview_data, preview_html, estimated_metrics, warnings,
                       generation_time_seconds, created_at, updated_at
                FROM script_previews
                WHERE form_id = ?
            """
            
            rows = self.db_manager.execute_query(preview_sql, (form_id,))
            
            if not rows:
                return None
            
            row = rows[0]
            
            # Parse JSON fields
            preview_data = json.loads(row["preview_data"]) if row["preview_data"] else {}
            estimated_metrics = json.loads(row["estimated_metrics"]) if row["estimated_metrics"] else None
            warnings = json.loads(row["warnings"]) if row["warnings"] else None
            
            result = {
                "form_id": form_id,
                "preview_data": preview_data,
                "preview_html": row["preview_html"],
                "estimated_metrics": estimated_metrics,
                "warnings": warnings,
                "generation_time_seconds": row["generation_time_seconds"],
                "created_at": datetime.fromisoformat(row["created_at"]),
                "updated_at": datetime.fromisoformat(row["updated_at"])
            }
            
            logger.debug(f"Retrieved script preview for form {form_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to get script preview for form {form_id}: {e}")
            raise ServiceError(f"Failed to get script preview: {e}", "form_storage")
    
    def delete_script_preview(self, form_id: str) -> bool:
        """Delete script preview for a form
        
        Args:
            form_id: Form ID to delete preview for
            
        Returns:
            True if preview was deleted, False if not found
            
        Raises:
            ServiceError: If delete operation fails
        """
        try:
            preview_sql = "DELETE FROM script_previews WHERE form_id = ?"
            affected_rows = self.db_manager.execute_update(preview_sql, (form_id,))
            
            if affected_rows > 0:
                logger.info(f"Deleted script preview for form {form_id}")
                return True
            else:
                return False
            
        except Exception as e:
            logger.error(f"Failed to delete script preview for form {form_id}: {e}")
            raise ServiceError(f"Failed to delete script preview: {e}", "form_storage")
    
    def list_script_previews(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """List all script previews with summary information
        
        Args:
            limit: Maximum number of previews to return
            offset: Number of previews to skip
            
        Returns:
            List of script preview summary dictionaries
            
        Raises:
            ServiceError: If query fails
        """
        try:
            preview_sql = """
                SELECT sp.form_id, sp.generation_time_seconds, sp.created_at, sp.updated_at,
                       of.created_by
                FROM script_previews sp
                LEFT JOIN operational_forms of ON sp.form_id = of.id
                ORDER BY sp.updated_at DESC
                LIMIT ? OFFSET ?
            """
            
            rows = self.db_manager.execute_query(preview_sql, (limit, offset))
            
            previews = []
            for row in rows:
                preview_dict = {
                    "form_id": row["form_id"], 
                    "created_by": row["created_by"],
                    "generation_time_seconds": row["generation_time_seconds"],
                    "created_at": datetime.fromisoformat(row["created_at"]),
                    "updated_at": datetime.fromisoformat(row["updated_at"])
                }
                previews.append(preview_dict)
            
            logger.debug(f"Listed {len(previews)} script previews")
            return previews
            
        except Exception as e:
            logger.error(f"Failed to list script previews: {e}")
            raise ServiceError(f"Failed to list script previews: {e}", "form_storage")
    
    def get_script_preview_stats(self) -> Dict[str, Any]:
        """Get script preview statistics
        
        Returns:
            Dictionary with script preview statistics
        """
        try:
            stats_sql = """
                SELECT 
                    COUNT(*) as total_previews,
                    AVG(generation_time_seconds) as avg_generation_time,
                    MAX(generation_time_seconds) as max_generation_time,
                    MIN(generation_time_seconds) as min_generation_time
                FROM script_previews
            """
            
            rows = self.db_manager.execute_query(stats_sql)
            stats = dict(rows[0]) if rows else {}
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get script preview stats: {e}")
            raise ServiceError(f"Failed to get script preview stats: {e}", "form_storage")
    
    def delete_all_script_previews(self) -> int:
        """Delete all script previews from database
        
        Returns:
            Number of previews deleted
            
        Raises:
            ServiceError: If delete operation fails
        """
        try:
            preview_sql = "DELETE FROM script_previews"
            affected_rows = self.db_manager.execute_update(preview_sql)
            
            logger.info(f"Deleted all script previews ({affected_rows} entries)")
            return affected_rows
            
        except Exception as e:
            logger.error(f"Failed to delete all script previews: {e}")
            raise ServiceError(f"Failed to delete all script previews: {e}", "form_storage")