"""
Script Persistence System Setup and Integration

One-stop setup script for integrating the script persistence system
with existing AI Live Streamer infrastructure.
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
from loguru import logger

from .script_persistence_manager import ScriptPersistenceManager
from .script_preview_observer import (
    ScriptPreviewObserver, GlobalScriptObserver, 
    setup_script_persistence, add_persistence_to_previewer,
    migrate_existing_cache_to_persistence
)
from .script_persistence_config import (
    ConfigurationManager, get_config_manager, 
    create_default_config_file, ScriptPersistenceSystemConfig
)
from ..database.migrations import initialize_persistence_database
from ..models.script_persistence import PersistenceConfig
from ..core.config import cfg


class ScriptPersistenceSetup:
    """
    Complete setup and integration manager for the script persistence system
    """
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_manager = ConfigurationManager(config_file)
        self.setup_steps_completed: List[str] = []
        self.setup_errors: List[str] = []
        
        logger.info("Script Persistence Setup initialized")
    
    async def run_complete_setup(
        self,
        migrate_existing_cache: bool = True,
        create_config_file: bool = True,
        validate_setup: bool = True
    ) -> Dict[str, Any]:
        """
        Run complete setup process for script persistence system
        
        Args:
            migrate_existing_cache: Whether to migrate existing preview cache
            create_config_file: Whether to create default config file
            validate_setup: Whether to validate setup after completion
            
        Returns:
            Setup results dictionary
        """
        logger.info("Starting complete script persistence setup")
        setup_start_time = datetime.now()
        
        try:
            # Step 1: Create configuration file if requested
            if create_config_file:
                await self._step_create_config_file()
            
            # Step 2: Initialize database
            await self._step_initialize_database()
            
            # Step 3: Setup persistence manager
            await self._step_setup_persistence_manager()
            
            # Step 4: Setup observer system
            await self._step_setup_observer_system()
            
            # Step 5: Migrate existing cache if requested
            if migrate_existing_cache:
                await self._step_migrate_existing_cache()
            
            # Step 6: Initialize analytics
            await self._step_initialize_analytics()
            
            # Step 7: Setup API endpoints
            await self._step_setup_api_endpoints()
            
            # Step 8: Validate setup if requested
            if validate_setup:
                await self._step_validate_setup()
            
            # Generate setup report
            setup_report = self._generate_setup_report()
            
            if len(self.setup_errors) == 0:
                logger.success("Script persistence setup completed successfully")
                setup_report["status"] = "success"
            else:
                logger.warning(f"Setup completed with {len(self.setup_errors)} warnings")
                setup_report["status"] = "completed_with_warnings"
            
            return setup_report
            
        except Exception as e:
            logger.error(f"Script persistence setup failed: {e}")
            self.setup_errors.append(f"Setup failed: {str(e)}")
            
            return {
                "status": "failed",
                "error": str(e),
                "completed_steps": self.setup_steps_completed,
                "errors": self.setup_errors
            }
    
    async def _step_create_config_file(self) -> None:
        """Step 1: Create default configuration file"""
        try:
            logger.info("Step 1: Creating configuration file")
            
            config_path = "config/script_persistence.yml"
            success = create_default_config_file(config_path)
            
            if success:
                self.setup_steps_completed.append("create_config_file")
                logger.info(f"✓ Configuration file created: {config_path}")
            else:
                self.setup_errors.append("Failed to create configuration file")
                logger.warning("⚠ Failed to create configuration file")
                
        except Exception as e:
            self.setup_errors.append(f"Config file creation error: {str(e)}")
            logger.error(f"Step 1 failed: {e}")
    
    async def _step_initialize_database(self) -> None:
        """Step 2: Initialize persistence database"""
        try:
            logger.info("Step 2: Initializing persistence database")
            
            config = self.config_manager.get_persistence_config()
            success = initialize_persistence_database(config)
            
            if success:
                self.setup_steps_completed.append("initialize_database")
                logger.info(f"✓ Database initialized: {config.database_path}")
            else:
                self.setup_errors.append("Database initialization failed")
                logger.error("✗ Database initialization failed")
                raise Exception("Database initialization failed")
                
        except Exception as e:
            self.setup_errors.append(f"Database initialization error: {str(e)}")
            logger.error(f"Step 2 failed: {e}")
            raise
    
    async def _step_setup_persistence_manager(self) -> None:
        """Step 3: Setup persistence manager"""
        try:
            logger.info("Step 3: Setting up persistence manager")
            
            config = self.config_manager.get_persistence_config()
            persistence_manager = ScriptPersistenceManager(config)
            
            # Test persistence manager
            stats = persistence_manager.get_manager_stats()
            
            self.setup_steps_completed.append("setup_persistence_manager")
            logger.info(f"✓ Persistence manager initialized: {stats['config']['database_path']}")
            
        except Exception as e:
            self.setup_errors.append(f"Persistence manager setup error: {str(e)}")
            logger.error(f"Step 3 failed: {e}")
            raise
    
    async def _step_setup_observer_system(self) -> None:
        """Step 4: Setup observer system"""
        try:
            logger.info("Step 4: Setting up observer system")
            
            # Initialize global observer
            observer = setup_script_persistence(self.config_manager.get_persistence_config())
            
            # Test observer
            observer_stats = observer.get_observer_stats()
            
            self.setup_steps_completed.append("setup_observer_system")
            logger.info(f"✓ Observer system initialized: enabled={observer_stats['config']['enabled']}")
            
        except Exception as e:
            self.setup_errors.append(f"Observer system setup error: {str(e)}")
            logger.error(f"Step 4 failed: {e}")
    
    async def _step_migrate_existing_cache(self) -> None:
        """Step 5: Migrate existing preview cache"""
        try:
            logger.info("Step 5: Migrating existing preview cache")
            
            # Try to import existing preview cache
            try:
                from ..api.script_preview import preview_cache
                
                if preview_cache:
                    migrated_count = await migrate_existing_cache_to_persistence(preview_cache)
                    
                    if migrated_count > 0:
                        self.setup_steps_completed.append("migrate_existing_cache")
                        logger.info(f"✓ Migrated {migrated_count} scripts from cache")
                    else:
                        logger.info("✓ No scripts to migrate from cache")
                        self.setup_steps_completed.append("migrate_existing_cache")
                else:
                    logger.info("✓ No existing cache to migrate")
                    self.setup_steps_completed.append("migrate_existing_cache")
                    
            except ImportError:
                logger.info("✓ No existing preview cache found")
                self.setup_steps_completed.append("migrate_existing_cache")
                
        except Exception as e:
            self.setup_errors.append(f"Cache migration error: {str(e)}")
            logger.warning(f"Step 5 warning: {e}")
            # Don't raise - migration failure is not critical
    
    async def _step_initialize_analytics(self) -> None:
        """Step 6: Initialize analytics system"""
        try:
            logger.info("Step 6: Initializing analytics system")
            
            from .script_analytics_service import ScriptAnalyticsService
            from .script_repository import ScriptRepository
            
            config = self.config_manager.get_persistence_config()
            repository = ScriptRepository(config)
            analytics_service = ScriptAnalyticsService(repository)
            
            # Test analytics
            real_time_metrics = await analytics_service.get_real_time_metrics()
            
            self.setup_steps_completed.append("initialize_analytics")
            logger.info(f"✓ Analytics system initialized: {real_time_metrics.get('timestamp')}")
            
        except Exception as e:
            self.setup_errors.append(f"Analytics initialization error: {str(e)}")
            logger.warning(f"Step 6 warning: {e}")
            # Analytics failure is not critical
    
    async def _step_setup_api_endpoints(self) -> None:
        """Step 7: Setup API endpoints"""
        try:
            logger.info("Step 7: Setting up API endpoints")
            
            # The API endpoints are automatically available when the module is imported
            # Just verify the router is accessible
            from ..api.script_management import script_management_router
            
            endpoint_count = len(script_management_router.routes)
            
            self.setup_steps_completed.append("setup_api_endpoints")
            logger.info(f"✓ API endpoints available: {endpoint_count} routes")
            
        except Exception as e:
            self.setup_errors.append(f"API endpoints setup error: {str(e)}")
            logger.warning(f"Step 7 warning: {e}")
    
    async def _step_validate_setup(self) -> None:
        """Step 8: Validate complete setup"""
        try:
            logger.info("Step 8: Validating setup")
            
            validation_results = await self._run_setup_validation()
            
            if validation_results["overall_status"] == "healthy":
                self.setup_steps_completed.append("validate_setup")
                logger.info("✓ Setup validation passed")
            else:
                self.setup_errors.append("Setup validation failed")
                logger.warning("⚠ Setup validation failed")
                
        except Exception as e:
            self.setup_errors.append(f"Setup validation error: {str(e)}")
            logger.warning(f"Step 8 warning: {e}")
    
    async def _run_setup_validation(self) -> Dict[str, Any]:
        """Run comprehensive setup validation"""
        try:
            validation_results = {
                "timestamp": logger.info("Running setup validation"),
                "checks": {},
                "overall_status": "healthy"
            }
            
            # Check database connectivity
            try:
                from .script_repository import ScriptRepository
                config = self.config_manager.get_persistence_config()
                repository = ScriptRepository(config)
                stats = await repository.get_repository_stats()
                
                validation_results["checks"]["database"] = {
                    "status": "healthy" if stats.get("database_size_mb", 0) >= 0 else "unhealthy",
                    "details": stats
                }
            except Exception as e:
                validation_results["checks"]["database"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                validation_results["overall_status"] = "unhealthy"
            
            # Check observer system
            try:
                observer = GlobalScriptObserver.get_instance()
                observer_stats = observer.get_observer_stats()
                
                validation_results["checks"]["observer"] = {
                    "status": "healthy" if observer_stats["config"]["enabled"] else "disabled",
                    "details": observer_stats
                }
            except Exception as e:
                validation_results["checks"]["observer"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                validation_results["overall_status"] = "unhealthy"
            
            # Check configuration
            try:
                config_summary = self.config_manager.get_config_summary()
                
                validation_results["checks"]["configuration"] = {
                    "status": "healthy",
                    "details": config_summary
                }
            except Exception as e:
                validation_results["checks"]["configuration"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                validation_results["overall_status"] = "unhealthy"
            
            return validation_results
            
        except Exception as e:
            return {
                "timestamp": logger.error(f"Validation failed: {e}"),
                "overall_status": "unhealthy",
                "error": str(e)
            }
    
    def _generate_setup_report(self) -> Dict[str, Any]:
        """Generate comprehensive setup report"""
        logger.info("Generating setup report")
        return {
            "setup_timestamp": datetime.now(),
            "completed_steps": self.setup_steps_completed,
            "total_steps": 8,
            "success_rate": len(self.setup_steps_completed) / 8,
            "errors": self.setup_errors,
            "configuration_summary": self.config_manager.get_config_summary(),
            "next_steps": self._generate_next_steps(),
            "integration_guide": self._generate_integration_guide()
        }
    
    def _generate_next_steps(self) -> List[str]:
        """Generate recommended next steps based on setup results"""
        next_steps = []
        
        if "create_config_file" in self.setup_steps_completed:
            next_steps.append("Review and customize configuration in config/script_persistence.yml")
        
        if "setup_observer_system" in self.setup_steps_completed:
            next_steps.append("Integrate observer with existing ScriptPreviewer instances")
        
        if "setup_api_endpoints" in self.setup_steps_completed:
            next_steps.append("Add script management routes to your FastAPI application")
        
        if "initialize_analytics" in self.setup_steps_completed:
            next_steps.append("Set up analytics dashboard in your admin interface")
        
        next_steps.extend([
            "Test script generation and persistence functionality",
            "Set up monitoring and alerting for script persistence system",
            "Configure backup and retention policies",
            "Train users on new script management features"
        ])
        
        return next_steps
    
    def _generate_integration_guide(self) -> Dict[str, Any]:
        """Generate integration guide for developers"""
        return {
            "description": "How to integrate script persistence with your existing code",
            "integration_patterns": {
                "observer_pattern": {
                    "description": "Non-invasive integration using observers",
                    "code_example": """
# Wrap existing ScriptPreviewer
from ai_live_streamer.services.script_preview_observer import add_persistence_to_previewer

# Your existing previewer
previewer = ScriptPreviewer()

# Add persistence
wrapped_previewer = add_persistence_to_previewer(previewer)

# Use wrapped previewer - persistence happens automatically
result = await wrapped_previewer.generate_script_preview(form, created_by="user123")
                    """
                },
                "direct_integration": {
                    "description": "Direct integration with persistence manager",
                    "code_example": """
# Direct integration
from ai_live_streamer.services.script_persistence_manager import ScriptPersistenceManager

persistence_manager = ScriptPersistenceManager()

# After generating script
preview_result = await previewer.generate_script_preview(form)
script_id = await persistence_manager.persist_script_from_preview(
    preview_result, form, created_by="user123"
)
                    """
                },
                "api_integration": {
                    "description": "Add API endpoints to your FastAPI app",
                    "code_example": """
# Add to your FastAPI app
from ai_live_streamer.api.script_management import script_management_router

app = FastAPI()
app.include_router(script_management_router)

# API endpoints will be available at /api/scripts/*
                    """
                }
            },
            "configuration": {
                "environment_variables": [
                    "SCRIPT_PERSISTENCE_ENABLED=true",
                    "SCRIPT_PERSISTENCE_DATABASE_PATH=data/scripts.db",
                    "SCRIPT_PERSISTENCE_AUTO_PERSIST=true"
                ],
                "config_file": "config/script_persistence.yml"
            }
        }


# === Convenience Functions ===

async def quick_setup(
    config_file: Optional[str] = None,
    migrate_cache: bool = True
) -> Dict[str, Any]:
    """
    Quick setup for script persistence system
    
    Args:
        config_file: Optional configuration file path
        migrate_cache: Whether to migrate existing cache
        
    Returns:
        Setup results
    """
    setup = ScriptPersistenceSetup(config_file)
    return await setup.run_complete_setup(
        migrate_existing_cache=migrate_cache,
        create_config_file=True,
        validate_setup=True
    )


def integrate_with_existing_previewer(previewer_instance):
    """
    Integrate persistence with existing ScriptPreviewer instance
    
    Args:
        previewer_instance: Existing ScriptPreviewer instance
        
    Returns:
        Wrapped previewer with persistence capabilities
    """
    return add_persistence_to_previewer(previewer_instance)


async def health_check() -> Dict[str, Any]:
    """
    Perform health check on script persistence system
    
    Returns:
        Health check results
    """
    try:
        setup = ScriptPersistenceSetup()
        return await setup._run_setup_validation()
    except Exception as e:
        return {
            "overall_status": "unhealthy",
            "error": str(e),
            "timestamp": logger.error(f"Health check failed: {e}")
        }


def get_integration_status() -> Dict[str, Any]:
    """
    Get current integration status
    
    Returns:
        Integration status information
    """
    try:
        config_manager = get_config_manager()
        config = config_manager.get_config()
        
        # Check if various components are available
        components_status = {}
        
        # Check observer
        try:
            observer = GlobalScriptObserver.get_instance()
            components_status["observer"] = {
                "available": True,
                "enabled": observer.enabled,
                "stats": observer.get_observer_stats()
            }
        except Exception:
            components_status["observer"] = {"available": False}
        
        # Check database
        try:
            from .script_repository import ScriptRepository
            repository = ScriptRepository(config_manager.get_persistence_config())
            components_status["database"] = {"available": True}
        except Exception:
            components_status["database"] = {"available": False}
        
        # Check API
        try:
            from ..api.script_management import script_management_router
            components_status["api"] = {
                "available": True,
                "routes_count": len(script_management_router.routes)
            }
        except Exception:
            components_status["api"] = {"available": False}
        
        return {
            "system_enabled": config.enabled,
            "auto_persist": config.auto_persist,
            "database_path": config.database_path,
            "components": components_status,
            "configuration_sources": config_manager._config_sources
        }
        
    except Exception as e:
        return {
            "error": str(e),
            "system_enabled": False
        }