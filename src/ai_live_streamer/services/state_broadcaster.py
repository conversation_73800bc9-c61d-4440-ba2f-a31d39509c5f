"""统一状态广播服务

提供跨上下文的WebSocket状态广播能力，支持播放列表更新、QA插入等事件的实时通知。
采用单例模式和观察者模式，确保架构解耦和状态一致性。

Author: Claude Code
Date: 2025-08-09
"""

import asyncio
import json
import time
from typing import Dict, Any, Set, Optional, List
from datetime import datetime
from loguru import logger
from fastapi import WebSocket
from fastapi.websockets import WebSocketState

from ..core.streaming_config import StreamingConfig


class StateBroadcaster:
    """
    统一状态广播服务
    
    核心功能：
    1. 管理所有活跃的WebSocket连接
    2. 提供统一的状态广播接口  
    3. 支持版本号追踪和同步
    4. 处理广播失败和连接清理
    5. 提供广播统计和监控
    """
    
    def __init__(self, config: StreamingConfig):
        self.config = config
        
        # 连接管理
        self._connections: Dict[str, WebSocket] = {}
        self._client_versions: Dict[str, int] = {}  # 客户端已知版本
        self._current_playlist_version: int = 1
        
        # 重试配置
        self.max_retry_attempts = 2
        self.retry_delay_seconds = 0.1
        
        # 统计信息
        self._stats = {
            "total_broadcasts": 0,
            "successful_broadcasts": 0,
            "failed_broadcasts": 0,
            "active_connections": 0,
            "version_mismatches": 0,
            "connection_cleanups": 0,
            "last_broadcast_time": None,
            "broadcast_types": {}  # 按消息类型统计
        }
        
        # 中断响应收集机制
        
        logger.info("StateBroadcaster初始化完成")
        
    async def register_client(self, client_id: str, websocket: WebSocket, 
                            known_version: Optional[int] = None) -> None:
        """
        注册新的WebSocket连接
        
        Args:
            client_id: 客户端唯一标识符
            websocket: WebSocket连接对象
            known_version: 客户端已知的播放列表版本（可选）
        """
        self._connections[client_id] = websocket
        self._client_versions[client_id] = known_version or 0
        self._stats["active_connections"] += 1
        
        logger.info(f"📡 客户端已注册到广播器: {client_id} (已知版本: {known_version or 0})")
        
        # 检查版本是否需要同步
        if known_version and known_version < self._current_playlist_version:
            await self._send_version_sync_message(client_id, websocket)
            
    async def unregister_client(self, client_id: str) -> None:
        """
        注销WebSocket连接
        
        Args:
            client_id: 客户端唯一标识符
        """
        if client_id in self._connections:
            del self._connections[client_id]
            self._stats["active_connections"] -= 1
            
        if client_id in self._client_versions:
            del self._client_versions[client_id]
            
        self._stats["connection_cleanups"] += 1
        logger.debug(f"📡 客户端已从广播器注销: {client_id}")
        
    async def broadcast_playlist_update(self, update_reason: str, **kwargs) -> int:
        """
        广播播放列表更新事件（统一接口）
        
        Args:
            update_reason: 更新原因 ("qa_inserted", "script_loaded", "manual_update"等)
            **kwargs: 额外的更新信息
            
        Returns:
            成功广播的客户端数量
        """
        # 更新版本号
        self._current_playlist_version += 1
        
        # 构造统一的播放列表更新消息
        message = {
            "type": "playlist_updated",
            "playlist_version": self._current_playlist_version,
            "update_reason": update_reason,
            "server_time": time.time(),
            "timestamp": datetime.utcnow().isoformat(),
            **kwargs  # 合并额外信息
        }
        
        # 执行广播
        success_count = await self._broadcast_message(message)
        
        # 更新统计
        self._update_broadcast_stats("playlist_updated", success_count)
        
        logger.info(f"✅ 播放列表更新广播完成: reason={update_reason}, "
                   f"version={self._current_playlist_version}, success={success_count}/{len(self._connections)}")
        
        return success_count
        
    async def broadcast_qa_inserted(self, qa_id: str, question: str, answer: str,
                                  queue_position: int = 0, **additional_info) -> int:
        """
        广播QA插入事件（便捷方法 - 兼容旧接口）
        
        Args:
            qa_id: QA标识符
            question: 问题文本
            answer: 答案文本  
            queue_position: 队列位置
            **additional_info: 额外信息（处理时间、优先级等）
            
        Returns:
            成功广播的客户端数量
        """
        qa_info = {
            "qa_id": qa_id,
            "question": question,
            "answer_preview": answer[:100] + "..." if len(answer) > 100 else answer,
            "queue_position": queue_position,
            **additional_info
        }
        
        return await self.broadcast_playlist_update("qa_inserted", qa_info=qa_info)
        
        
        
        
    async def broadcast_item_inserted(self, insert_index: int, items: List[Dict[str, Any]], 
                                    playlist_version: int, reason: str = "qa_inserted",
                                    **additional_info) -> int:
        """
        广播项目插入事件（新的事务性事件驱动模式）
        
        Args:
            insert_index: 插入位置的索引
            items: 插入的项目列表（已转换为字典）
            playlist_version: 新的播放列表版本号
            reason: 插入原因
            **additional_info: 额外信息
            
        Returns:
            成功广播的客户端数量
        """
        # 边界检查：参数验证
        if insert_index < 0:
            logger.warning(f"⚠️ 插入索引为负数，已调整为0: {insert_index}")
            insert_index = 0
            
        if not items or not isinstance(items, list):
            logger.error("❌ 项目列表无效或为空，跳过广播")
            return 0
            
        if playlist_version <= 0:
            logger.error(f"❌ 播放列表版本无效: {playlist_version}")
            return 0
            
        # 更新播放列表版本（确保单调递增）
        self._current_playlist_version = max(playlist_version, self._current_playlist_version)
        
        # 构造item_inserted事件消息
        message = {
            "type": "item_inserted",
            "schema_version": "1.0",
            "insert_index": insert_index,
            "items": items,
            "playlist_version": playlist_version,
            "reason": reason,
            "server_time": time.time(),
            "timestamp": datetime.utcnow().isoformat(),
            **additional_info
        }
        
        # 执行广播
        success_count = await self._broadcast_message(message)
        
        # 更新统计
        self._update_broadcast_stats("item_inserted", success_count)
        
        logger.info(f"✅ 项目插入事件广播完成: reason={reason}, insert_index={insert_index}, "
                   f"version={playlist_version}, success={success_count}/{len(self._connections)}")
        
        return success_count
        
    async def broadcast_state_update(self, state_data: Dict[str, Any]) -> int:
        """
        广播通用状态更新（兼容原有接口）
        
        Args:
            state_data: 状态数据
            
        Returns:
            成功广播的客户端数量
        """
        # 确保消息格式正确
        if "type" not in state_data:
            state_data["type"] = "state_update"
            
        if "server_time" not in state_data:
            state_data["server_time"] = time.time()
            
        success_count = await self._broadcast_message(state_data)
        
        # 更新统计
        message_type = state_data.get("type", "unknown")
        self._update_broadcast_stats(message_type, success_count)
        
        return success_count
        
    async def ensure_client_version_sync(self, client_id: str, known_version: int) -> bool:
        """
        确保客户端版本同步
        
        Args:
            client_id: 客户端ID
            known_version: 客户端已知版本
            
        Returns:
            是否需要同步（版本不一致）
        """
        if known_version < self._current_playlist_version:
            # 版本落后，需要同步
            websocket = self._connections.get(client_id)
            if websocket:
                await self._send_version_sync_message(client_id, websocket)
                self._stats["version_mismatches"] += 1
                return True
                
        # 更新客户端已知版本
        self._client_versions[client_id] = max(known_version, self._client_versions.get(client_id, 0))
        return False
        
    async def _broadcast_message(self, message: Dict[str, Any]) -> int:
        """
        执行消息广播（内部方法）
        
        Args:
            message: 要广播的消息
            
        Returns:
            成功广播的客户端数量
        """
        # 边界检查：消息有效性验证
        if not message or not isinstance(message, dict):
            logger.error("❌ 消息格式无效，跳过广播")
            return 0
            
        if "type" not in message:
            logger.error("❌ 消息缺少类型字段，跳过广播")
            return 0
            
        # 提前序列化消息，检测序列化错误
        try:
            message_json = json.dumps(message, ensure_ascii=False)
        except (TypeError, ValueError) as e:
            logger.error(f"❌ 消息序列化失败: {e}")
            return 0
            
        if not self._connections:
            logger.debug("📡 无活跃连接，跳过广播")
            return 0
            
        successful_broadcasts = 0
        failed_clients = []
        
        # 并发广播到所有客户端
        async def send_to_client(client_id: str, websocket: WebSocket) -> bool:
            try:
                # 重试机制
                for attempt in range(self.max_retry_attempts + 1):
                    try:
                        if websocket.client_state == WebSocketState.CONNECTED:
                            await websocket.send_text(message_json)
                            return True
                        else:
                            logger.debug(f"📡 客户端连接已断开: {client_id}")
                            failed_clients.append(client_id)
                            return False
                            
                    except Exception as e:
                        if attempt < self.max_retry_attempts:
                            logger.debug(f"📡 广播重试 {attempt + 1}/{self.max_retry_attempts}: {client_id}")
                            await asyncio.sleep(self.retry_delay_seconds)
                        else:
                            logger.warning(f"📡 广播最终失败: {client_id}, error: {e}")
                            failed_clients.append(client_id)
                            return False
                            
                return False
                
            except Exception as e:
                logger.error(f"📡 广播异常: {client_id}, error: {e}")
                failed_clients.append(client_id)
                return False
                
        # 创建并发任务
        tasks = [
            send_to_client(client_id, websocket)
            for client_id, websocket in self._connections.items()
        ]
        
        # 执行并统计结果
        results = await asyncio.gather(*tasks, return_exceptions=True)
        successful_broadcasts = sum(1 for result in results if result is True)
        
        # 清理失败的连接
        if failed_clients:
            await self._cleanup_failed_connections(failed_clients)
            
        self._stats["total_broadcasts"] += 1
        self._stats["successful_broadcasts"] += successful_broadcasts
        self._stats["failed_broadcasts"] += len(failed_clients)
        self._stats["last_broadcast_time"] = time.time()
        
        return successful_broadcasts
        
    async def _send_version_sync_message(self, client_id: str, websocket: WebSocket) -> None:
        """
        发送版本同步消息
        
        Args:
            client_id: 客户端ID
            websocket: WebSocket连接
        """
        sync_message = {
            "type": "playlist_version_mismatch",
            "current_version": self._current_playlist_version,
            "client_known_version": self._client_versions.get(client_id, 0),
            "action_required": "client_should_sync",
            "server_time": time.time()
        }
        
        try:
            await websocket.send_text(json.dumps(sync_message, ensure_ascii=False))
            logger.info(f"📡 版本同步消息已发送: {client_id} "
                       f"(current={self._current_playlist_version}, "
                       f"client={self._client_versions.get(client_id, 0)})")
        except Exception as e:
            logger.error(f"📡 版本同步消息发送失败: {client_id}, error: {e}")
            
    async def _cleanup_failed_connections(self, failed_clients: List[str]) -> None:
        """
        清理失败的连接
        
        Args:
            failed_clients: 失败的客户端ID列表
        """
        for client_id in failed_clients:
            await self.unregister_client(client_id)
            
        if failed_clients:
            logger.info(f"📡 已清理失败连接: {len(failed_clients)} 个")
            
    def _update_broadcast_stats(self, message_type: str, success_count: int) -> None:
        """
        更新广播统计信息
        
        Args:
            message_type: 消息类型
            success_count: 成功次数
        """
        if message_type not in self._stats["broadcast_types"]:
            self._stats["broadcast_types"][message_type] = 0
        self._stats["broadcast_types"][message_type] += success_count
        
    def get_stats(self) -> Dict[str, Any]:
        """
        获取广播统计信息
        
        Returns:
            统计信息字典
        """
        return {
            **self._stats,
            "current_playlist_version": self._current_playlist_version,
            "client_versions": dict(self._client_versions),
            "connected_clients": list(self._connections.keys()),
            "uptime_seconds": time.time() - (self._stats.get("start_time", time.time()))
        }
        
    def get_connected_clients(self) -> Set[str]:
        """
        获取已连接客户端列表
        
        Returns:
            客户端ID集合
        """
        return set(self._connections.keys())
        
    def get_current_playlist_version(self) -> int:
        """
        获取当前播放列表版本
        
        Returns:
            当前版本号
        """
        return self._current_playlist_version
        
    async def cleanup(self) -> None:
        """
        清理所有连接和资源
        """
        logger.info("📡 开始清理StateBroadcaster...")
        
        # 清理所有连接
        for client_id in list(self._connections.keys()):
            await self.unregister_client(client_id)
            
        self._connections.clear()
        self._client_versions.clear()
        
        logger.info("📡 StateBroadcaster清理完成")


# 单例实例（由依赖注入系统管理）
_broadcaster_instance: Optional[StateBroadcaster] = None


def create_state_broadcaster(config: StreamingConfig) -> StateBroadcaster:
    """
    创建状态广播器实例
    
    Args:
        config: 流式配置
        
    Returns:
        StateBroadcaster实例
    """
    global _broadcaster_instance
    if _broadcaster_instance is None:
        _broadcaster_instance = StateBroadcaster(config)
        _broadcaster_instance._stats["start_time"] = time.time()
        logger.info("✅ StateBroadcaster单例实例已创建")
    return _broadcaster_instance


def get_state_broadcaster() -> Optional[StateBroadcaster]:
    """
    获取状态广播器实例（如果已创建）
    
    Returns:
        StateBroadcaster实例或None
    """
    return _broadcaster_instance


def reset_state_broadcaster() -> None:
    """
    重置状态广播器实例（测试用）
    """
    global _broadcaster_instance
    _broadcaster_instance = None
    logger.debug("StateBroadcaster实例已重置")