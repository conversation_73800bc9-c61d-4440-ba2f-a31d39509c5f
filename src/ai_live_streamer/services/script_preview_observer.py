"""
Script Preview Observer

Non-invasive integration with existing ScriptPreviewer service to add 
persistence capabilities without modifying the core preview generation logic.
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable, Tuple
from datetime import datetime
from loguru import logger

from ..services.script_previewer import PreviewResult
from .script_persistence_manager import ScriptPersistenceManager
from ..models.forms import OperationalForm
from ..models.script_persistence import PersistenceConfig
from ..core.config import cfg


class ScriptPreviewObserver:
    """
    Observer that monitors script preview generation and automatically
    persists successful results for future use and analytics.
    """
    
    def __init__(self, persistence_manager: Optional[ScriptPersistenceManager] = None):
        self.persistence_manager = persistence_manager or ScriptPersistenceManager()
        self.enabled = cfg.get('script_persistence.enabled', True)
        self.auto_persist = cfg.get('script_persistence.auto_persist', True)
        self.persist_threshold = cfg.get('script_persistence.quality_threshold', 0.0)
        
        # Statistics
        self.stats = {
            "previews_observed": 0,
            "scripts_persisted": 0,
            "persistence_failures": 0,
            "skipped_low_quality": 0,
            "last_observation": None
        }
        
        logger.info(f"Script Preview Observer initialized (enabled: {self.enabled}, auto_persist: {self.auto_persist})")
    
    async def observe_preview_generation(
        self, 
        preview_result: PreviewResult, 
        form: OperationalForm,
        created_by: Optional[str] = None,
        additional_metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Observe a preview generation result and persist if appropriate
        
        Args:
            preview_result: The generated preview result
            form: Original operational form
            created_by: User who created the script
            additional_metadata: Additional metadata to store
            
        Returns:
            Script ID if persisted, None otherwise
        """
        if not self.enabled:
            return None
        
        try:
            self.stats["previews_observed"] += 1
            self.stats["last_observation"] = datetime.now()
            
            logger.debug(f"Observing preview generation for form {preview_result.form_id}")
            
            # Check if preview was successful
            if not preview_result.success:
                logger.debug(f"Skipping persistence for failed preview: {preview_result.form_id}")
                return None
            
            # Check if auto-persistence is enabled
            if not self.auto_persist:
                logger.debug("Auto-persistence disabled, skipping")
                return None
            
            # Enhance preview result with additional metadata if provided
            if additional_metadata:
                self._enhance_preview_result(preview_result, additional_metadata)
            
            # Persist the script
            script_id = await self.persistence_manager.persist_script_from_preview(
                preview_result, form, created_by
            )
            
            if script_id:
                self.stats["scripts_persisted"] += 1
                logger.info(f"Successfully persisted script {script_id} from preview {preview_result.form_id}")
                
                # Notify about successful persistence
                await self._notify_persistence_success(script_id, preview_result, form)
            else:
                self.stats["persistence_failures"] += 1
                logger.warning(f"Failed to persist script from preview {preview_result.form_id}")
            
            return script_id
            
        except Exception as e:
            self.stats["persistence_failures"] += 1
            logger.error(f"Observer error for preview {preview_result.form_id}: {e}")
            return None
    
    async def observe_batch_preview_generation(
        self,
        preview_results: List[Tuple[PreviewResult, OperationalForm]],
        created_by: Optional[str] = None
    ) -> List[Optional[str]]:
        """
        Observe batch preview generation and persist in batch
        
        Args:
            preview_results: List of (preview_result, form) tuples
            created_by: User who created the scripts
            
        Returns:
            List of script IDs (None for failed persistence)
        """
        if not self.enabled or not self.auto_persist:
            return [None] * len(preview_results)
        
        try:
            logger.info(f"Observing batch preview generation: {len(preview_results)} scripts")
            
            # Filter successful previews
            valid_previews = [
                (preview, form) for preview, form in preview_results 
                if preview.success
            ]
            
            if not valid_previews:
                logger.debug("No successful previews to persist in batch")
                return [None] * len(preview_results)
            
            # Batch persist
            script_ids = await self.persistence_manager.batch_persist_scripts(
                valid_previews, created_by
            )
            
            # Update statistics
            successful_count = sum(1 for sid in script_ids if sid is not None)
            self.stats["scripts_persisted"] += successful_count
            self.stats["persistence_failures"] += len(script_ids) - successful_count
            
            logger.info(f"Batch persistence completed: {successful_count}/{len(script_ids)} scripts persisted")
            
            return script_ids
            
        except Exception as e:
            logger.error(f"Batch observer error: {e}")
            return [None] * len(preview_results)
    
    def observe_preview_usage(
        self,
        script_id: str,
        usage_context: Dict[str, Any]
    ) -> None:
        """
        Observe when a persisted script is used in live streaming
        
        Args:
            script_id: ID of the script being used
            usage_context: Context about how the script is being used
        """
        if not self.enabled:
            return
        
        try:
            # Track usage asynchronously
            asyncio.create_task(self._track_script_usage_async(script_id, usage_context))
            
        except Exception as e:
            logger.error(f"Usage observation error for script {script_id}: {e}")
    
    async def _track_script_usage_async(self, script_id: str, usage_context: Dict[str, Any]) -> None:
        """Track script usage asynchronously"""
        try:
            await self.persistence_manager.track_script_usage(script_id, usage_context)
            logger.debug(f"Tracked usage for script {script_id}")
            
        except Exception as e:
            logger.error(f"Failed to track usage for script {script_id}: {e}")
    
    def _enhance_preview_result(self, preview_result: PreviewResult, metadata: Dict[str, Any]) -> None:
        """Enhance preview result with additional metadata"""
        try:
            # Add metadata to estimated metrics
            if not hasattr(preview_result, 'estimated_metrics') or preview_result.estimated_metrics is None:
                preview_result.estimated_metrics = {}
            
            preview_result.estimated_metrics.update({
                'observer_metadata': metadata,
                'observation_timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.warning(f"Failed to enhance preview result: {e}")
    
    async def _notify_persistence_success(
        self, 
        script_id: str, 
        preview_result: PreviewResult, 
        form: OperationalForm
    ) -> None:
        """Notify about successful persistence (for potential integrations)"""
        try:
            # This could be extended to notify other services
            # For now, just log and potentially trigger webhooks
            
            notification_data = {
                'script_id': script_id,
                'form_id': preview_result.form_id,
                'timestamp': datetime.now().isoformat(),
                'generation_time_seconds': preview_result.generation_time_seconds,
                'segment_count': len(preview_result.script_timeline.segments) if preview_result.script_timeline else 0
            }
            
            # Could add webhook notifications here
            webhook_url = cfg.get('script_persistence.webhook_url')
            if webhook_url:
                await self._send_webhook_notification(webhook_url, notification_data)
            
        except Exception as e:
            logger.warning(f"Failed to send persistence notification: {e}")
    
    async def _send_webhook_notification(self, webhook_url: str, data: Dict[str, Any]) -> None:
        """Send webhook notification about script persistence"""
        try:
            import aiohttp
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status >= 400:
                        logger.warning(f"Webhook notification failed: {response.status}")
                    else:
                        logger.debug("Webhook notification sent successfully")
                        
        except Exception as e:
            logger.warning(f"Webhook notification error: {e}")
    
    # === Integration Methods ===
    
    def create_previewer_wrapper(self, original_previewer):
        """
        Create a wrapper around the original ScriptPreviewer that adds persistence
        
        Args:
            original_previewer: Original ScriptPreviewer instance
            
        Returns:
            Wrapped previewer with persistence capabilities
        """
        return ScriptPreviewerWrapper(original_previewer, self)
    
    def get_observer_stats(self) -> Dict[str, Any]:
        """Get observer statistics"""
        return {
            **self.stats,
            "config": {
                "enabled": self.enabled,
                "auto_persist": self.auto_persist,
                "persist_threshold": self.persist_threshold
            },
            "persistence_manager_stats": self.persistence_manager.get_manager_stats()
        }
    
    def enable_persistence(self) -> None:
        """Enable persistence operations"""
        self.enabled = True
        logger.info("Script persistence enabled")
    
    def disable_persistence(self) -> None:
        """Disable persistence operations"""
        self.enabled = False
        logger.info("Script persistence disabled")
    
    def set_auto_persist(self, enabled: bool) -> None:
        """Enable/disable automatic persistence"""
        self.auto_persist = enabled
        logger.info(f"Auto-persistence {'enabled' if enabled else 'disabled'}")


class ScriptPreviewerWrapper:
    """
    Wrapper around the original ScriptPreviewer that adds persistence capabilities
    without modifying the original service.
    """
    
    def __init__(self, original_previewer, observer: ScriptPreviewObserver):
        self.original_previewer = original_previewer
        self.observer = observer
        
        # Delegate all attributes to the original previewer
        self.__dict__.update(original_previewer.__dict__)
    
    async def generate_script_preview(self, form: OperationalForm, created_by: Optional[str] = None) -> PreviewResult:
        """
        Generate script preview with automatic persistence
        
        Args:
            form: Operational form
            created_by: User who created the script
            
        Returns:
            PreviewResult with potential persistence
        """
        try:
            # Call original method
            preview_result = await self.original_previewer.generate_script_preview(form)
            
            # Observe and potentially persist
            script_id = await self.observer.observe_preview_generation(
                preview_result, form, created_by
            )
            
            # Add script_id to result if persisted
            if script_id and hasattr(preview_result, 'estimated_metrics'):
                if preview_result.estimated_metrics is None:
                    preview_result.estimated_metrics = {}
                preview_result.estimated_metrics['persisted_script_id'] = script_id
            
            return preview_result
            
        except Exception as e:
            logger.error(f"Wrapped preview generation failed: {e}")
            # Return original result even if persistence fails
            return await self.original_previewer.generate_script_preview(form)
    
    def __getattr__(self, name):
        """Delegate all other attributes to the original previewer"""
        return getattr(self.original_previewer, name)


class GlobalScriptObserver:
    """
    Global singleton observer for easy integration across the application
    """
    
    _instance: Optional[ScriptPreviewObserver] = None
    
    @classmethod
    def get_instance(cls) -> ScriptPreviewObserver:
        """Get or create global observer instance"""
        if cls._instance is None:
            cls._instance = ScriptPreviewObserver()
        return cls._instance
    
    @classmethod
    def initialize(cls, persistence_manager: Optional[ScriptPersistenceManager] = None) -> ScriptPreviewObserver:
        """Initialize global observer with custom persistence manager"""
        cls._instance = ScriptPreviewObserver(persistence_manager)
        return cls._instance
    
    @classmethod
    def observe_preview(
        cls, 
        preview_result: PreviewResult, 
        form: OperationalForm,
        created_by: Optional[str] = None
    ) -> Optional[str]:
        """
        Convenience method to observe preview generation
        
        Returns:
            Coroutine that resolves to script_id or None
        """
        observer = cls.get_instance()
        return asyncio.create_task(
            observer.observe_preview_generation(preview_result, form, created_by)
        )
    
    @classmethod
    def observe_usage(cls, script_id: str, usage_context: Dict[str, Any]) -> None:
        """Convenience method to observe script usage"""
        observer = cls.get_instance()
        observer.observe_preview_usage(script_id, usage_context)


# === Easy Integration Functions ===

def setup_script_persistence(
    config: Optional[PersistenceConfig] = None,
    enable_webhooks: bool = False
) -> ScriptPreviewObserver:
    """
    Easy setup function for script persistence system
    
    Args:
        config: Persistence configuration
        enable_webhooks: Whether to enable webhook notifications
        
    Returns:
        Configured observer instance
    """
    try:
        # Initialize persistence manager
        persistence_manager = ScriptPersistenceManager(config)
        
        # Initialize global observer
        observer = GlobalScriptObserver.initialize(persistence_manager)
        
        logger.info("Script persistence system initialized successfully")
        return observer
        
    except Exception as e:
        logger.error(f"Failed to setup script persistence: {e}")
        raise


def add_persistence_to_previewer(previewer_instance) -> ScriptPreviewerWrapper:
    """
    Add persistence capabilities to an existing ScriptPreviewer instance
    
    Args:
        previewer_instance: Existing ScriptPreviewer instance
        
    Returns:
        Wrapped previewer with persistence
    """
    try:
        observer = GlobalScriptObserver.get_instance()
        wrapped_previewer = observer.create_previewer_wrapper(previewer_instance)
        
        logger.info("Added persistence capabilities to ScriptPreviewer")
        return wrapped_previewer
        
    except Exception as e:
        logger.error(f"Failed to add persistence to previewer: {e}")
        return previewer_instance  # Return original on failure


async def migrate_existing_cache_to_persistence(preview_cache: Dict) -> int:
    """
    Migrate existing preview cache to persistent storage
    
    Args:
        preview_cache: Existing preview cache dictionary
        
    Returns:
        Number of scripts successfully migrated
    """
    try:
        from ..database.migrations import migrate_from_preview_cache, DatabaseMigrator
        
        config = PersistenceConfig()
        migrator = DatabaseMigrator(config)
        
        migrated_count = migrate_from_preview_cache(preview_cache, migrator)
        
        logger.info(f"Successfully migrated {migrated_count} scripts from preview cache")
        return migrated_count
        
    except Exception as e:
        logger.error(f"Cache migration failed: {e}")
        return 0