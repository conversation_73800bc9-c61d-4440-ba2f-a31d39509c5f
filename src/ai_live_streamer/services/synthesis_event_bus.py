"""合成请求事件总线 - 基于队列的新架构

使用请求/应答队列模式替代Future，彻底解决死锁和竞态条件问题。
实现请求去重，避免重复合成相同内容。

Author: Claude Code
Date: 2025-08-11
"""

import asyncio
import time
import uuid
from typing import Optional, Callable, Dict, Any, List, Tuple, TYPE_CHECKING
from dataclasses import dataclass, field
from loguru import logger
from datetime import datetime

from ..models.playlist_models import PlaylistItem
from ..exceptions import ContentNotReadyError

if TYPE_CHECKING:
    pass


@dataclass
class SynthesisRequest:
    """合成请求数据结构（新架构）"""
    request_id: str
    item: PlaylistItem
    cache_key: str  # 用于去重
    priority: int  # 0 = highest priority (client request), 1+ = pre-synthesis
    client_id: str
    created_at: datetime = field(default_factory=datetime.utcnow)
    
    def __post_init__(self):
        if not self.request_id:
            self.request_id = uuid.uuid4().hex[:8]
        if not self.cache_key:
            self.cache_key = self.item.get_cache_key()


class SynthesisEventBus:
    """
    合成请求事件总线 - 基于消息队列的新架构
    
    使用请求/应答队列模式，消除Future共享状态，根除死锁问题。
    实现请求去重，避免重复合成相同内容。
    """
    
    def __init__(self):
        """初始化事件总线（新架构）"""
        # 核心队列（带背压控制）
        self.request_queue: asyncio.Queue = asyncio.Queue(maxsize=100)
        
        # 回复映射：request_id -> (reply_queue, timestamp)
        self.reply_map: Dict[str, Tuple[asyncio.Queue, float]] = {}
        self._reply_map_lock = asyncio.Lock()
        
        # 去重机制：cache_key -> [reply_queues]
        self._in_progress_map: Dict[str, List[asyncio.Queue]] = {}
        self._in_progress_lock = asyncio.Lock()
        
        # 统计信息
        self._stats = {
            "total_requests": 0,
            "high_priority_requests": 0,
            "normal_priority_requests": 0,
            "completed_requests": 0,
            "failed_requests": 0,
            "dedup_hits": 0,  # 去重命中次数
            "average_response_time_ms": 0.0
        }
        self._response_times = []
        
        # 运行状态
        self._running = False
        self._orphan_cleaner_task = None
        
        logger.info("合成事件总线已初始化（基于队列的新架构）")
    
    async def start(self):
        """启动事件总线"""
        if self._running:
            logger.warning("事件总线已在运行中")
            return
            
        self._running = True
        
        # 启动孤儿清理任务
        self._orphan_cleaner_task = asyncio.create_task(self._orphan_cleaner())
        
        logger.info("合成事件总线已启动（带孤儿清理）")
    
    async def stop(self):
        """停止事件总线"""
        if not self._running:
            return
            
        self._running = False
        
        # 停止孤儿清理任务
        if self._orphan_cleaner_task and not self._orphan_cleaner_task.done():
            self._orphan_cleaner_task.cancel()
            try:
                await self._orphan_cleaner_task
            except asyncio.CancelledError:
                pass
        
        # 清理待处理的请求
        while not self.request_queue.empty():
            try:
                request = self.request_queue.get_nowait()
                # 通知所有等待者
                async with self._in_progress_lock:
                    subscribers = self._in_progress_map.pop(request.cache_key, [])
                for reply_queue in subscribers:
                    try:
                        await reply_queue.put(Exception("Event bus shutting down"))
                    except:
                        pass
            except asyncio.QueueEmpty:
                break
        
        # 清理回复映射
        async with self._reply_map_lock:
            self.reply_map.clear()
        
        logger.info("合成事件总线已停止")
    
    async def request_synthesis(self, 
                               item: PlaylistItem,
                               priority: int = 0,
                               client_id: str = "unknown") -> Tuple[bytes, float]:
        """
        请求合成音频（新架构：基于队列，带去重）
        
        Args:
            item: 要合成的播放列表项
            priority: 优先级 (0=最高，用于客户端请求)
            client_id: 请求的客户端ID
            
        Returns:
            (audio_data, duration_ms) 或抛出ContentNotReadyError
        """
        if not self._running:
            raise RuntimeError("Event bus is not running")
        
        cache_key = item.get_cache_key()
        reply_queue = asyncio.Queue(1)  # 容量为1的专用回复队列
        request_id = None
        is_new_request = False
        
        # 检查是否有进行中的合成（去重）
        async with self._in_progress_lock:
            if cache_key in self._in_progress_map:
                # 已有任务，订阅结果
                self._in_progress_map[cache_key].append(reply_queue)
                logger.info(f"🔄 订阅现有合成任务: {cache_key}")
                self._stats["dedup_hits"] += 1
            else:
                # 新任务，创建订阅列表
                self._in_progress_map[cache_key] = [reply_queue]
                is_new_request = True
                
                # 生成请求ID并注册回复地址
                request_id = uuid.uuid4().hex[:8]
                async with self._reply_map_lock:
                    self.reply_map[request_id] = (reply_queue, time.time())
                
                logger.info(f"📤 发送新合成请求 [{request_id}]: {item.item_id}")
        
        # 只有新请求才需要发送到队列
        if is_new_request:
            # 创建并发送请求
            request = SynthesisRequest(
                request_id=request_id,
                item=item,
                cache_key=cache_key,
                priority=priority,
                client_id=client_id
            )
            
            # 更新统计
            self._stats["total_requests"] += 1
            if priority == 0:
                self._stats["high_priority_requests"] += 1
            else:
                self._stats["normal_priority_requests"] += 1
            
            try:
                await self.request_queue.put(request)
            except asyncio.QueueFull:
                # 队列满，触发背压
                logger.warning(f"⚠️ 请求队列已满，等待空间...")
                await self.request_queue.put(request)  # 阻塞直到有空间
        
        try:
            # 等待回复（带超时）
            result = await asyncio.wait_for(reply_queue.get(), timeout=5.0)
            
            if isinstance(result, Exception):
                raise result
            
            self._stats["completed_requests"] += 1
            logger.info(f"✅ 收到合成结果: {cache_key}")
            return result
            
        except asyncio.TimeoutError:
            self._stats["failed_requests"] += 1
            logger.info(f"⏱️ 合成请求超时: {cache_key}")
            raise ContentNotReadyError(
                message=f"Content '{item.item_id}' is being generated",
                retry_after_ms=1000
            )
        finally:
            # 清理回复地址（仅清理创建者的）
            if request_id:
                async with self._reply_map_lock:
                    self.reply_map.pop(request_id, None)
    
    async def send_synthesis_result(self, cache_key: str, result: Any) -> int:
        """
        向所有订阅者发送合成结果（供ProactiveSynthesizer调用）
        
        Args:
            cache_key: 内容的缓存键
            result: 合成结果 (audio_data, duration_ms) 或 Exception
            
        Returns:
            通知的订阅者数量
        """
        # 获取所有订阅者
        async with self._in_progress_lock:
            subscribers = self._in_progress_map.pop(cache_key, [])
        
        # 向所有订阅者发送结果
        notified = 0
        for reply_queue in subscribers:
            try:
                await reply_queue.put(result)
                notified += 1
            except:
                pass  # 订阅者可能已超时
        
        if notified > 0:
            logger.debug(f"📨 向 {notified} 个订阅者发送结果: {cache_key}")
        
        return notified
    
    async def _orphan_cleaner(self):
        """定期清理超时的回复队列"""
        while self._running:
            try:
                await asyncio.sleep(60)  # 每分钟执行
                
                async with self._reply_map_lock:
                    now = time.time()
                    expired = []
                    
                    for request_id, (queue, timestamp) in self.reply_map.items():
                        if now - timestamp > 60:  # 60秒超时
                            expired.append(request_id)
                    
                    for request_id in expired:
                        del self.reply_map[request_id]
                        logger.debug(f"🧹 清理孤儿队列 [{request_id}]")
                    
                    if expired:
                        logger.info(f"清理了 {len(expired)} 个孤儿回复队列")
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"孤儿清理任务错误: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self._stats,
            "pending_requests": self.request_queue.qsize(),
            "reply_map_size": len(self.reply_map),
            "in_progress_tasks": len(self._in_progress_map),
            "is_running": self._running
        }


# 全局单例实例
_synthesis_event_bus: Optional[SynthesisEventBus] = None


def get_synthesis_event_bus() -> SynthesisEventBus:
    """获取合成事件总线单例"""
    global _synthesis_event_bus
    if _synthesis_event_bus is None:
        _synthesis_event_bus = SynthesisEventBus()
        logger.info("创建合成事件总线单例")
    return _synthesis_event_bus