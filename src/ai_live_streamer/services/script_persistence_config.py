"""
Script Persistence Configuration Management

Configuration management for the script persistence system with environment
variable support, validation, and runtime configuration updates.
"""

import os
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from pathlib import Path
import yaml
import json
from loguru import logger

from ..models.script_persistence import PersistenceConfig
from ..core.config import cfg


@dataclass
class ScriptPersistenceSystemConfig:
    """Complete configuration for the script persistence system"""
    
    # Core persistence settings
    enabled: bool = True
    auto_persist: bool = True
    database_path: str = "data/scripts.db"
    
    # Quality validation settings
    quality_validation_enabled: bool = True
    min_quality_threshold: float = 0.6
    enable_duplicate_detection: bool = True
    
    # Performance settings
    connection_pool_size: int = 10
    cache_size_mb: int = 64
    enable_async_operations: bool = True
    batch_insert_size: int = 100
    
    # Retention policies
    auto_archive_after_days: int = 90
    auto_delete_after_days: int = 365
    max_scripts_per_user: int = 1000
    
    # Analytics settings
    analytics_enabled: bool = True
    analytics_cache_ttl_seconds: int = 300
    analytics_retention_days: int = 180
    trend_analysis_enabled: bool = True
    
    # Export settings
    export_enabled: bool = True
    max_export_batch_size: int = 500
    export_timeout_seconds: int = 300
    export_formats: List[str] = field(default_factory=lambda: ["json", "csv", "html"])
    
    # Integration settings
    webhook_notifications_enabled: bool = False
    webhook_url: Optional[str] = None
    webhook_timeout_seconds: int = 10
    
    # Observer settings
    observer_enabled: bool = True
    observer_auto_persist: bool = True
    observer_persist_threshold: float = 0.0
    
    # Benchmarks for analytics
    quality_benchmark: float = 0.7
    engagement_benchmark: float = 0.6
    usage_benchmark: float = 0.3
    generation_time_benchmark_ms: int = 5000
    
    # Feature flags
    enable_full_text_search: bool = True
    enable_performance_insights: bool = True
    enable_comparative_analysis: bool = True
    enable_real_time_metrics: bool = True
    
    # Debug and logging
    debug_mode: bool = False
    log_level: str = "INFO"
    log_database_operations: bool = False


class ConfigurationManager:
    """Manages configuration for the script persistence system"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self._config: Optional[ScriptPersistenceSystemConfig] = None
        self._config_sources: List[str] = []
        
        # Load configuration
        self._load_configuration()
        
        logger.info(f"Configuration Manager initialized with sources: {self._config_sources}")
    
    def get_config(self) -> ScriptPersistenceSystemConfig:
        """Get current configuration"""
        if self._config is None:
            self._load_configuration()
        return self._config
    
    def get_persistence_config(self) -> PersistenceConfig:
        """Get PersistenceConfig from system configuration"""
        config = self.get_config()
        
        return PersistenceConfig(
            database_path=config.database_path,
            connection_pool_size=config.connection_pool_size,
            enable_wal_mode=True,
            max_scripts_per_user=config.max_scripts_per_user,
            auto_archive_after_days=config.auto_archive_after_days,
            auto_delete_after_days=config.auto_delete_after_days,
            batch_insert_size=config.batch_insert_size,
            cache_size_mb=config.cache_size_mb,
            enable_async_operations=config.enable_async_operations,
            enable_usage_tracking=config.analytics_enabled,
            enable_performance_monitoring=config.enable_performance_insights,
            analytics_retention_days=config.analytics_retention_days,
            max_export_batch_size=config.max_export_batch_size,
            export_timeout_seconds=config.export_timeout_seconds,
            enable_quality_validation=config.quality_validation_enabled,
            min_quality_threshold=config.min_quality_threshold,
            enable_duplicate_detection=config.enable_duplicate_detection
        )
    
    def _load_configuration(self) -> None:
        """Load configuration from multiple sources"""
        try:
            # Start with default configuration
            config_dict = self._get_default_config()
            self._config_sources.append("defaults")
            
            # Load from config file if specified
            if self.config_file and Path(self.config_file).exists():
                file_config = self._load_config_file(self.config_file)
                config_dict.update(file_config)
                self._config_sources.append(f"file:{self.config_file}")
            
            # Load from main application config
            app_config = self._load_from_app_config()
            config_dict.update(app_config)
            self._config_sources.append("app_config")
            
            # Load from environment variables
            env_config = self._load_from_environment()
            config_dict.update(env_config)
            if env_config:
                self._config_sources.append("environment")
            
            # Create configuration object
            self._config = ScriptPersistenceSystemConfig(**config_dict)
            
            # Validate configuration
            self._validate_configuration()
            
            logger.debug(f"Configuration loaded: {len(config_dict)} settings")
            
        except Exception as e:
            logger.error(f"Configuration loading failed: {e}")
            # Fall back to default configuration
            self._config = ScriptPersistenceSystemConfig()
            self._config_sources = ["defaults_fallback"]
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration values"""
        default_config = ScriptPersistenceSystemConfig()
        return {
            field.name: getattr(default_config, field.name) 
            for field in default_config.__dataclass_fields__.values()
        }
    
    def _load_config_file(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML or JSON file"""
        try:
            config_path = Path(config_path)
            
            if not config_path.exists():
                logger.warning(f"Config file not found: {config_path}")
                return {}
            
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yml', '.yaml']:
                    file_config = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    file_config = json.load(f)
                else:
                    logger.warning(f"Unsupported config file format: {config_path}")
                    return {}
            
            # Extract script persistence settings
            script_config = file_config.get('script_persistence', {})
            logger.debug(f"Loaded {len(script_config)} settings from {config_path}")
            return script_config
            
        except Exception as e:
            logger.error(f"Failed to load config file {config_path}: {e}")
            return {}
    
    def _load_from_app_config(self) -> Dict[str, Any]:
        """Load from main application configuration"""
        try:
            app_config = {}
            
            # Get script persistence settings from main config
            if hasattr(cfg, 'get'):
                persistence_settings = cfg.get('script_persistence', {})
                if isinstance(persistence_settings, dict):
                    app_config.update(persistence_settings)
                
                # Get analytics settings
                analytics_settings = cfg.get('analytics', {})
                if isinstance(analytics_settings, dict):
                    # Map analytics settings to our config
                    if 'cache_ttl' in analytics_settings:
                        app_config['analytics_cache_ttl_seconds'] = analytics_settings['cache_ttl']
                    if 'retention_days' in analytics_settings:
                        app_config['analytics_retention_days'] = analytics_settings['retention_days']
                    if 'benchmarks' in analytics_settings:
                        benchmarks = analytics_settings['benchmarks']
                        if 'quality' in benchmarks:
                            app_config['quality_benchmark'] = benchmarks['quality']
                        if 'engagement' in benchmarks:
                            app_config['engagement_benchmark'] = benchmarks['engagement']
                        if 'usage' in benchmarks:
                            app_config['usage_benchmark'] = benchmarks['usage']
            
            logger.debug(f"Loaded {len(app_config)} settings from app config")
            return app_config
            
        except Exception as e:
            logger.warning(f"Failed to load from app config: {e}")
            return {}
    
    def _load_from_environment(self) -> Dict[str, Any]:
        """Load configuration from environment variables"""
        try:
            env_config = {}
            
            # Environment variable mappings
            env_mappings = {
                'SCRIPT_PERSISTENCE_ENABLED': ('enabled', self._parse_bool),
                'SCRIPT_PERSISTENCE_AUTO_PERSIST': ('auto_persist', self._parse_bool),
                'SCRIPT_PERSISTENCE_DATABASE_PATH': ('database_path', str),
                'SCRIPT_PERSISTENCE_QUALITY_VALIDATION': ('quality_validation_enabled', self._parse_bool),
                'SCRIPT_PERSISTENCE_MIN_QUALITY': ('min_quality_threshold', float),
                'SCRIPT_PERSISTENCE_POOL_SIZE': ('connection_pool_size', int),
                'SCRIPT_PERSISTENCE_CACHE_SIZE_MB': ('cache_size_mb', int),
                'SCRIPT_PERSISTENCE_ARCHIVE_DAYS': ('auto_archive_after_days', int),
                'SCRIPT_PERSISTENCE_DELETE_DAYS': ('auto_delete_after_days', int),
                'SCRIPT_PERSISTENCE_MAX_SCRIPTS': ('max_scripts_per_user', int),
                'SCRIPT_PERSISTENCE_ANALYTICS_ENABLED': ('analytics_enabled', self._parse_bool),
                'SCRIPT_PERSISTENCE_WEBHOOK_URL': ('webhook_url', str),
                'SCRIPT_PERSISTENCE_WEBHOOK_ENABLED': ('webhook_notifications_enabled', self._parse_bool),
                'SCRIPT_PERSISTENCE_DEBUG': ('debug_mode', self._parse_bool),
                'SCRIPT_PERSISTENCE_LOG_LEVEL': ('log_level', str),
            }
            
            # Load from environment
            for env_var, (config_key, parser) in env_mappings.items():
                value = os.getenv(env_var)
                if value is not None:
                    try:
                        env_config[config_key] = parser(value)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Invalid value for {env_var}: {value} ({e})")
            
            logger.debug(f"Loaded {len(env_config)} settings from environment")
            return env_config
            
        except Exception as e:
            logger.warning(f"Failed to load from environment: {e}")
            return {}
    
    def _parse_bool(self, value: str) -> bool:
        """Parse boolean value from string"""
        return str(value).lower() in ('true', '1', 'yes', 'on', 'enabled')
    
    def _validate_configuration(self) -> None:
        """Validate configuration values"""
        config = self._config
        
        # Validate database path
        if config.database_path:
            db_path = Path(config.database_path)
            db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Validate numeric ranges
        if config.min_quality_threshold < 0 or config.min_quality_threshold > 1:
            logger.warning(f"Invalid quality threshold: {config.min_quality_threshold}, using 0.6")
            config.min_quality_threshold = 0.6
        
        if config.connection_pool_size < 1:
            logger.warning(f"Invalid pool size: {config.connection_pool_size}, using 5")
            config.connection_pool_size = 5
        
        if config.cache_size_mb < 1:
            logger.warning(f"Invalid cache size: {config.cache_size_mb}, using 32")
            config.cache_size_mb = 32
        
        # Validate retention policies
        if config.auto_archive_after_days < 7:
            logger.warning(f"Archive period too short: {config.auto_archive_after_days}, using 30")
            config.auto_archive_after_days = 30
        
        if config.auto_delete_after_days <= config.auto_archive_after_days:
            logger.warning("Delete period should be longer than archive period")
            config.auto_delete_after_days = config.auto_archive_after_days * 4
        
        logger.info("Configuration validation completed")
    
    def update_config(self, updates: Dict[str, Any]) -> bool:
        """Update configuration at runtime"""
        try:
            config = self.get_config()
            
            # Apply updates
            for key, value in updates.items():
                if hasattr(config, key):
                    setattr(config, key, value)
                    logger.debug(f"Updated config: {key} = {value}")
                else:
                    logger.warning(f"Unknown config key: {key}")
            
            # Re-validate after updates
            self._validate_configuration()
            
            logger.info(f"Configuration updated: {len(updates)} settings")
            return True
            
        except Exception as e:
            logger.error(f"Configuration update failed: {e}")
            return False
    
    def save_config_to_file(self, output_path: str) -> bool:
        """Save current configuration to file"""
        try:
            config = self.get_config()
            config_dict = {
                field.name: getattr(config, field.name)
                for field in config.__dataclass_fields__.values()
            }
            
            # Wrap in script_persistence section
            output_config = {"script_persistence": config_dict}
            
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                if output_path.suffix.lower() in ['.yml', '.yaml']:
                    yaml.dump(output_config, f, default_flow_style=False, indent=2)
                else:
                    json.dump(output_config, f, indent=2, default=str)
            
            logger.info(f"Configuration saved to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary for display"""
        config = self.get_config()
        
        return {
            "sources": self._config_sources,
            "core_settings": {
                "enabled": config.enabled,
                "auto_persist": config.auto_persist,
                "database_path": config.database_path,
                "quality_validation": config.quality_validation_enabled
            },
            "performance_settings": {
                "connection_pool_size": config.connection_pool_size,
                "cache_size_mb": config.cache_size_mb,
                "async_operations": config.enable_async_operations
            },
            "retention_policies": {
                "archive_after_days": config.auto_archive_after_days,
                "delete_after_days": config.auto_delete_after_days,
                "max_scripts_per_user": config.max_scripts_per_user
            },
            "analytics_settings": {
                "enabled": config.analytics_enabled,
                "cache_ttl_seconds": config.analytics_cache_ttl_seconds,
                "retention_days": config.analytics_retention_days
            },
            "feature_flags": {
                "full_text_search": config.enable_full_text_search,
                "performance_insights": config.enable_performance_insights,
                "comparative_analysis": config.enable_comparative_analysis,
                "real_time_metrics": config.enable_real_time_metrics
            }
        }


def create_default_config_file(output_path: str = "config/script_persistence.yml") -> bool:
    """Create a default configuration file with comments"""
    try:
        config_content = """
# Script Persistence System Configuration
script_persistence:
  # Core Settings
  enabled: true                    # Enable/disable the entire persistence system
  auto_persist: true              # Automatically persist generated scripts
  database_path: "data/scripts.db" # SQLite database file path
  
  # Quality Validation
  quality_validation_enabled: true # Enable quality validation before persistence
  min_quality_threshold: 0.6      # Minimum quality score for persistence
  enable_duplicate_detection: true # Detect and prevent duplicate scripts
  
  # Performance Settings
  connection_pool_size: 10         # Database connection pool size
  cache_size_mb: 64               # Database cache size in MB
  enable_async_operations: true   # Enable async database operations
  batch_insert_size: 100          # Batch size for bulk operations
  
  # Retention Policies
  auto_archive_after_days: 90     # Archive scripts after N days
  auto_delete_after_days: 365     # Delete archived scripts after N days
  max_scripts_per_user: 1000      # Maximum scripts per user
  
  # Analytics Settings
  analytics_enabled: true         # Enable analytics and reporting
  analytics_cache_ttl_seconds: 300 # Analytics cache TTL
  analytics_retention_days: 180   # Analytics data retention
  trend_analysis_enabled: true    # Enable trend analysis
  
  # Export Settings
  export_enabled: true            # Enable script export functionality
  max_export_batch_size: 500     # Maximum scripts per export
  export_timeout_seconds: 300    # Export operation timeout
  export_formats:                 # Supported export formats
    - "json"
    - "csv"
    - "html"
  
  # Integration Settings
  webhook_notifications_enabled: false # Enable webhook notifications
  webhook_url: null               # Webhook URL for notifications
  webhook_timeout_seconds: 10     # Webhook timeout
  
  # Observer Settings
  observer_enabled: true          # Enable script generation observer
  observer_auto_persist: true     # Auto-persist through observer
  observer_persist_threshold: 0.0 # Minimum quality for observer persistence
  
  # Analytics Benchmarks
  quality_benchmark: 0.7          # Quality score benchmark
  engagement_benchmark: 0.6       # Engagement score benchmark
  usage_benchmark: 0.3           # Usage rate benchmark
  generation_time_benchmark_ms: 5000 # Generation time benchmark
  
  # Feature Flags
  enable_full_text_search: true   # Enable full-text search
  enable_performance_insights: true # Enable performance insights
  enable_comparative_analysis: true # Enable comparative analysis
  enable_real_time_metrics: true  # Enable real-time metrics
  
  # Debug and Logging
  debug_mode: false               # Enable debug mode
  log_level: "INFO"              # Logging level
  log_database_operations: false # Log database operations
"""
        
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(config_content.strip())
        
        logger.info(f"Default configuration file created: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create default config file: {e}")
        return False


# Global configuration manager instance
_config_manager: Optional[ConfigurationManager] = None

def get_config_manager(config_file: Optional[str] = None) -> ConfigurationManager:
    """Get or create global configuration manager"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigurationManager(config_file)
    return _config_manager

def get_script_persistence_config() -> ScriptPersistenceSystemConfig:
    """Get script persistence configuration"""
    return get_config_manager().get_config()

def update_script_persistence_config(updates: Dict[str, Any]) -> bool:
    """Update script persistence configuration"""
    return get_config_manager().update_config(updates)