"""TTS Manager for concurrent audio synthesis and queue management

Implements multi-engine TTS integration with sentence-level concurrent synthesis,
priority queue management, and batch processing capabilities.
"""

import asyncio
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor
from queue import PriorityQueue
import time
from pathlib import Path
import tempfile
import uuid
import hashlib
import os
from loguru import logger

from ..models.audio import AudioChunk, AudioStatus, AudioFormat, AudioPriority
from ..core.config import cfg
from ..core.exceptions import ServiceError, TimeoutError
from ..models.constants import (
    MAX_TTS_LATENCY_MS,
    MAX_CONCURRENT_TTS_REQUESTS,
    TTS_CHUNK_SIZE_WORDS
)
from .factories import ServiceFactory
from .tts_engines.base import BaseTTSEngine
from .audio_streaming_proxy import get_audio_proxy


# Global TTS manager instance
_tts_manager: Optional['TTSManager'] = None


class TTSRequest:
    """TTS synthesis request with priority handling"""
    
    def __init__(self, 
                 chunk: AudioChunk, 
                 priority: AudioPriority,
                 voice: Optional[str] = None,
                 callback: Optional[Callable] = None):
        self.chunk = chunk
        self.priority = priority
        self.voice = voice
        self.callback = callback
        self.created_at = datetime.utcnow()
    
    def __lt__(self, other):
        # Lower priority value = higher priority in queue
        return self.priority.value < other.priority.value


class AudioCache:
    """Audio segment caching for TTS optimization"""
    
    def __init__(self, cache_dir: Optional[Path] = None, max_cache_size_mb: int = 500):
        """Initialize audio cache
        
        Args:
            cache_dir: Directory for cache storage (defaults to tests/audio_cache)
            max_cache_size_mb: Maximum cache size in megabytes
        """
        self.cache_dir = cache_dir or Path("tests") / "audio_cache"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_cache_size_mb = max_cache_size_mb
        self.max_cache_size_bytes = max_cache_size_mb * 1024 * 1024
        
        # Cache metadata
        self.cache_metadata: Dict[str, Dict[str, Any]] = {}
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Load existing cache metadata
        self._load_cache_metadata()
        
        logger.info(f"Audio cache initialized at {self.cache_dir}, max size: {max_cache_size_mb}MB")
    
    def get_cache_key(self, text: str, voice: Optional[str] = None, 
                     engine: Optional[str] = None) -> str:
        """Generate cache key for text and parameters
        
        Args:
            text: Text content to cache
            voice: Voice parameter (optional)
            engine: TTS engine name (optional)
            
        Returns:
            SHA256 hash as cache key
        """
        cache_input = f"{text}|{voice or 'default'}|{engine or 'default'}"
        return hashlib.sha256(cache_input.encode('utf-8')).hexdigest()
    
    def get_cached_audio(self, cache_key: str) -> Optional[AudioChunk]:
        """Retrieve cached audio chunk
        
        Args:
            cache_key: Cache key to retrieve
            
        Returns:
            AudioChunk if found and valid, None otherwise
        """
        try:
            if cache_key not in self.cache_metadata:
                self.cache_misses += 1
                return None
            
            metadata = self.cache_metadata[cache_key]
            cache_file = self.cache_dir / f"{cache_key}.wav"
            
            # Check if file exists and is not expired
            if not cache_file.exists():
                self._remove_cache_entry(cache_key)
                self.cache_misses += 1
                return None
            
            # Check expiration (default 24 hours)
            cache_age = datetime.utcnow() - datetime.fromisoformat(metadata['created_at'])
            max_age_hours = cfg.get_yaml_config('performance.tts.cache_max_age_hours', 24)
            
            if cache_age.total_seconds() > max_age_hours * 3600:
                self._remove_cache_entry(cache_key)
                self.cache_misses += 1
                return None
            
            # Create AudioChunk from cached file
            audio_chunk = AudioChunk(
                chunk_id=metadata['chunk_id'],
                text=metadata['text'],
                audio_file_path=str(cache_file),
                duration=metadata['duration'],
                format=AudioFormat(metadata['format']),
                status=AudioStatus.READY,
                created_at=datetime.fromisoformat(metadata['created_at']),
                priority=AudioPriority(metadata['priority'])
            )
            
            # Update access time
            metadata['last_accessed'] = datetime.utcnow().isoformat()
            metadata['access_count'] += 1
            
            self.cache_hits += 1
            logger.debug(f"Cache hit for key {cache_key[:8]}...")
            return audio_chunk
            
        except Exception as e:
            logger.error(f"Failed to retrieve cached audio {cache_key}: {e}")
            self.cache_misses += 1
            return None
    
    def cache_audio_segment(self, cache_key: str, audio_chunk: AudioChunk, 
                          voice: Optional[str] = None, engine: Optional[str] = None) -> bool:
        """Cache audio segment
        
        Args:
            cache_key: Cache key for storage
            audio_chunk: AudioChunk to cache
            voice: Voice parameter used
            engine: TTS engine used
            
        Returns:
            True if cached successfully, False otherwise
        """
        try:
            # Check cache size before adding
            if self._get_cache_size_bytes() > self.max_cache_size_bytes:
                self._cleanup_old_entries()
            
            # Copy audio file to cache directory
            cache_file = self.cache_dir / f"{cache_key}.wav"
            if hasattr(audio_chunk, 'audio_file_path') and audio_chunk.audio_file_path:
                source_path = Path(audio_chunk.audio_file_path)
                if source_path.exists():
                    # Copy file to cache
                    import shutil
                    shutil.copy2(source_path, cache_file)
                else:
                    logger.warning(f"Source audio file not found: {source_path}")
                    return False
            else:
                logger.warning(f"AudioChunk has no file path to cache")
                return False
            
            # Store metadata
            self.cache_metadata[cache_key] = {
                'chunk_id': audio_chunk.chunk_id,
                'text': audio_chunk.text,
                'duration': audio_chunk.duration,
                'format': audio_chunk.format.value,
                'priority': audio_chunk.priority.value,
                'voice': voice,
                'engine': engine,
                'created_at': datetime.utcnow().isoformat(),
                'last_accessed': datetime.utcnow().isoformat(),
                'access_count': 0,
                'file_size': cache_file.stat().st_size
            }
            
            # Save metadata
            self._save_cache_metadata()
            
            logger.debug(f"Cached audio segment with key {cache_key[:8]}...")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache audio segment {cache_key}: {e}")
            return False
    
    def cleanup_expired_cache(self) -> int:
        """Clean up expired cache entries
        
        Returns:
            Number of entries removed
        """
        try:
            removed_count = 0
            max_age_hours = cfg.get_yaml_config('performance.tts.cache_max_age_hours', 24)
            current_time = datetime.utcnow()
            
            keys_to_remove = []
            for cache_key, metadata in self.cache_metadata.items():
                created_at = datetime.fromisoformat(metadata['created_at'])
                age = current_time - created_at
                
                if age.total_seconds() > max_age_hours * 3600:
                    keys_to_remove.append(cache_key)
            
            for cache_key in keys_to_remove:
                if self._remove_cache_entry(cache_key):
                    removed_count += 1
            
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} expired cache entries")
            
            return removed_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired cache: {e}")
            return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics
        
        Returns:
            Dictionary with cache statistics
        """
        cache_size_mb = self._get_cache_size_bytes() / (1024 * 1024)
        hit_rate = (self.cache_hits / (self.cache_hits + self.cache_misses) * 100 
                   if (self.cache_hits + self.cache_misses) > 0 else 0)
        
        return {
            'total_entries': len(self.cache_metadata),
            'cache_size_mb': round(cache_size_mb, 2),
            'max_cache_size_mb': self.max_cache_size_mb,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate_percent': round(hit_rate, 2),
            'cache_directory': str(self.cache_dir)
        }
    
    def _load_cache_metadata(self) -> None:
        """Load cache metadata from file"""
        try:
            metadata_file = self.cache_dir / "cache_metadata.json"
            if metadata_file.exists():
                import json
                with open(metadata_file, 'r') as f:
                    self.cache_metadata = json.load(f)
                logger.debug(f"Loaded {len(self.cache_metadata)} cache entries")
        except Exception as e:
            logger.warning(f"Failed to load cache metadata: {e}")
            self.cache_metadata = {}
    
    def _save_cache_metadata(self) -> None:
        """Save cache metadata to file"""
        try:
            metadata_file = self.cache_dir / "cache_metadata.json"
            import json
            with open(metadata_file, 'w') as f:
                json.dump(self.cache_metadata, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save cache metadata: {e}")
    
    def _remove_cache_entry(self, cache_key: str) -> bool:
        """Remove cache entry and associated file"""
        try:
            # Remove file
            cache_file = self.cache_dir / f"{cache_key}.wav"
            if cache_file.exists():
                cache_file.unlink()
            
            # Remove metadata
            if cache_key in self.cache_metadata:
                del self.cache_metadata[cache_key]
                self._save_cache_metadata()
            
            return True
        except Exception as e:
            logger.error(f"Failed to remove cache entry {cache_key}: {e}")
            return False
    
    def _get_cache_size_bytes(self) -> int:
        """Get total cache size in bytes"""
        total_size = 0
        for metadata in self.cache_metadata.values():
            total_size += metadata.get('file_size', 0)
        return total_size
    
    def _cleanup_old_entries(self) -> None:
        """Remove oldest/least accessed entries to free space"""
        try:
            # Sort by last accessed time and access count
            sorted_entries = sorted(
                self.cache_metadata.items(),
                key=lambda x: (x[1]['last_accessed'], x[1]['access_count'])
            )
            
            # Remove oldest 20% of entries
            entries_to_remove = max(1, len(sorted_entries) // 5)
            
            for i in range(entries_to_remove):
                cache_key = sorted_entries[i][0]
                self._remove_cache_entry(cache_key)
            
            logger.info(f"Cleaned up {entries_to_remove} old cache entries for space")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old cache entries: {e}")


class TTSManager:
    """Enhanced TTS manager supporting multiple engines"""
    
    def __init__(self):
        """Initialize TTS manager with configurable engine support"""
        self.engine: Optional[BaseTTSEngine] = None
        self.is_initialized = False
        
        # Audio caching
        cache_enabled = cfg.get_yaml_config('performance.tts.cache_enabled', True)
        if cache_enabled:
            max_cache_size = cfg.get_yaml_config('performance.tts.cache_max_size_mb', 500)
            self.audio_cache = AudioCache(max_cache_size_mb=max_cache_size)
        else:
            self.audio_cache = None
        
        # Queue management
        self.request_queue = PriorityQueue()
        self.processing_tasks = []
        self.max_concurrent = cfg.get_yaml_config(
            'performance.tts.max_concurrent_requests', 
            MAX_CONCURRENT_TTS_REQUESTS
        )
        self.queue_timeout = cfg.get_yaml_config(
            'performance.tts.queue_timeout_seconds', 
            30
        )
        
        # Performance tracking
        self.stats = {
            "requests_processed": 0,
            "requests_failed": 0,
            "total_processing_time": 0.0,
            "average_latency_ms": 0.0,
            "queue_size": 0,
            "engine_switches": 0
        }
        
        # Buffer management
        self.audio_chunks = {}  # chunk_id -> AudioChunk
        self.buffer_size_seconds = cfg.get_yaml_config(
            'performance.tts.buffer_size_seconds', 
            5
        )
        
        # Audio streaming support
        self.streaming_enabled = cfg.get_yaml_config('audio_streaming.client_streaming.buffer_size', 0) > 0
        self.audio_proxy = get_audio_proxy() if self.streaming_enabled else None
        
        logger.info(f"TTS Manager initialized with multi-engine support, cache: {'enabled' if self.audio_cache else 'disabled'}, streaming: {'enabled' if self.streaming_enabled else 'disabled'}")
    
    async def initialize(self, engine_name: Optional[str] = None) -> None:
        """Initialize the TTS manager with specified engine
        
        Args:
            engine_name: Name of TTS engine to use (defaults to configured engine)
            
        Raises:
            ServiceError: If initialization fails
        """
        try:
            # Create TTS engine using factory - fail-fast principle
            self.engine = await ServiceFactory.create_tts_engine(
                engine_name=engine_name
            )
            
            # Start processing tasks
            await self._start_processing_tasks()
            
            self.is_initialized = True
            logger.info(f"TTS Manager initialized with engine: {self.engine.engine_name}")
            
        except Exception as e:
            logger.error(f"TTS Manager initialization failed: {e}")
            raise ServiceError(f"TTS Manager initialization failed: {e}")
    
    async def switch_engine(self, engine_name: str) -> None:
        """Switch to a different TTS engine
        
        Args:
            engine_name: Name of the new TTS engine
            
        Raises:
            ServiceError: If engine switch fails
        """
        try:
            logger.info(f"Switching TTS engine to: {engine_name}")
            
            # Clean up current engine
            if self.engine:
                await self.engine.cleanup()
            
            # Create new engine - fail-fast principle
            self.engine = await ServiceFactory.create_tts_engine(
                engine_name=engine_name
            )
            
            self.stats["engine_switches"] += 1
            logger.info(f"Successfully switched to TTS engine: {engine_name}")
            
        except Exception as e:
            logger.error(f"Engine switch failed: {e}")
            raise ServiceError(f"Engine switch failed: {e}")
    
    async def synthesize_text(
        self, 
        text: str, 
        priority: AudioPriority = AudioPriority.NORMAL,
        voice: Optional[str] = None,
        streaming: bool = None,
        callback: Optional[Callable] = None
    ) -> List[AudioChunk]:
        """Synthesize text to audio chunks
        
        Args:
            text: Text to synthesize
            priority: Synthesis priority
            voice: Voice to use (optional)
            streaming: Whether to use streaming synthesis (auto-detect if None)
            callback: Callback function for completion
            
        Returns:
            List of AudioChunk objects
            
        Raises:
            ServiceError: If synthesis fails
        """
        if not self.is_initialized:
            await self.initialize()
        
        if not text.strip():
            return []
        
        try:
            # Check cache first if enabled
            if self.audio_cache:
                cache_key = self.audio_cache.get_cache_key(
                    text, voice, self.engine.__class__.__name__ if self.engine else None
                )
                cached_audio = self.audio_cache.get_cached_audio(cache_key)
                if cached_audio:
                    logger.debug(f"Using cached audio for text: {text[:50]}...")
                    return [cached_audio]
            
            # Determine if streaming should be used
            if streaming is None:
                streaming = (
                    self.engine.supports_streaming and
                    cfg.get_yaml_config('features.tts.enable_streaming', True)
                )
            
            # Split text into manageable chunks if needed
            text_chunks = self._split_text(text)
            audio_chunks = []
            
            for chunk_text in text_chunks:
                # Check cache for each chunk if enabled
                chunk_cached = False
                if self.audio_cache:
                    chunk_cache_key = self.audio_cache.get_cache_key(
                        chunk_text, voice, self.engine.__class__.__name__ if self.engine else None
                    )
                    cached_chunk = self.audio_cache.get_cached_audio(chunk_cache_key)
                    if cached_chunk:
                        audio_chunks.append(cached_chunk)
                        chunk_cached = True
                        logger.debug(f"Using cached audio chunk: {chunk_text[:30]}...")
                
                if not chunk_cached:
                    # Synthesize the chunk
                    if streaming and self.engine.supports_streaming:
                        # Use streaming synthesis
                        async for audio_chunk in self.engine.synthesize_streaming(
                            chunk_text, voice=voice
                        ):
                            audio_chunks.append(audio_chunk)
                            self._update_stats_success(audio_chunk)
                            
                            # Cache the chunk if caching is enabled
                            if self.audio_cache:
                                chunk_cache_key = self.audio_cache.get_cache_key(
                                    chunk_text, voice, self.engine.__class__.__name__ if self.engine else None
                                )
                                self.audio_cache.cache_audio_segment(
                                    chunk_cache_key, audio_chunk, voice, 
                                    self.engine.__class__.__name__ if self.engine else None
                                )
                            
                            if callback:
                                await self._safe_callback(callback, audio_chunk)
                    else:
                        # Use regular synthesis
                        audio_chunk = await self.engine.synthesize(
                            chunk_text, voice=voice
                        )
                        audio_chunks.append(audio_chunk)
                        self._update_stats_success(audio_chunk)
                        
                        # Cache the chunk if caching is enabled
                        if self.audio_cache:
                            chunk_cache_key = self.audio_cache.get_cache_key(
                                chunk_text, voice, self.engine.__class__.__name__ if self.engine else None
                            )
                            self.audio_cache.cache_audio_segment(
                                chunk_cache_key, audio_chunk, voice,
                                self.engine.__class__.__name__ if self.engine else None
                            )
                        
                        if callback:
                            await self._safe_callback(callback, audio_chunk)
            
            logger.debug(f"Synthesized {len(text)} chars into {len(audio_chunks)} chunks")
            return audio_chunks
            
        except Exception as e:
            self.stats["requests_failed"] += 1
            logger.error(f"Text synthesis failed: {e}")
            raise ServiceError(f"Text synthesis failed: {e}")
    
    async def queue_synthesis_request(
        self, 
        text: str, 
        priority: AudioPriority = AudioPriority.NORMAL,
        voice: Optional[str] = None,
        callback: Optional[Callable] = None
    ) -> str:
        """Queue a synthesis request for background processing
        
        Args:
            text: Text to synthesize
            priority: Request priority
            voice: Voice to use
            callback: Completion callback
            
        Returns:
            Request ID for tracking
        """
        if not self.is_initialized:
            await self.initialize()
        
        # Create audio chunk placeholder
        chunk_id = str(uuid.uuid4())
        audio_chunk = AudioChunk(
            chunk_id=chunk_id,
            text=text,
            status=AudioStatus.PENDING,
            priority=priority
        )
        
        # Create request
        request = TTSRequest(
            chunk=audio_chunk,
            priority=priority,
            voice=voice,
            callback=callback
        )
        
        # Add to queue
        self.request_queue.put(request)
        self.stats["queue_size"] = self.request_queue.qsize()
        
        logger.debug(f"Queued synthesis request: {chunk_id}")
        return chunk_id
    
    def _split_text(self, text: str) -> List[str]:
        """Split text into optimal chunks for synthesis
        
        Args:
            text: Text to split
            
        Returns:
            List of text chunks
        """
        chunk_size = cfg.get_yaml_config(
            'performance.tts.chunk_size_words', 
            TTS_CHUNK_SIZE_WORDS
        )
        
        # Simple word-based chunking
        words = text.split()
        chunks = []
        
        for i in range(0, len(words), chunk_size):
            chunk = ' '.join(words[i:i + chunk_size])
            chunks.append(chunk)
        
        return chunks if chunks else [text]
    
    async def _start_processing_tasks(self) -> None:
        """Start background processing tasks"""
        for i in range(self.max_concurrent):
            task = asyncio.create_task(self._process_queue())
            self.processing_tasks.append(task)
        
        logger.info(f"Started {self.max_concurrent} TTS processing tasks")
    
    async def _process_queue(self) -> None:
        """Background task to process synthesis queue"""
        while True:
            try:
                # Get request from queue (with timeout)
                request = await asyncio.get_event_loop().run_in_executor(
                    None, self._get_request_with_timeout
                )
                
                if request is None:
                    # Timeout occurred, continue
                    await asyncio.sleep(0.1)
                    continue
                
                # Process the request
                await self._process_request(request)
                
            except Exception as e:
                logger.error(f"Queue processing error: {e}")
                await asyncio.sleep(1.0)  # Brief pause on error
    
    def _get_request_with_timeout(self) -> Optional[TTSRequest]:
        """Get request from queue with timeout"""
        try:
            return self.request_queue.get(timeout=1.0)
        except:
            return None
    
    async def _process_request(self, request: TTSRequest) -> None:
        """Process a single synthesis request
        
        Args:
            request: TTS request to process
        """
        try:
            start_time = time.time()
            
            # Update chunk status
            request.chunk.status = AudioStatus.PROCESSING
            
            # Perform synthesis
            audio_chunks = await self.synthesize_text(
                text=request.chunk.text,
                priority=request.priority,
                voice=request.voice,
                callback=None  # Don't double-call callback
            )
            
            # Update chunk with results
            if audio_chunks:
                # Use first chunk as primary result
                primary_chunk = audio_chunks[0]
                request.chunk.file_path = primary_chunk.file_path
                request.chunk.duration_ms = primary_chunk.duration_ms
                request.chunk.format = primary_chunk.format
                request.chunk.sample_rate = primary_chunk.sample_rate
                request.chunk.status = AudioStatus.COMPLETED
                
                # Store all chunks
                for chunk in audio_chunks:
                    self.audio_chunks[chunk.chunk_id] = chunk
            else:
                request.chunk.status = AudioStatus.FAILED
            
            # Update processing time
            processing_time = time.time() - start_time
            self.stats["total_processing_time"] += processing_time
            
            # Call callback if provided
            if request.callback:
                await self._safe_callback(request.callback, request.chunk)
            
            self.stats["requests_processed"] += 1
            self.stats["queue_size"] = self.request_queue.qsize()
            
        except Exception as e:
            logger.error(f"Request processing failed: {e}")
            request.chunk.status = AudioStatus.FAILED
            self.stats["requests_failed"] += 1
    
    async def _safe_callback(self, callback: Callable, *args) -> None:
        """Safely execute callback function"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(*args)
            else:
                callback(*args)
        except Exception as e:
            logger.error(f"Callback execution failed: {e}")
    
    def _update_stats_success(self, audio_chunk: AudioChunk) -> None:
        """Update statistics for successful synthesis"""
        if audio_chunk.duration_ms > 0:
            # Estimate synthesis speed (chars per second)
            chars_per_sec = len(audio_chunk.text) / (audio_chunk.duration_ms / 1000.0)
            
            # Update average latency (rough estimate)
            if self.stats["requests_processed"] > 0:
                current_avg = self.stats["average_latency_ms"]
                new_latency = audio_chunk.duration_ms / len(audio_chunk.text) * 100  # ms per 100 chars
                self.stats["average_latency_ms"] = (
                    (current_avg * self.stats["requests_processed"] + new_latency) /
                    (self.stats["requests_processed"] + 1)
                )
    
    async def get_voices(self) -> List[Dict[str, str]]:
        """Get available voices from current engine
        
        Returns:
            List of voice information dictionaries
        """
        if not self.engine:
            await self.initialize()
        
        return await self.engine.get_voices()
    
    async def cleanup(self) -> None:
        """Clean up TTS manager resources"""
        logger.info("Cleaning up TTS Manager")
        
        # Cancel processing tasks
        for task in self.processing_tasks:
            task.cancel()
        
        if self.processing_tasks:
            await asyncio.gather(*self.processing_tasks, return_exceptions=True)
        
        # Clean up engine
        if self.engine:
            await self.engine.cleanup()
        
        # Clear audio chunks
        self.audio_chunks.clear()
        
        self.is_initialized = False
        logger.info("TTS Manager cleanup completed")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get TTS manager statistics including cache stats
        
        Returns:
            Dictionary of statistics
        """
        stats = {
            **self.stats,
            "engine_name": self.engine.engine_name if self.engine else None,
            "supports_streaming": self.engine.supports_streaming if self.engine else False,
            "is_initialized": self.is_initialized
        }
        
        # Add cache statistics if cache is enabled
        if self.audio_cache:
            stats["cache"] = self.audio_cache.get_cache_stats()
        else:
            stats["cache"] = {"enabled": False}
        
        return stats
    
    def cleanup_cache(self) -> int:
        """Clean up expired cache entries
        
        Returns:
            Number of entries removed, 0 if cache is disabled
        """
        if self.audio_cache:
            return self.audio_cache.cleanup_expired_cache()
        return 0
    
    def clear_cache(self) -> bool:
        """Clear all cache entries
        
        Returns:
            True if cache was cleared, False if cache is disabled
        """
        if self.audio_cache:
            try:
                # Remove all cache files
                for cache_key in list(self.audio_cache.cache_metadata.keys()):
                    self.audio_cache._remove_cache_entry(cache_key)
                
                logger.info("Audio cache cleared successfully")
                return True
            except Exception as e:
                logger.error(f"Failed to clear audio cache: {e}")
                return False
        return False
    
    async def start_audio_streaming(self, texts: List[str], voice_config: Optional[Dict[str, Any]] = None) -> bool:
        """启动音频流播放
        
        Args:
            texts: 要播放的文本列表
            voice_config: 音色配置
            
        Returns:
            是否成功启动
        """
        if not self.streaming_enabled or not self.audio_proxy:
            logger.warning("Audio streaming not enabled")
            return False
            
        try:
            # 使用默认配置或提供的配置
            default_config = {
                'model': cfg.get_yaml_config('audio_streaming.cosyvoice_websocket.model', 'cosyvoice-v2'),
                'voice': cfg.get_yaml_config('audio_streaming.cosyvoice_websocket.default_voice', 'longanran'),
                'format': cfg.get_yaml_config('audio_streaming.cosyvoice_websocket.default_format', 'opus'),
                'sample_rate': cfg.get_yaml_config('audio_streaming.cosyvoice_websocket.default_sample_rate', 24000),
                'volume': cfg.get_yaml_config('audio_streaming.cosyvoice_websocket.default_volume', 50),
                'rate': cfg.get_yaml_config('audio_streaming.cosyvoice_websocket.default_rate', 1.0),
                'pitch': cfg.get_yaml_config('audio_streaming.cosyvoice_websocket.default_pitch', 1.0)
            }
            
            if voice_config:
                default_config.update(voice_config)
            
            # 启动音频流
            session_id = f"tts_manager_{int(time.time())}"
            await self.audio_proxy.start_streaming(texts, default_config, session_id)
            
            logger.info(f"Audio streaming started with session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start audio streaming: {e}")
            return False
    
    async def stop_audio_streaming(self) -> bool:
        """停止音频流播放
        
        Returns:
            是否成功停止
        """
        if not self.streaming_enabled or not self.audio_proxy:
            return False
            
        try:
            await self.audio_proxy.stop_streaming()
            logger.info("Audio streaming stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop audio streaming: {e}")
            return False
    
    def get_streaming_status(self) -> Dict[str, Any]:
        """获取音频流状态
        
        Returns:
            音频流状态信息
        """
        if not self.streaming_enabled or not self.audio_proxy:
            return {
                "enabled": False,
                "status": "disabled"
            }
            
        return {
            "enabled": True,
            "status": self.audio_proxy.get_status()
        }


def get_tts_manager() -> TTSManager:
    """Get global TTS manager instance"""
    global _tts_manager
    if _tts_manager is None:
        _tts_manager = TTSManager()
    return _tts_manager


async def initialize_tts_manager():
    """Initialize and start the global TTS manager"""
    manager = get_tts_manager()
    await manager.initialize()


async def cleanup_tts_manager():
    """Stop and cleanup the global TTS manager"""
    global _tts_manager
    if _tts_manager:
        await _tts_manager.cleanup()
        _tts_manager = None