"""
Script Persistence Manager

Central coordinator for script persistence operations, providing a high-level
interface for script storage, retrieval, analytics, and lifecycle management.
"""

import asyncio
import uuid
from typing import Dict, List, Optional, Any, Tuple, Callable
from datetime import datetime, timedelta
from dataclasses import asdict
from loguru import logger

from ..models.script_persistence import (
    PersistedScript, PersistedScriptSegment, StreamingSession,
    ScriptQuery, ScriptAnalytics, GenerationMethod, ScriptStatus,
    ScriptValidationResult, PersistenceConfig
)
from ..services.script_repository import ScriptRepository
from ..services.script_previewer import PreviewResult, ScriptTimeline, ScriptSegment
from ..models.forms import OperationalForm
from ..core.exceptions import ServiceError
from ..core.config import cfg


class ScriptPersistenceManager:
    """
    High-level service for managing script persistence lifecycle
    
    Provides event-driven integration with existing script generation,
    comprehensive analytics, and non-invasive persistence operations.
    """
    
    def __init__(self, config: Optional[PersistenceConfig] = None):
        self.config = config or PersistenceConfig()
        self.repository = ScriptRepository(self.config)
        self._observers: List[Callable] = []
        self._quality_validators: List[Callable] = []
        
        # Statistics tracking
        self.stats = {
            "scripts_persisted": 0,
            "persistence_errors": 0,
            "average_persistence_time_ms": 0.0,
            "last_persistence_at": None,
            "validation_failures": 0,
            "analytics_requests": 0
        }
        
        logger.info("Script Persistence Manager initialized")
    
    # === Core Persistence Operations ===
    
    async def persist_script_from_preview(
        self, 
        preview_result: PreviewResult, 
        form: OperationalForm,
        created_by: Optional[str] = None
    ) -> Optional[str]:
        """
        Persist a script from ScriptPreviewer result
        
        Args:
            preview_result: Result from script generation
            form: Original operational form
            created_by: User who created the script
            
        Returns:
            Script ID if successful, None if failed
        """
        if not preview_result.success or not preview_result.script_timeline:
            logger.warning(f"Cannot persist failed preview result for form {preview_result.form_id}")
            return None
        
        start_time = datetime.now()
        
        try:
            # Generate unique script ID
            script_id = self._generate_script_id(preview_result.form_id)
            
            # Convert to persistent format
            persisted_script = await self._convert_preview_to_persistent_script(
                script_id, preview_result, form, created_by
            )
            
            # Validate script quality if enabled
            if self.config.enable_quality_validation:
                validation_result = await self._validate_script_quality(persisted_script)
                if not validation_result.is_valid:
                    self.stats["validation_failures"] += 1
                    if validation_result.quality_score < self.config.min_quality_threshold:
                        logger.warning(f"Script {script_id} quality below threshold: {validation_result.quality_score}")
                        return None
                
                persisted_script.quality_score = validation_result.quality_score
            
            # Save to repository
            success = await self.repository.save_script(persisted_script)
            
            if success:
                # Update statistics
                persistence_time = (datetime.now() - start_time).total_seconds() * 1000
                self._update_persistence_stats(persistence_time, success=True)
                
                # Notify observers
                await self._notify_observers('script_persisted', {
                    'script_id': script_id,
                    'form_id': preview_result.form_id,
                    'generation_method': persisted_script.generation_method,
                    'persistence_time_ms': persistence_time
                })
                
                logger.info(f"Successfully persisted script {script_id} from form {preview_result.form_id}")
                return script_id
            else:
                self._update_persistence_stats(0, success=False)
                logger.error(f"Failed to save script {script_id} to repository")
                return None
                
        except Exception as e:
            self._update_persistence_stats(0, success=False)
            logger.error(f"Script persistence failed for form {preview_result.form_id}: {e}")
            return None
    
    async def get_script(self, script_id: str) -> Optional[PersistedScript]:
        """Retrieve a script by ID"""
        try:
            return await self.repository.get_script(script_id)
        except Exception as e:
            logger.error(f"Failed to retrieve script {script_id}: {e}")
            return None
    
    async def search_scripts(
        self, 
        query: Optional[ScriptQuery] = None,
        text_query: Optional[str] = None
    ) -> Tuple[List[PersistedScript], int]:
        """
        Search scripts with flexible filtering
        
        Args:
            query: Structured query parameters
            text_query: Full-text search query
            
        Returns:
            Tuple of (scripts, total_count)
        """
        try:
            if text_query:
                # Perform full-text search
                scripts = await self.repository.full_text_search(text_query, limit=query.limit if query else 20)
                return scripts, len(scripts)
            else:
                # Structured search
                query = query or ScriptQuery()
                return await self.repository.search_scripts(query)
                
        except Exception as e:
            logger.error(f"Script search failed: {e}")
            return [], 0
    
    async def track_script_usage(
        self, 
        script_id: str, 
        session_data: Dict[str, Any]
    ) -> bool:
        """
        Track script usage in a streaming session
        
        Args:
            script_id: ID of the used script
            session_data: Session metrics and metadata
            
        Returns:
            True if tracking successful
        """
        try:
            # Convert session data to StreamingSession object
            session = self._create_streaming_session(script_id, session_data)
            
            # Update repository
            success = await self.repository.update_script_usage(script_id, session)
            
            if success:
                await self._notify_observers('script_used', {
                    'script_id': script_id,
                    'session_id': session.session_id,
                    'engagement_score': session.engagement_score
                })
                
                logger.debug(f"Tracked usage for script {script_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to track usage for script {script_id}: {e}")
            return False
    
    async def delete_script(self, script_id: str, soft_delete: bool = True) -> bool:
        """Delete a script"""
        try:
            success = await self.repository.delete_script(script_id, soft_delete)
            
            if success:
                await self._notify_observers('script_deleted', {
                    'script_id': script_id,
                    'soft_delete': soft_delete
                })
                
                logger.info(f"{'Soft' if soft_delete else 'Hard'} deleted script {script_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to delete script {script_id}: {e}")
            return False
    
    # === Analytics and Reporting ===
    
    async def get_analytics(self, days: int = 30) -> ScriptAnalytics:
        """Get comprehensive script analytics"""
        try:
            self.stats["analytics_requests"] += 1
            analytics = await self.repository.get_analytics(days)
            
            logger.debug(f"Generated analytics for {days} days: {analytics.total_scripts} scripts")
            return analytics
            
        except Exception as e:
            logger.error(f"Failed to get analytics: {e}")
            # Return empty analytics instead of failing
            return ScriptAnalytics(
                total_scripts=0, scripts_by_method={}, average_generation_time_ms=0.0,
                generation_success_rate=0.0, total_sessions=0, total_usage_minutes=0,
                average_session_duration_minutes=0.0, scripts_reuse_rate=0.0,
                average_quality_score=0.0, quality_distribution={}, top_performing_scripts=[],
                average_engagement_score=0.0, average_interaction_rate=0.0, average_retention_rate=0.0,
                generation_trend_7d=[0]*7, usage_trend_7d=[0]*7, quality_trend_7d=[0.0]*7,
                generation_efficiency_score=0.0, content_utilization_rate=0.0, user_satisfaction_score=0.0
            )
    
    async def get_script_performance_report(self, script_id: str) -> Dict[str, Any]:
        """Get detailed performance report for a specific script"""
        try:
            script = await self.repository.get_script(script_id)
            if not script:
                return {"error": "Script not found"}
            
            # Calculate performance metrics
            usage_efficiency = min(1.0, script.usage_count / 10.0)  # Target: 10 uses
            quality_score = script.quality_score or 0.0
            
            # Get recent usage data (would need additional repository method)
            recent_sessions = []  # TODO: Implement get_recent_sessions
            
            avg_engagement = 0.0
            if recent_sessions:
                avg_engagement = sum(s.engagement_score for s in recent_sessions) / len(recent_sessions)
            
            return {
                "script_id": script_id,
                "basic_info": {
                    "form_id": script.form_id,
                    "generated_at": script.generated_at.isoformat(),
                    "generation_method": script.generation_method.value,
                    "status": script.status.value,
                    "total_duration_seconds": script.total_duration_seconds,
                    "segment_count": script.segment_count
                },
                "usage_metrics": {
                    "usage_count": script.usage_count,
                    "last_used_at": script.last_used_at.isoformat() if script.last_used_at else None,
                    "usage_efficiency_score": usage_efficiency
                },
                "quality_metrics": {
                    "quality_score": quality_score,
                    "average_engagement": avg_engagement,
                    "performance_rating": self._calculate_performance_rating(quality_score, usage_efficiency, avg_engagement)
                },
                "segments_summary": [
                    {
                        "segment_id": seg.segment_id,
                        "type": seg.segment_type,
                        "title": seg.title,
                        "duration_seconds": seg.duration_seconds,
                        "usage_count": seg.usage_count
                    }
                    for seg in script.segments
                ],
                "recommendations": self._generate_performance_recommendations(script, usage_efficiency, quality_score)
            }
            
        except Exception as e:
            logger.error(f"Failed to generate performance report for {script_id}: {e}")
            return {"error": str(e)}
    
    # === Batch Operations ===
    
    async def batch_persist_scripts(
        self, 
        preview_results: List[Tuple[PreviewResult, OperationalForm]], 
        created_by: Optional[str] = None
    ) -> List[Optional[str]]:
        """Persist multiple scripts in batch"""
        try:
            logger.info(f"Starting batch persistence of {len(preview_results)} scripts")
            
            # Convert all to persistent format
            persistent_scripts = []
            script_ids = []
            
            for preview_result, form in preview_results:
                if preview_result.success and preview_result.script_timeline:
                    script_id = self._generate_script_id(preview_result.form_id)
                    
                    persisted_script = await self._convert_preview_to_persistent_script(
                        script_id, preview_result, form, created_by
                    )
                    
                    persistent_scripts.append(persisted_script)
                    script_ids.append(script_id)
                else:
                    script_ids.append(None)
            
            # Batch save to repository
            saved_count = await self.repository.batch_save_scripts(persistent_scripts)
            
            logger.info(f"Batch persistence completed: {saved_count}/{len(persistent_scripts)} scripts saved")
            
            # Notify observers
            await self._notify_observers('batch_scripts_persisted', {
                'total_attempted': len(preview_results),
                'successfully_saved': saved_count,
                'script_ids': [sid for sid in script_ids if sid is not None]
            })
            
            return script_ids
            
        except Exception as e:
            logger.error(f"Batch persistence failed: {e}")
            return [None] * len(preview_results)
    
    # === Event System ===
    
    def add_observer(self, callback: Callable[[str, Dict[str, Any]], None]) -> None:
        """Add an observer for persistence events"""
        self._observers.append(callback)
        logger.debug(f"Added persistence observer: {callback.__name__}")
    
    def remove_observer(self, callback: Callable) -> None:
        """Remove an observer"""
        if callback in self._observers:
            self._observers.remove(callback)
            logger.debug(f"Removed persistence observer: {callback.__name__}")
    
    async def _notify_observers(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """Notify all observers of an event"""
        for observer in self._observers:
            try:
                if asyncio.iscoroutinefunction(observer):
                    await observer(event_type, event_data)
                else:
                    observer(event_type, event_data)
            except Exception as e:
                logger.warning(f"Observer {observer.__name__} failed: {e}")
    
    # === Quality Validation ===
    
    def add_quality_validator(self, validator: Callable[[PersistedScript], ScriptValidationResult]) -> None:
        """Add a quality validator"""
        self._quality_validators.append(validator)
        logger.debug(f"Added quality validator: {validator.__name__}")
    
    async def _validate_script_quality(self, script: PersistedScript) -> ScriptValidationResult:
        """Validate script quality using all registered validators"""
        try:
            # Built-in basic validation
            validation_result = self._basic_quality_validation(script)
            
            # Run custom validators
            for validator in self._quality_validators:
                try:
                    custom_result = validator(script)
                    # Combine results (take minimum quality score)
                    validation_result.quality_score = min(
                        validation_result.quality_score, 
                        custom_result.quality_score
                    )
                    validation_result.validation_warnings.extend(custom_result.validation_warnings)
                    validation_result.validation_errors.extend(custom_result.validation_errors)
                    validation_result.improvement_suggestions.extend(custom_result.improvement_suggestions)
                    
                except Exception as e:
                    logger.warning(f"Quality validator {validator.__name__} failed: {e}")
                    validation_result.validation_warnings.append(f"Validator error: {str(e)}")
            
            # Final validation
            validation_result.is_valid = (
                len(validation_result.validation_errors) == 0 and
                validation_result.quality_score >= self.config.min_quality_threshold
            )
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Quality validation failed: {e}")
            return ScriptValidationResult(
                is_valid=False,
                quality_score=0.0,
                validation_warnings=[],
                validation_errors=[f"Validation system error: {str(e)}"],
                content_length_score=0.0,
                coherence_score=0.0,
                engagement_score=0.0,
                technical_score=0.0,
                improvement_suggestions=[],
                estimated_performance_impact={}
            )
    
    def _basic_quality_validation(self, script: PersistedScript) -> ScriptValidationResult:
        """Basic built-in quality validation"""
        warnings = []
        errors = []
        suggestions = []
        
        # Content length validation
        total_content_length = sum(len(seg.content) for seg in script.segments)
        content_length_score = min(1.0, total_content_length / 2000)  # Target: 2000+ chars
        
        if total_content_length < 500:
            errors.append("Script content too short (< 500 characters)")
        elif total_content_length < 1000:
            warnings.append("Script content relatively short (< 1000 characters)")
            suggestions.append("Consider adding more detailed content for better engagement")
        
        # Segment validation
        coherence_score = 1.0
        if len(script.segments) < 3:
            warnings.append("Script has very few segments")
            coherence_score = 0.7
            suggestions.append("Add more segments for better content structure")
        
        # Duration validation
        duration_minutes = script.total_duration_seconds / 60
        if duration_minutes < 5:
            warnings.append("Script duration very short (< 5 minutes)")
        elif duration_minutes > 120:
            warnings.append("Script duration very long (> 2 hours)")
            suggestions.append("Consider breaking into multiple shorter scripts")
        
        # Engagement validation
        engagement_score = 0.8  # Default
        interaction_segment_count = sum(1 for seg in script.segments if 'interaction' in seg.segment_type.lower())
        if interaction_segment_count == 0:
            warnings.append("No interaction segments found")
            engagement_score = 0.6
            suggestions.append("Add interaction segments to improve viewer engagement")
        
        # Technical validation
        technical_score = 1.0
        for segment in script.segments:
            if not segment.content.strip():
                errors.append(f"Empty content in segment {segment.segment_id}")
                technical_score = 0.5
            if segment.duration_seconds <= 0:
                errors.append(f"Invalid duration in segment {segment.segment_id}")
                technical_score = 0.5
        
        # Calculate overall quality score
        quality_score = (content_length_score + coherence_score + engagement_score + technical_score) / 4
        
        return ScriptValidationResult(
            is_valid=len(errors) == 0,
            quality_score=quality_score,
            validation_warnings=warnings,
            validation_errors=errors,
            content_length_score=content_length_score,
            coherence_score=coherence_score,
            engagement_score=engagement_score,
            technical_score=technical_score,
            improvement_suggestions=suggestions,
            estimated_performance_impact={
                "viewer_retention": engagement_score,
                "content_quality": coherence_score,
                "technical_reliability": technical_score
            }
        )
    
    # === Conversion and Helper Methods ===
    
    async def _convert_preview_to_persistent_script(
        self,
        script_id: str,
        preview_result: PreviewResult,
        form: OperationalForm,
        created_by: Optional[str]
    ) -> PersistedScript:
        """Convert PreviewResult to PersistedScript"""
        
        timeline = preview_result.script_timeline
        
        # Convert segments
        segments = []
        for i, segment in enumerate(timeline.segments):
            persisted_segment = PersistedScriptSegment(
                segment_id=segment.segment_id,
                script_id=script_id,
                segment_type=segment.segment_type.value,
                title=segment.title,
                content=segment.content,
                duration_seconds=segment.estimated_duration_seconds,
                priority=segment.priority.value,
                segment_order=i,
                triggers=segment.triggers,
                variables=segment.variables,
                generation_metadata=getattr(segment, 'generation_metadata', {}),
                created_at=datetime.now()
            )
            segments.append(persisted_segment)
        
        # Determine generation method
        generation_method = GenerationMethod.TEMPLATE  # Default
        if hasattr(preview_result, 'generation_stats'):
            gen_stats = preview_result.generation_stats
            if gen_stats.get('llm_generated', False):
                generation_method = GenerationMethod.LLM
            elif gen_stats.get('hybrid_generation', False):
                generation_method = GenerationMethod.HYBRID
        
        # Create form snapshot for search and analytics
        form_snapshot = {
            'basic_information': {
                'stream_title': form.basic_information.stream_title,
                'stream_type': form.basic_information.stream_type.value if hasattr(form.basic_information.stream_type, 'value') else str(form.basic_information.stream_type),
                'planned_duration_minutes': form.basic_information.planned_duration_minutes
            },
            'product_information': {
                'product_name': form.product_information.product_name,
                'brand': form.product_information.brand,
                'category': form.product_information.category,
                'current_price': float(form.product_information.current_price) if form.product_information.current_price else 0.0
            },
            'generated_metadata': {
                'generation_time_seconds': preview_result.generation_time_seconds,
                'warnings': preview_result.warnings
            }
        }
        
        # Extract persona information
        persona_info = {}
        if hasattr(preview_result, 'persona_info'):
            persona_info = preview_result.persona_info
        
        # Create persisted script
        return PersistedScript(
            script_id=script_id,
            form_id=preview_result.form_id,
            generated_at=datetime.now(),
            generation_method=generation_method,
            total_duration_seconds=sum(seg.duration_seconds for seg in segments),
            segment_count=len(segments),
            generation_time_ms=int(preview_result.generation_time_seconds * 1000),
            html_preview=preview_result.preview_html,
            segments=segments,
            interaction_points=timeline.interaction_points,
            break_points=timeline.break_points,
            adaptation_rules=timeline.adaptation_rules,
            estimated_metrics=preview_result.estimated_metrics,
            generation_warnings=preview_result.warnings,
            persona_info=persona_info,
            form_snapshot=form_snapshot,
            status=ScriptStatus.GENERATED,
            created_by=created_by,
            tags=self._extract_tags_from_form(form)
        )
    
    def _create_streaming_session(self, script_id: str, session_data: Dict[str, Any]) -> StreamingSession:
        """Create StreamingSession from session data"""
        return StreamingSession(
            session_id=session_data.get('session_id', str(uuid.uuid4())),
            script_id=script_id,
            started_at=session_data.get('started_at', datetime.now()),
            ended_at=session_data.get('ended_at'),
            segments_used=session_data.get('segments_used', 0),
            segments_skipped=session_data.get('segments_skipped', 0),
            total_viewers=session_data.get('total_viewers', 0),
            peak_viewers=session_data.get('peak_viewers', 0),
            average_engagement=session_data.get('average_engagement', 0.0),
            generation_to_use_delay_minutes=session_data.get('generation_to_use_delay_minutes', 0),
            actual_vs_planned_duration_ratio=session_data.get('actual_vs_planned_duration_ratio', 1.0),
            viewer_retention_rate=session_data.get('viewer_retention_rate', 0.0),
            questions_received=session_data.get('questions_received', 0),
            questions_answered=session_data.get('questions_answered', 0),
            interaction_rate=session_data.get('interaction_rate', 0.0),
            conversion_metrics=session_data.get('conversion_metrics', {}),
            stream_title=session_data.get('stream_title', ''),
            persona_used=session_data.get('persona_used', ''),
            platform_data=session_data.get('platform_data', {})
        )
    
    def _extract_tags_from_form(self, form: OperationalForm) -> List[str]:
        """Extract relevant tags from operational form"""
        tags = []
        
        # Add category as tag
        if hasattr(form.product_information, 'category') and form.product_information.category:
            tags.append(form.product_information.category.lower().replace(' ', '_'))
        
        # Add brand as tag
        if hasattr(form.product_information, 'brand') and form.product_information.brand:
            tags.append(form.product_information.brand.lower().replace(' ', '_'))
        
        # Add stream type as tag
        if hasattr(form.basic_information, 'stream_type'):
            stream_type = form.basic_information.stream_type
            if hasattr(stream_type, 'value'):
                tags.append(stream_type.value)
            else:
                tags.append(str(stream_type))
        
        # Add selling point priorities as tags
        if hasattr(form, 'selling_points_structure') and form.selling_points_structure.selling_points:
            high_priority_count = sum(1 for sp in form.selling_points_structure.selling_points 
                                    if hasattr(sp, 'priority') and sp.priority.value == 'high')
            if high_priority_count > 0:
                tags.append(f'high_priority_{high_priority_count}')
        
        return tags
    
    def _generate_script_id(self, form_id: str) -> str:
        """Generate unique script ID"""
        timestamp = int(datetime.now().timestamp() * 1000)
        return f"script_{form_id}_{timestamp}"
    
    def _update_persistence_stats(self, persistence_time_ms: float, success: bool) -> None:
        """Update internal statistics"""
        if success:
            self.stats["scripts_persisted"] += 1
            
            # Update rolling average
            count = self.stats["scripts_persisted"]
            current_avg = self.stats["average_persistence_time_ms"]
            new_avg = ((current_avg * (count - 1)) + persistence_time_ms) / count
            self.stats["average_persistence_time_ms"] = new_avg
            
            self.stats["last_persistence_at"] = datetime.now()
        else:
            self.stats["persistence_errors"] += 1
    
    def _calculate_performance_rating(self, quality_score: float, usage_efficiency: float, engagement: float) -> str:
        """Calculate overall performance rating"""
        overall_score = (quality_score + usage_efficiency + engagement) / 3
        
        if overall_score >= 0.8:
            return "excellent"
        elif overall_score >= 0.6:
            return "good"
        elif overall_score >= 0.4:
            return "fair"
        else:
            return "poor"
    
    def _generate_performance_recommendations(
        self, 
        script: PersistedScript, 
        usage_efficiency: float, 
        quality_score: float
    ) -> List[str]:
        """Generate performance improvement recommendations"""
        recommendations = []
        
        if usage_efficiency < 0.3:
            recommendations.append("Script has low usage - consider improving content relevance or promotion")
        
        if quality_score < 0.6:
            recommendations.append("Script quality is below average - review and refine content")
        
        if len(script.segments) < 4:
            recommendations.append("Consider adding more segments for better content structure")
        
        if script.total_duration_seconds < 300:  # 5 minutes
            recommendations.append("Script may be too short - consider expanding content")
        elif script.total_duration_seconds > 3600:  # 1 hour
            recommendations.append("Script may be too long - consider breaking into multiple parts")
        
        # Check for interaction segments
        interaction_segments = [s for s in script.segments if 'interaction' in s.segment_type.lower()]
        if not interaction_segments:
            recommendations.append("Add interaction segments to improve viewer engagement")
        
        return recommendations
    
    # === Maintenance and Utilities ===
    
    async def cleanup_old_scripts(self, retention_days: int = None) -> int:
        """Clean up old scripts based on retention policy"""
        retention_days = retention_days or self.config.auto_delete_after_days
        return await self.repository.cleanup_old_data(retention_days)
    
    async def optimize_storage(self) -> bool:
        """Optimize storage performance"""
        return await self.repository.optimize_database()
    
    def get_manager_stats(self) -> Dict[str, Any]:
        """Get manager-level statistics"""
        return {
            **self.stats,
            "config": {
                "database_path": self.config.database_path,
                "quality_validation_enabled": self.config.enable_quality_validation,
                "min_quality_threshold": self.config.min_quality_threshold,
                "retention_days": self.config.auto_delete_after_days
            },
            "observers_count": len(self._observers),
            "validators_count": len(self._quality_validators)
        }