#!/usr/bin/env python3
"""
播放命令定义模块

定义播放器命令接口和具体实现，避免循环导入问题。
"""

from abc import ABC, abstractmethod
from typing import List, TYPE_CHECKING
from enum import Enum

if TYPE_CHECKING:
    from ..services.main_content_player import MainContentPlayer


class InsertionPolicy(Enum):
    """插播策略枚举"""
    IMMEDIATE = "immediate"                    # 立即中断当前句子
    AFTER_SENTENCE = "after_sentence"          # 当前句子结束后插入
    AFTER_PARAGRAPH = "after_paragraph"       # 当前段落结束后插入


class PlaybackCommand(ABC):
    """播放命令基类 - 纯接口定义，不含业务逻辑"""
    
    @abstractmethod
    async def execute(self, player: 'MainContentPlayer') -> None:
        """执行命令
        
        Args:
            player: MainContentPlayer实例
        """
        pass


class InsertSentencesCommand(PlaybackCommand):
    """插入句子命令"""
    
    def __init__(self, sentences: List[str], policy: InsertionPolicy = InsertionPolicy.AFTER_SENTENCE):
        self.sentences = sentences
        self.policy = policy
    
    async def execute(self, player: 'MainContentPlayer') -> None:
        """纯技术执行，不做业务决策"""
        await player._insert_sentences_with_policy(self.sentences, self.policy)


class PauseCommand(PlaybackCommand):
    """暂停命令"""
    
    async def execute(self, player: 'MainContentPlayer') -> None:
        await player._do_pause()


class ResumeCommand(PlaybackCommand):
    """恢复命令"""
    
    async def execute(self, player: 'MainContentPlayer') -> None:
        await player._do_resume()