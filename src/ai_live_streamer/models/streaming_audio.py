"""Streaming Audio Models

Data models for streaming audio processing, supporting real-time TTS synthesis
and playback without file creation. Designed for CosyVoice v2 integration.
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum
import numpy as np
from loguru import logger

from .constants import AudioPriority, DEFAULT_SAMPLE_RATE


class StreamingStatus(Enum):
    """Streaming audio status"""
    INITIALIZING = "initializing"    # Setting up stream
    BUFFERING = "buffering"         # Buffering audio data
    STREAMING = "streaming"         # Actively streaming
    PLAYING = "playing"             # Currently playing
    PAUSED = "paused"              # Playback paused
    COMPLETED = "completed"        # Stream completed
    FAILED = "failed"              # Stream failed
    STOPPED = "stopped"            # Stream stopped


class VoiceProfile(BaseModel):
    """Voice profile configuration for CosyVoice v2"""
    
    voice_id: str = Field(description="Voice identifier (e.g., 'longanran', 'longangxuan')")
    name: str = Field(description="Human readable voice name")
    type: str = Field(description="Voice type (e.g., 'female_energetic', 'female_calm')")
    description: str = Field(description="Voice description")
    language: str = Field(default="zh-CN", description="Voice language")
    
    # Voice characteristics
    pitch_range: str = Field(default="medium", description="Pitch range (low/medium/high)")
    energy_level: str = Field(default="medium", description="Energy level (low/medium/high)")
    speaking_rate: float = Field(default=1.0, ge=0.5, le=2.0, description="Speaking rate multiplier")

    # Usage preferences
    suitable_for: List[str] = Field(
        default_factory=list,
        description="Suitable use cases (e.g., product_intro, call_to_action)"
    )
    quality_score: float = Field(default=0.9, description="Voice quality score (0-1)")
    latency_ms: int = Field(default=150, description="Expected synthesis latency in ms")

    # Technical parameters
    sample_rate: int = Field(default=24000, description="Audio sample rate")
    bit_depth: int = Field(default=16, description="Audio bit depth")
    channels: int = Field(default=1, description="Audio channels (1=mono, 2=stereo)")


class AudioStream(BaseModel):
    """Audio stream data structure for real-time processing"""
    
    stream_id: str = Field(description="Unique stream identifier")
    audio_data: Union[bytes, np.ndarray] = Field(description="Raw audio data")
    
    # Stream metadata
    sample_rate: int = Field(default=24000, description="Audio sample rate")
    channels: int = Field(default=1, description="Number of audio channels")
    bit_depth: int = Field(default=16, description="Audio bit depth")
    duration_ms: int = Field(description="Audio duration in milliseconds")
    
    # Timing information
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Stream creation time")
    sequence_number: int = Field(description="Sequence number in stream")
    is_final: bool = Field(default=False, description="Whether this is the final chunk")
    
    # Content metadata
    text_content: Optional[str] = Field(None, description="Original text content")
    segment_id: Optional[str] = Field(None, description="Related segment ID")
    segment_index: Optional[int] = Field(None, description="Segment index in timeline")
    
    # Audio processing metadata
    voice_profile: Optional[VoiceProfile] = Field(None, description="Voice profile used")
    synthesis_latency_ms: Optional[int] = Field(None, description="TTS synthesis latency")
    
    class Config:
        arbitrary_types_allowed = True  # Allow numpy arrays

    def to_numpy_array(self) -> Optional[np.ndarray]:
        """Convert audio data to numpy array for playback

        Returns:
            Numpy array of audio samples or None if no audio data
        """
        if self.audio_data is None:
            return None

        try:
            if isinstance(self.audio_data, np.ndarray):
                return self.audio_data.astype(np.float32)
            elif isinstance(self.audio_data, bytes):
                # Convert bytes to numpy array (assuming 16-bit PCM)
                audio_array = np.frombuffer(self.audio_data, dtype=np.int16)
                # Convert to float32 and normalize to [-1, 1]
                return audio_array.astype(np.float32) / 32768.0
            else:
                return None
        except Exception:
            return None


class StreamingChunk(BaseModel):
    """Individual streaming audio chunk"""
    
    chunk_id: str = Field(description="Unique chunk identifier")
    stream_id: str = Field(description="Parent stream identifier")
    audio_stream: AudioStream = Field(description="Audio stream data")
    
    # Playback metadata
    priority: AudioPriority = Field(default=AudioPriority.NORMAL, description="Playback priority")
    status: StreamingStatus = Field(default=StreamingStatus.INITIALIZING, description="Chunk status")
    
    # Queue management
    queued_at: datetime = Field(default_factory=datetime.utcnow, description="When chunk was queued")
    play_after: Optional[datetime] = Field(None, description="Earliest play time")
    expires_at: Optional[datetime] = Field(None, description="Chunk expiration time")
    
    # Playback control
    fade_in_ms: int = Field(default=0, ge=0, le=1000, description="Fade-in duration")
    fade_out_ms: int = Field(default=0, ge=0, le=1000, description="Fade-out duration")
    volume: float = Field(default=1.0, ge=0.0, le=2.0, description="Playback volume")
    
    # Context information
    context_type: str = Field(default="narration", description="Content context type")
    segment_title: Optional[str] = Field(None, description="Related segment title")
    segment_type: Optional[str] = Field(None, description="Related segment type")
    
    def is_expired(self) -> bool:
        """Check if chunk has expired"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    def can_play_now(self) -> bool:
        """Check if chunk can be played now"""
        if self.play_after is None:
            return True
        return datetime.utcnow() >= self.play_after
    
    def get_wait_time_ms(self) -> int:
        """Get how long chunk has been waiting"""
        delta = datetime.utcnow() - self.queued_at
        return int(delta.total_seconds() * 1000)


class StreamingQueue(BaseModel):
    """Queue for managing streaming audio chunks"""
    
    queue_id: str = Field(description="Queue identifier")
    chunks: List[StreamingChunk] = Field(default_factory=list, description="Queued chunks")
    
    # Queue configuration
    max_queue_size: int = Field(default=150, ge=1, le=500, description="Maximum queue size - 支持流式音频的大量小块")
    max_buffer_duration_ms: int = Field(default=10000, description="Maximum buffer duration")
    auto_cleanup: bool = Field(default=True, description="Auto-cleanup expired chunks")
    
    # Queue statistics
    total_processed: int = Field(default=0, description="Total chunks processed")
    total_expired: int = Field(default=0, description="Total expired chunks")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Queue creation time")
    
    def add_chunk(self, chunk: StreamingChunk) -> bool:
        """Add chunk to queue with priority handling"""
        original_size = len(self.chunks)
        
        if len(self.chunks) >= self.max_queue_size:
            if self.auto_cleanup:
                self._cleanup_expired()
                expired_removed = original_size - len(self.chunks)
                if expired_removed > 0:
                    logger.debug(f"Queue {self.queue_id}: 清理过期块 {expired_removed} 个")
            
            if len(self.chunks) >= self.max_queue_size:
                # Remove lowest priority chunks if still full
                before_priority_cleanup = len(self.chunks)
                self._remove_low_priority_chunks()
                priority_removed = before_priority_cleanup - len(self.chunks)
                if priority_removed > 0:
                    logger.debug(f"Queue {self.queue_id}: 移除低优先级块 {priority_removed} 个")
                
            if len(self.chunks) >= self.max_queue_size:
                # 队列仍然满载，记录详细信息用于调试
                logger.warning(
                    f"Queue {self.queue_id} 满载拒绝新块 {chunk.chunk_id}: "
                    f"current={len(self.chunks)}, max={self.max_queue_size}, "
                    f"total_processed={self.total_processed}, total_expired={self.total_expired}"
                )
                return False
        
        self.chunks.append(chunk)
        self._sort_by_priority()
        
        # 队列健康度监控
        queue_health = len(self.chunks) / self.max_queue_size
        if queue_health > 0.8:  # 队列使用率超过80%时警告
            logger.warning(
                f"Queue {self.queue_id} 使用率高: {queue_health:.1%} "
                f"({len(self.chunks)}/{self.max_queue_size})"
            )
        
        return True
    
    def get_next_chunk(self) -> Optional[StreamingChunk]:
        """Get next chunk ready for playback"""
        if self.auto_cleanup:
            self._cleanup_expired()
        
        for chunk in self.chunks:
            if chunk.can_play_now() and not chunk.is_expired():
                return chunk
        
        return None
    
    def remove_chunk(self, chunk_id: str) -> bool:
        """Remove chunk by ID"""
        for i, chunk in enumerate(self.chunks):
            if chunk.chunk_id == chunk_id:
                del self.chunks[i]
                self.total_processed += 1
                return True
        return False
    
    def clear_context(self, context_type: str) -> int:
        """Clear chunks of specific context type"""
        original_count = len(self.chunks)
        self.chunks = [chunk for chunk in self.chunks if chunk.context_type != context_type]
        removed_count = original_count - len(self.chunks)
        self.total_processed += removed_count
        return removed_count
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """Get queue statistics"""
        if not self.chunks:
            return {
                "total_chunks": 0,
                "buffer_duration_ms": 0,
                "by_priority": {},
                "by_context": {},
                "average_wait_time_ms": 0
            }
        
        # Calculate statistics
        by_priority = {}
        by_context = {}
        total_wait_time = 0
        total_duration = 0
        
        for chunk in self.chunks:
            # Count by priority
            priority_name = chunk.priority.name
            by_priority[priority_name] = by_priority.get(priority_name, 0) + 1
            
            # Count by context
            context = chunk.context_type
            by_context[context] = by_context.get(context, 0) + 1
            
            # Sum wait times and durations
            total_wait_time += chunk.get_wait_time_ms()
            total_duration += chunk.audio_stream.duration_ms
        
        return {
            "total_chunks": len(self.chunks),
            "buffer_duration_ms": total_duration,
            "by_priority": by_priority,
            "by_context": by_context,
            "average_wait_time_ms": total_wait_time // len(self.chunks),
            "total_processed": self.total_processed,
            "total_expired": self.total_expired
        }
    
    def _sort_by_priority(self) -> None:
        """Sort chunks by priority"""
        self.chunks.sort(key=lambda chunk: chunk.priority.value)
    
    def _cleanup_expired(self) -> None:
        """Remove expired chunks"""
        original_count = len(self.chunks)
        self.chunks = [chunk for chunk in self.chunks if not chunk.is_expired()]
        expired_count = original_count - len(self.chunks)
        self.total_expired += expired_count
    
    def _remove_low_priority_chunks(self) -> None:
        """Remove low priority chunks when queue is full"""
        if not self.chunks:
            return
            
        # 如果所有块优先级相同，按时间顺序移除最老的块（FIFO）
        priorities = [chunk.priority.value for chunk in self.chunks]
        if len(set(priorities)) == 1:
            # 所有块优先级相同，移除最老的25%块以腾出空间
            remove_count = max(1, len(self.chunks) // 4)
            # 按排队时间排序，移除最老的
            self.chunks.sort(key=lambda chunk: chunk.queued_at)
            removed_chunks = self.chunks[:remove_count]
            self.chunks = self.chunks[remove_count:]
            self.total_processed += len(removed_chunks)
        else:
            # 有不同优先级，移除低优先级块
            min_priority = min(priorities)
            original_count = len(self.chunks)
            self.chunks = [chunk for chunk in self.chunks if chunk.priority.value == min_priority]
            removed_count = original_count - len(self.chunks)
            self.total_processed += removed_count


class StreamingPlaybackStatus(BaseModel):
    """Streaming playback status information"""
    
    # Current state
    status: StreamingStatus = Field(description="Current streaming status")
    is_active: bool = Field(default=False, description="Whether streaming is active")
    is_playing: bool = Field(default=False, description="Whether audio is playing")
    is_buffering: bool = Field(default=False, description="Whether currently buffering")
    
    # Current playback
    current_chunk_id: Optional[str] = Field(None, description="Currently playing chunk ID")
    current_stream_id: Optional[str] = Field(None, description="Currently playing stream ID")
    current_voice: Optional[str] = Field(None, description="Currently used voice")
    
    # Progress information
    chunks_played: int = Field(default=0, description="Total chunks played")
    total_chunks: int = Field(default=0, description="Total chunks in queue")
    playback_position_ms: int = Field(default=0, description="Current playback position")
    
    # Performance metrics
    average_latency_ms: float = Field(default=0.0, description="Average synthesis latency")
    buffer_health_percent: float = Field(default=0.0, description="Buffer health percentage")
    throughput_mbps: float = Field(default=0.0, description="Audio throughput")
    
    # Error tracking
    synthesis_errors: int = Field(default=0, description="TTS synthesis errors")
    playback_errors: int = Field(default=0, description="Audio playback errors")
    network_errors: int = Field(default=0, description="Network-related errors")
    
    # Timing
    session_start_time: Optional[datetime] = Field(None, description="Session start time")
    last_chunk_time: Optional[datetime] = Field(None, description="Last chunk received time")
    last_update: datetime = Field(default_factory=datetime.utcnow, description="Status last updated")
    
    def get_uptime_seconds(self) -> float:
        """Get session uptime in seconds"""
        if self.session_start_time is None:
            return 0.0
        return (datetime.utcnow() - self.session_start_time).total_seconds()
    
    def get_buffer_duration_ms(self) -> int:
        """Calculate buffer duration based on queue stats"""
        # This would be updated by the streaming player
        return int(self.buffer_health_percent * 10000 / 100)  # Rough estimate


# Export main models
__all__ = [
    "StreamingStatus",
    "VoiceProfile", 
    "AudioStream",
    "StreamingChunk",
    "StreamingQueue",
    "StreamingPlaybackStatus"
]