"""产品模型定义

定义产品相关的数据模型，包括产品基本信息和标签系统。
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class ProductCategory(str, Enum):
    """产品分类枚举"""
    ELECTRONICS = "electronics"
    CLOTHING = "clothing"
    FOOD = "food"
    BEAUTY = "beauty"
    HOME = "home"
    SPORTS = "sports"
    BOOKS = "books"
    TOYS = "toys"
    OTHER = "other"


class TagCategory(str, Enum):
    """标签分类枚举"""
    FEATURE = "feature"       # 功能特性
    AUDIENCE = "audience"     # 目标人群
    SCENE = "scene"          # 使用场景
    STYLE = "style"          # 风格
    PRICE = "price"          # 价格段
    SEASON = "season"        # 季节
    OTHER = "other"          # 其他


class Product(BaseModel):
    """产品基本模型"""
    id: Optional[int] = Field(None, description="产品ID（自增主键）")
    sku: str = Field(..., min_length=1, max_length=100, description="产品SKU（业务唯一标识）")
    name: str = Field(..., min_length=1, max_length=200, description="产品名称")
    category: ProductCategory = Field(..., description="产品分类")
    description: Optional[str] = Field(None, max_length=2000, description="产品描述")
    price: Optional[float] = Field(None, ge=0, description="产品价格")
    stock: Optional[int] = Field(None, ge=0, description="库存数量")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    @validator('sku')
    def validate_sku(cls, v):
        """验证SKU格式"""
        if not v or not v.strip():
            raise ValueError('SKU不能为空')
        # SKU只允许字母、数字、横线和下划线
        import re
        if not re.match(r'^[A-Za-z0-9_-]+$', v):
            raise ValueError('SKU只能包含字母、数字、横线和下划线')
        return v.upper()  # 统一转为大写
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class ProductCreate(BaseModel):
    """创建产品请求模型"""
    sku: str = Field(..., min_length=1, max_length=100, description="产品SKU")
    name: str = Field(..., min_length=1, max_length=200, description="产品名称")
    category: ProductCategory = Field(..., description="产品分类")
    description: Optional[str] = Field(None, max_length=2000, description="产品描述")
    price: Optional[float] = Field(None, ge=0, description="产品价格")
    stock: Optional[int] = Field(None, ge=0, description="库存数量")
    tags: Optional[List[str]] = Field(default_factory=list, description="产品标签列表")


class ProductUpdate(BaseModel):
    """更新产品请求模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="产品名称")
    category: Optional[ProductCategory] = Field(None, description="产品分类")
    description: Optional[str] = Field(None, max_length=2000, description="产品描述")
    price: Optional[float] = Field(None, ge=0, description="产品价格")
    stock: Optional[int] = Field(None, ge=0, description="库存数量")
    tags: Optional[List[str]] = Field(None, description="产品标签列表")


class ProductResponse(BaseModel):
    """产品响应模型"""
    id: int
    sku: str
    name: str
    category: str
    description: Optional[str]
    price: Optional[float]
    stock: Optional[int]
    tags: List[str]
    created_at: datetime
    updated_at: datetime


class ProductDetailResponse(ProductResponse):
    """产品详情响应模型（包含统计信息）"""
    qa_count: int = Field(0, description="QA数量")
    config_count: int = Field(0, description="配置数量")
    total_qa_hits: int = Field(0, description="QA总命中次数")
    last_used: Optional[datetime] = Field(None, description="最后使用时间")


class ProductStats(BaseModel):
    """产品统计信息"""
    product_id: int
    qa_count: int = Field(0, description="该产品的QA数量")
    config_count: int = Field(0, description="使用该产品的配置数")
    last_used: Optional[datetime] = Field(None, description="最后使用时间")
    total_qa_hits: int = Field(0, description="QA总命中次数")
    popular_questions: List[Dict[str, Any]] = Field(default_factory=list, description="热门问题列表")
    created_at: datetime
    updated_at: datetime


class Tag(BaseModel):
    """标签模型"""
    id: Optional[int] = Field(None, description="标签ID")
    name: str = Field(..., min_length=1, max_length=50, description="标签名称")
    category: TagCategory = Field(TagCategory.OTHER, description="标签分类")
    description: Optional[str] = Field(None, max_length=200, description="标签描述")
    usage_count: int = Field(0, description="使用次数")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    
    @validator('name')
    def validate_name(cls, v):
        """验证标签名称"""
        if not v or not v.strip():
            raise ValueError('标签名称不能为空')
        return v.strip().lower()  # 统一转为小写


class ProductTag(BaseModel):
    """产品标签关联模型"""
    product_id: int = Field(..., description="产品ID")
    tag_id: int = Field(..., description="标签ID")
    created_at: Optional[datetime] = Field(None, description="创建时间")


class ProductFilters(BaseModel):
    """产品查询过滤器"""
    category: Optional[ProductCategory] = Field(None, description="产品分类")
    search: Optional[str] = Field(None, description="搜索关键词")
    tags: Optional[List[str]] = Field(None, description="标签过滤")
    min_price: Optional[float] = Field(None, ge=0, description="最低价格")
    max_price: Optional[float] = Field(None, ge=0, description="最高价格")
    has_stock: Optional[bool] = Field(None, description="是否有库存")
    sort_by: Optional[str] = Field("created_at", description="排序字段")
    sort_order: Optional[str] = Field("desc", description="排序方向")


class Pagination(BaseModel):
    """分页参数"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页大小")


class Page(BaseModel):
    """分页响应模型"""
    items: List[Any] = Field(default_factory=list, description="数据项")
    total: int = Field(0, description="总数")
    page: int = Field(1, description="当前页")
    size: int = Field(20, description="每页大小")
    pages: int = Field(0, description="总页数")
    has_next: bool = Field(False, description="是否有下一页")
    has_prev: bool = Field(False, description="是否有上一页")