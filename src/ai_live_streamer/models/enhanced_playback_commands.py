"""增强播放命令系统 - QA并发冲突解决专用

实现预演-提交模式的播放命令，用于解决QA插入与主播放流程的并发竞争问题。

核心特性：
1. 预演-提交模式：所有修改先在暂存区验证，通过后原子性提交
2. 状态一致性检查：确保插入点仍然有效
3. 回滚机制：失败时零成本回滚
4. 优先级队列：QA插入优先级高于常规控制

Author: Claude Code
Date: 2025-01-08 (QA修复专用)
"""

import time
import asyncio
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Union, TYPE_CHECKING
from enum import Enum
from dataclasses import dataclass, field
from loguru import logger

if TYPE_CHECKING:
    from ..services.main_content_player import MainContentPlayer


class CommandPriority(Enum):
    """命令优先级"""
    LOW = 1
    NORMAL = 5
    HIGH = 8
    CRITICAL = 10  # QA插入使用


class CommandExecutionResult(Enum):
    """命令执行结果"""
    SUCCESS = "success"
    FAILED = "failed" 
    ROLLBACK = "rollback"
    SKIPPED = "skipped"


@dataclass
class StagingArea:
    """暂存区 - 用于预演-提交模式"""
    original_state: Dict[str, Any] = field(default_factory=dict)
    modified_state: Dict[str, Any] = field(default_factory=dict) 
    changes: List[Dict[str, Any]] = field(default_factory=list)
    validation_errors: List[str] = field(default_factory=list)
    
    def record_change(self, change_type: str, **kwargs):
        """记录变更"""
        change = {
            "type": change_type,
            "timestamp": time.time(),
            **kwargs
        }
        self.changes.append(change)
    
    def add_validation_error(self, error: str):
        """添加验证错误"""
        self.validation_errors.append(error)
    
    def is_valid(self) -> bool:
        """检查暂存区是否有效"""
        return len(self.validation_errors) == 0
    
    def get_summary(self) -> Dict[str, Any]:
        """获取变更摘要"""
        return {
            "total_changes": len(self.changes),
            "validation_errors": len(self.validation_errors),
            "is_valid": self.is_valid(),
            "changes": self.changes,
            "errors": self.validation_errors
        }


class EnhancedPlaybackCommand(ABC):
    """增强播放命令基类 - 支持预演-提交模式"""
    
    def __init__(self, priority: CommandPriority = CommandPriority.NORMAL):
        self.priority = priority
        self.command_id = f"cmd_{int(time.time() * 1000)}"
        self.created_at = time.time()
        self.execution_result: Optional[CommandExecutionResult] = None
        self.error_message: Optional[str] = None
        self.execution_time: Optional[float] = None
    
    @abstractmethod
    async def preview(self, player: 'MainContentPlayer', staging: StagingArea) -> None:
        """预演阶段 - 在暂存区模拟执行，验证可行性
        
        Args:
            player: 播放器实例
            staging: 暂存区
        """
        pass
    
    @abstractmethod 
    async def commit(self, player: 'MainContentPlayer', staging: StagingArea) -> None:
        """提交阶段 - 原子性应用暂存区的变更
        
        Args:
            player: 播放器实例  
            staging: 经过验证的暂存区
        """
        pass
    
    async def rollback(self, player: 'MainContentPlayer', staging: StagingArea) -> None:
        """回滚阶段 - 恢复原始状态（默认实现：不做任何操作）
        
        Args:
            player: 播放器实例
            staging: 暂存区
        """
        # 默认实现：预演-提交模式下，回滚就是丢弃暂存区，不需要额外操作
        logger.debug(f"Command {self.command_id} rollback completed (discard staging)")
    
    async def execute_with_staging(self, player: 'MainContentPlayer') -> CommandExecutionResult:
        """使用预演-提交模式执行命令"""
        execution_start = time.time()
        staging = StagingArea()
        
        try:
            # 📋 第一阶段：预演
            logger.debug(f"🎭 命令预演开始: {self.command_id} ({type(self).__name__})")
            
            # 记录原始状态
            staging.original_state = await self._capture_player_state(player)
            
            # 执行预演
            await self.preview(player, staging)
            
            # 验证预演结果
            if not staging.is_valid():
                logger.warning(f"❌ 命令预演验证失败: {self.command_id}, 错误: {staging.validation_errors}")
                self.execution_result = CommandExecutionResult.FAILED
                self.error_message = f"预演验证失败: {', '.join(staging.validation_errors)}"
                return self.execution_result
            
            logger.debug(f"✅ 命令预演成功: {self.command_id}, 变更: {len(staging.changes)}")
            
            # 📦 第二阶段：原子性提交
            logger.debug(f"📦 命令提交开始: {self.command_id}")
            
            await self.commit(player, staging)
            
            self.execution_result = CommandExecutionResult.SUCCESS
            logger.info(f"✅ 命令执行成功: {self.command_id} ({type(self).__name__})")
            
            return self.execution_result
            
        except Exception as e:
            # 🔄 第三阶段：回滚
            logger.error(f"❌ 命令执行异常: {self.command_id}, error: {e}")
            
            try:
                await self.rollback(player, staging)
                self.execution_result = CommandExecutionResult.ROLLBACK
                logger.info(f"🔄 命令回滚成功: {self.command_id}")
            except Exception as rollback_error:
                logger.error(f"💀 命令回滚失败: {self.command_id}, rollback_error: {rollback_error}")
                self.execution_result = CommandExecutionResult.FAILED
            
            self.error_message = str(e)
            return self.execution_result
            
        finally:
            self.execution_time = time.time() - execution_start
            logger.debug(f"⏱️ 命令执行耗时: {self.command_id}, {self.execution_time:.3f}s")
    
    async def _capture_player_state(self, player: 'MainContentPlayer') -> Dict[str, Any]:
        """捕获播放器当前状态"""
        return {
            "current_index": player.current_index,
            "total_sentences": len(player.sentences),
            "is_playing": player._playing_state,
            "is_paused": player.is_paused,
            "should_stop": player.should_stop,
            "sentences_snapshot": len(player.sentences),  # 只记录数量，避免大量数据
            "state_timestamp": time.time()
        }
    
    def __lt__(self, other):
        """优先级队列排序"""
        if not isinstance(other, EnhancedPlaybackCommand):
            return NotImplemented
        
        # 优先级高的排前面，创建时间早的排前面
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value
        return self.created_at < other.created_at


class QAInsertCommand(EnhancedPlaybackCommand):
    """QA插入命令 - 使用预演-提交模式解决并发冲突"""
    
    def __init__(
        self,
        sentences: List[str], 
        target_position: Optional[int] = None,
        insertion_strategy: str = "safe_after_current",
        metadata: Dict[str, Any] = None
    ):
        super().__init__(CommandPriority.CRITICAL)  # QA插入使用最高优先级
        
        self.sentences = sentences
        self.target_position = target_position
        self.insertion_strategy = insertion_strategy
        self.metadata = metadata or {}
        
        # 插入结果
        self.actual_position: Optional[int] = None
        self.inserted_count: int = 0
    
    async def preview(self, player: 'MainContentPlayer', staging: StagingArea) -> None:
        """预演QA插入"""
        try:
            current_index = player.current_index
            total_sentences = len(player.sentences)
            current_state = player.state_manager.get_current_state() if hasattr(player, 'state_manager') else None
            
            # 记录预演信息
            staging.record_change(
                "qa_insert_preview",
                sentences_count=len(self.sentences),
                current_index=current_index,
                total_sentences=total_sentences,
                current_state=str(current_state)
            )
            
            # 🎯 智能插入位置计算（基于播放进度）
            calculated_position = self._calculate_safe_insertion_position(
                player, staging
            )
            
            if calculated_position is None:
                staging.add_validation_error("无法计算安全插入位置")
                return
            
            # 验证插入位置的有效性
            if calculated_position < current_index:
                staging.add_validation_error(
                    f"插入位置({calculated_position})早于当前播放位置({current_index})，可能导致音频丢失"
                )
                return
            
            if calculated_position > total_sentences + 100:  # 合理上限检查
                staging.add_validation_error(
                    f"插入位置({calculated_position})过大，超出合理范围"
                )
                return
            
            # 预演成功，记录计算结果
            self.actual_position = calculated_position
            staging.modified_state["insertion_position"] = calculated_position
            staging.modified_state["sentences_to_insert"] = len(self.sentences)
            
            staging.record_change(
                "qa_insert_validated",
                calculated_position=calculated_position,
                sentences_to_insert=len(self.sentences),
                insertion_strategy=self.insertion_strategy
            )
            
            logger.debug(f"🎯 QA插入预演成功: 位置 {calculated_position}, 句子数 {len(self.sentences)}")
            
        except Exception as e:
            staging.add_validation_error(f"QA插入预演异常: {str(e)}")
    
    async def commit(self, player: 'MainContentPlayer', staging: StagingArea) -> None:
        """原子性提交QA插入"""
        if self.actual_position is None:
            raise RuntimeError("插入位置未计算，无法提交")
        
        insertion_position = self.actual_position
        
        # 🔧 使用语义分析器处理句子
        semantic_sentences = []
        for sentence_text in self.sentences:
            if sentence_text.strip():
                # 使用播放器的语义分割器
                sub_sentences = player.semantic_splitter.split_sentences_semantic(sentence_text.strip())
                for sentence in sub_sentences:
                    # 标记为QA插入句子
                    sentence.is_qa_insertion = True
                    sentence.qa_metadata = self.metadata
                    semantic_sentences.append(sentence)
        
        # 🎯 原子性插入（关键操作）
        insert_count = 0
        for i, sentence in enumerate(semantic_sentences):
            actual_insert_pos = insertion_position + i
            player.sentences.insert(actual_insert_pos, sentence)
            insert_count += 1
        
        self.inserted_count = insert_count
        
        # 📊 更新播放进度追踪器（如果存在）
        if hasattr(player, 'progress_tracker') and player.progress_tracker:
            try:
                # 通知进度追踪器有新的QA句子插入
                for i, sentence in enumerate(semantic_sentences):
                    player.progress_tracker.record_qa_insertion(
                        index=insertion_position + i,
                        text=sentence.text,
                        metadata=self.metadata
                    )
            except Exception as e:
                logger.warning(f"⚠️ 进度追踪器更新失败: {e}")
        
        # 记录提交结果
        staging.record_change(
            "qa_insert_committed",
            insertion_position=insertion_position,
            inserted_count=insert_count,
            new_total_sentences=len(player.sentences)
        )
        
        logger.info(f"📦 QA插入提交完成: 位置 {insertion_position}, 插入 {insert_count} 个句子")
    
    def _calculate_safe_insertion_position(
        self, 
        player: 'MainContentPlayer', 
        staging: StagingArea
    ) -> Optional[int]:
        """计算安全的QA插入位置 - 🚀 Phase 2.3 优化版本
        
        智能分析多个维度：
        1. 实际播放进度 vs 发送进度
        2. 播放器状态机状态
        3. QA准备时间 vs 预期播放时间
        4. 用户体验优化（避免过长等待）
        """
        
        try:
            current_index = player.current_index
            total_sentences = len(player.sentences)
            current_time_ms = time.time() * 1000
            
            # 获取播放器当前状态
            player_state = player.state_manager.get_current_state() if hasattr(player, 'state_manager') else None
            player_phase = player.state_manager.get_current_phase() if hasattr(player, 'state_manager') else None
            
            staging.record_change(
                "context_analysis",
                current_index=current_index,
                total_sentences=total_sentences,
                player_state=str(player_state),
                player_phase=str(player_phase),
                qa_ready_time=current_time_ms
            )
            
            # 🚀 策略1：智能双进度分析（最优策略）
            if (hasattr(player, 'progress_tracker') and 
                player.progress_tracker and 
                self.insertion_strategy in ["safe_after_current", "auto"]):
                
                return self._calculate_dual_progress_position(player, staging, current_time_ms)
            
            # 🚀 策略2：基于播放器状态的智能插入
            elif self.insertion_strategy == "smart_state_aware":
                return self._calculate_state_aware_position(player, staging)
            
            # 🎯 策略3：立即插入（激进策略）
            elif self.insertion_strategy == "after_current_sentence":
                return self._calculate_immediate_position(player, staging)
            
            # 🎯 策略4：指定位置（高级用法）
            elif self.target_position is not None:
                return self._calculate_specified_position(player, staging)
            
            else:
                # 默认策略：使用智能双进度分析
                return self._calculate_dual_progress_position(player, staging, current_time_ms)
                
        except Exception as e:
            staging.add_validation_error(f"插入位置计算异常: {str(e)}")
            logger.error(f"❌ QA插入位置计算失败: {e}")
            return None
    
    def _calculate_dual_progress_position(
        self, 
        player: 'MainContentPlayer', 
        staging: StagingArea,
        qa_ready_time_ms: float
    ) -> Optional[int]:
        """双进度智能分析 - 🚀 考虑发送进度和播放进度的差异"""
        
        try:
            current_index = player.current_index  # 发送进度
            playing_index = player.progress_tracker.get_current_playing_index()  # 播放进度
            
            # 获取播放统计信息
            playback_stats = player.progress_tracker.get_playback_stats()
            
            # 计算进度差异
            progress_gap = current_index - playing_index
            
            staging.record_change(
                "dual_progress_analysis",
                sending_index=current_index,
                playing_index=playing_index,
                progress_gap=progress_gap,
                playback_stats=playback_stats
            )
            
            # 🎯 场景1：播放进度跟上了发送进度（理想情况）
            if progress_gap <= 1:
                # 用户基本同步，在当前播放句子后插入
                safe_position = max(playing_index + 2, current_index + 1)
                
                staging.record_change(
                    "position_decision",
                    method="synchronized_progress",
                    reason="发送和播放基本同步",
                    calculated_position=safe_position,
                    confidence=0.9
                )
                
                logger.debug(f"🎯 双进度同步场景: 插入位置 {safe_position} (播放进度跟上)")
                return safe_position
            
            # 🎯 场景2：播放进度落后较多（用户听得慢）
            elif progress_gap > 3:
                # 使用进度追踪器的智能插入点计算
                optimal_position = player.progress_tracker.find_qa_insertion_point(qa_ready_time_ms)
                
                # 确保不回退到已发送位置之前
                safe_position = max(optimal_position, current_index + 1)
                
                staging.record_change(
                    "position_decision",
                    method="lagging_playback_compensation", 
                    reason="播放进度落后，使用时间预测",
                    progress_gap=progress_gap,
                    optimal_position=optimal_position,
                    calculated_position=safe_position,
                    confidence=0.8
                )
                
                logger.debug(f"🎯 播放滞后场景: 插入位置 {safe_position} (进度差 {progress_gap})")
                return safe_position
            
            # 🎯 场景3：中等进度差异（正常缓冲）
            else:
                # 在播放进度基础上预留适当缓冲
                buffer_sentences = min(2, progress_gap)
                safe_position = playing_index + buffer_sentences + 1
                
                # 确保不回退
                safe_position = max(safe_position, current_index + 1)
                
                staging.record_change(
                    "position_decision",
                    method="buffered_insertion",
                    reason="中等进度差异，预留缓冲",
                    buffer_sentences=buffer_sentences,
                    calculated_position=safe_position,
                    confidence=0.85
                )
                
                logger.debug(f"🎯 缓冲插入场景: 插入位置 {safe_position} (缓冲 {buffer_sentences} 句)")
                return safe_position
                
        except Exception as e:
            staging.add_validation_error(f"双进度分析失败: {str(e)}")
            return None
    
    def _calculate_state_aware_position(
        self, 
        player: 'MainContentPlayer', 
        staging: StagingArea
    ) -> Optional[int]:
        """基于播放器状态的智能插入 - 🚀 状态机驱动的位置计算"""
        
        try:
            current_state = player.state_manager.get_current_state()
            is_at_safe_point = player.state_manager.is_at_safe_interruption_point()
            current_index = player.current_index
            
            staging.record_change(
                "state_aware_analysis",
                current_state=str(current_state),
                at_safe_point=is_at_safe_point,
                current_index=current_index
            )
            
            # 🎯 如果当前正处于安全中断点
            if is_at_safe_point:
                # 立即插入到下一个位置
                safe_position = current_index + 1
                
                staging.record_change(
                    "position_decision",
                    method="safe_point_immediate",
                    reason="当前处于安全中断点",
                    calculated_position=safe_position,
                    confidence=0.95
                )
                
                return safe_position
            
            # 🎯 如果正在播放中，等待下一个安全点
            else:
                # 预测下一个安全点（当前句子完成后）
                safe_position = current_index + 1
                
                staging.record_change(
                    "position_decision", 
                    method="next_safe_point",
                    reason="等待当前句子完成后插入",
                    calculated_position=safe_position,
                    confidence=0.8
                )
                
                return safe_position
                
        except Exception as e:
            staging.add_validation_error(f"状态感知计算失败: {str(e)}")
            return None
    
    def _calculate_immediate_position(
        self, 
        player: 'MainContentPlayer', 
        staging: StagingArea
    ) -> Optional[int]:
        """立即插入策略 - 🚀 最快响应时间"""
        
        current_index = player.current_index
        safe_position = current_index + 1
        
        staging.record_change(
            "position_decision",
            method="immediate_after_current",
            reason="立即插入策略，最快响应",
            calculated_position=safe_position,
            confidence=0.7  # 较低置信度，因为可能中断正在播放的句子
        )
        
        logger.debug(f"🎯 立即插入: 位置 {safe_position}")
        return safe_position
    
    def _calculate_specified_position(
        self, 
        player: 'MainContentPlayer', 
        staging: StagingArea
    ) -> Optional[int]:
        """指定位置策略 - 🚀 高级用户自定义"""
        
        current_index = player.current_index
        
        # 验证指定位置的安全性
        if self.target_position >= current_index:
            staging.record_change(
                "position_decision",
                method="user_specified_position",
                target_position=self.target_position,
                current_index=current_index,
                calculated_position=self.target_position,
                confidence=1.0  # 用户明确指定，高置信度
            )
            return self.target_position
        else:
            staging.add_validation_error(
                f"指定位置({self.target_position})早于当前播放位置({current_index})"
            )
            logger.warning(f"⚠️ 用户指定位置无效: {self.target_position} < {current_index}")
            return None


class SmartPlaybackQueue:
    """智能播放命令队列 - 支持优先级和预演-提交模式"""
    
    def __init__(self, maxsize: int = 1000):
        self.queue = asyncio.PriorityQueue(maxsize=maxsize)
        self.processing = False
        self.stats = {
            "total_commands": 0,
            "successful_commands": 0,
            "failed_commands": 0,
            "rollback_commands": 0
        }
    
    async def submit_command(self, command: EnhancedPlaybackCommand) -> None:
        """提交命令到队列"""
        await self.queue.put(command)
        logger.debug(f"📨 命令已提交到队列: {command.command_id}, 优先级: {command.priority.value}")
    
    async def process_commands(self, player: 'MainContentPlayer') -> None:
        """处理命令队列"""
        self.processing = True
        
        try:
            while self.processing:
                try:
                    # 等待命令（带超时）
                    command = await asyncio.wait_for(self.queue.get(), timeout=0.1)
                    
                    # 执行命令
                    self.stats["total_commands"] += 1
                    result = await command.execute_with_staging(player)
                    
                    # 更新统计
                    if result == CommandExecutionResult.SUCCESS:
                        self.stats["successful_commands"] += 1
                    elif result == CommandExecutionResult.ROLLBACK:
                        self.stats["rollback_commands"] += 1
                    else:
                        self.stats["failed_commands"] += 1
                    
                    # 标记任务完成
                    self.queue.task_done()
                    
                except asyncio.TimeoutError:
                    # 超时是正常的，继续循环
                    continue
                except Exception as e:
                    logger.error(f"❌ 命令队列处理异常: {e}")
                    continue
                    
        except asyncio.CancelledError:
            logger.info("📋 命令队列处理被取消")
        finally:
            self.processing = False
    
    def stop_processing(self):
        """停止处理命令"""
        self.processing = False
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self.queue.qsize()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total = self.stats["total_commands"]
        success_rate = (self.stats["successful_commands"] / max(total, 1)) * 100
        
        return {
            **self.stats,
            "success_rate": round(success_rate, 2),
            "queue_size": self.get_queue_size(),
            "is_processing": self.processing
        }