"""
Database Migration Scripts for Script Persistence

Handles database initialization, schema updates, and data migrations
for the script persistence system.
"""

import sqlite3
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
from contextlib import contextmanager

from ..models.script_persistence import DATABASE_SCHEMA, PersistenceConfig
from ..core.config import cfg


logger = logging.getLogger(__name__)


class DatabaseMigrator:
    """Handles database schema creation and migrations"""
    
    def __init__(self, config: Optional[PersistenceConfig] = None):
        self.config = config or PersistenceConfig()
        self.db_path = Path(self.config.database_path)
        self.migration_version = 1
        
    def ensure_database_exists(self) -> bool:
        """Ensure database directory and file exist"""
        try:
            # Create directory if it doesn't exist
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Check if database file exists
            if not self.db_path.exists():
                logger.info(f"Creating new database at {self.db_path}")
                self._create_database()
                return True
            else:
                logger.debug(f"Database already exists at {self.db_path}")
                return self._verify_schema()
                
        except Exception as e:
            logger.error(f"Failed to ensure database exists: {e}")
            return False
    
    def _create_database(self) -> None:
        """Create new database with complete schema"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Enable WAL mode for better concurrency
            if self.config.enable_wal_mode:
                cursor.execute("PRAGMA journal_mode=WAL")
            
            # Set cache size
            cache_size_pages = (self.config.cache_size_mb * 1024 * 1024) // 4096
            cursor.execute(f"PRAGMA cache_size=-{cache_size_pages}")
            
            # Create all tables
            for table_name, schema_sql in DATABASE_SCHEMA.items():
                logger.debug(f"Creating table: {table_name}")
                cursor.execute(schema_sql)
            
            # Create migration tracking table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS schema_migrations (
                    version INTEGER PRIMARY KEY,
                    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT
                )
            """)
            
            # Record initial migration
            cursor.execute(
                "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
                (self.migration_version, "Initial schema creation with script persistence")
            )
            
            conn.commit()
            logger.info("Database schema created successfully")
    
    def _verify_schema(self) -> bool:
        """Verify existing database has correct schema"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # Check if all required tables exist
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' 
                    ORDER BY name
                """)
                existing_tables = {row[0] for row in cursor.fetchall()}
                required_tables = set(DATABASE_SCHEMA.keys())
                
                missing_tables = required_tables - existing_tables
                if missing_tables:
                    logger.warning(f"Missing tables: {missing_tables}")
                    return self._migrate_schema()
                
                # Check migration version
                if 'schema_migrations' in existing_tables:
                    cursor.execute("SELECT MAX(version) FROM schema_migrations")
                    current_version = cursor.fetchone()[0] or 0
                    
                    if current_version < self.migration_version:
                        logger.info(f"Schema migration needed: {current_version} -> {self.migration_version}")
                        return self._migrate_schema()
                
                logger.debug("Database schema verification passed")
                return True
                
        except Exception as e:
            logger.error(f"Schema verification failed: {e}")
            return False
    
    def _migrate_schema(self) -> bool:
        """Apply schema migrations"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # Get current version
                try:
                    cursor.execute("SELECT MAX(version) FROM schema_migrations")
                    current_version = cursor.fetchone()[0] or 0
                except sqlite3.OperationalError:
                    current_version = 0
                
                # Apply missing schema updates
                if current_version < 1:
                    self._apply_migration_v1(cursor)
                
                conn.commit()
                logger.info(f"Schema migrated to version {self.migration_version}")
                return True
                
        except Exception as e:
            logger.error(f"Schema migration failed: {e}")
            return False
    
    def _apply_migration_v1(self, cursor: sqlite3.Cursor) -> None:
        """Apply version 1 migration (initial schema)"""
        logger.info("Applying migration v1: Initial script persistence schema")
        
        # Create migration tracking table first if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS schema_migrations (
                version INTEGER PRIMARY KEY,
                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                description TEXT
            )
        """)
        
        # Create all schema tables
        for table_name, schema_sql in DATABASE_SCHEMA.items():
            logger.debug(f"Creating/updating table: {table_name}")
            cursor.execute(schema_sql)
        
        # Record migration
        cursor.execute(
            "INSERT OR REPLACE INTO schema_migrations (version, description) VALUES (?, ?)",
            (1, "Initial script persistence schema with full-text search")
        )
    
    @contextmanager
    def _get_connection(self):
        """Get database connection with proper configuration"""
        conn = None
        try:
            conn = sqlite3.connect(
                str(self.db_path),
                timeout=30.0,
                check_same_thread=False
            )
            
            # Configure connection
            conn.execute("PRAGMA foreign_keys=ON")
            conn.execute("PRAGMA synchronous=NORMAL")
            
            yield conn
            
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
    
    def backup_database(self, backup_path: Optional[str] = None) -> str:
        """Create database backup"""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"{self.db_path}.backup_{timestamp}"
            
            backup_path = Path(backup_path)
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Use SQLite backup API for consistent backup
            with sqlite3.connect(str(self.db_path)) as source_conn:
                with sqlite3.connect(str(backup_path)) as backup_conn:
                    source_conn.backup(backup_conn)
            
            logger.info(f"Database backed up to: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"Database backup failed: {e}")
            raise
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                stats = {
                    "database_path": str(self.db_path),
                    "database_size_mb": self.db_path.stat().st_size / (1024 * 1024),
                    "tables": {}
                }
                
                # Get table statistics
                for table_name in DATABASE_SCHEMA.keys():
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        row_count = cursor.fetchone()[0]
                        stats["tables"][table_name] = {"rows": row_count}
                    except sqlite3.OperationalError:
                        stats["tables"][table_name] = {"rows": 0, "error": "Table not found"}
                
                # Get schema version
                try:
                    cursor.execute("SELECT MAX(version) FROM schema_migrations")
                    stats["schema_version"] = cursor.fetchone()[0] or 0
                except sqlite3.OperationalError:
                    stats["schema_version"] = 0
                
                return stats
                
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {"error": str(e)}
    
    def optimize_database(self) -> bool:
        """Optimize database performance"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                logger.info("Optimizing database...")
                
                # Analyze tables for query optimization
                cursor.execute("ANALYZE")
                
                # Vacuum to reclaim space and optimize layout
                cursor.execute("VACUUM")
                
                # Update statistics
                cursor.execute("PRAGMA optimize")
                
                logger.info("Database optimization completed")
                return True
                
        except Exception as e:
            logger.error(f"Database optimization failed: {e}")
            return False
    
    def cleanup_old_data(self, retention_days: int = 365) -> int:
        """Clean up old data based on retention policy"""
        try:
            cutoff_date = datetime.now().timestamp() - (retention_days * 24 * 60 * 60)
            
            with self._get_connection() as conn:
                cursor = conn.cursor()
                
                # Clean up old sessions first (cascading foreign keys will handle dependencies)
                cursor.execute("""
                    DELETE FROM streaming_sessions 
                    WHERE started_at < datetime(?, 'unixepoch')
                """, (cutoff_date,))
                sessions_deleted = cursor.rowcount
                
                # Clean up old scripts marked for deletion
                cursor.execute("""
                    DELETE FROM persisted_scripts 
                    WHERE status = 'deleted' 
                    AND generated_at < datetime(?, 'unixepoch')
                """, (cutoff_date,))
                scripts_deleted = cursor.rowcount
                
                # Clean up expired analytics cache
                cursor.execute("DELETE FROM analytics_cache WHERE expires_at < datetime('now')")
                cache_deleted = cursor.rowcount
                
                conn.commit()
                
                total_deleted = sessions_deleted + scripts_deleted + cache_deleted
                logger.info(f"Cleanup completed: {total_deleted} records deleted")
                
                return total_deleted
                
        except Exception as e:
            logger.error(f"Data cleanup failed: {e}")
            return 0


def migrate_from_preview_cache(preview_cache: Dict[str, Any], migrator: DatabaseMigrator) -> int:
    """Migrate existing preview cache data to persistent storage"""
    try:
        if not preview_cache:
            logger.info("No preview cache data to migrate")
            return 0
        
        logger.info(f"Migrating {len(preview_cache)} scripts from preview cache")
        
        with migrator._get_connection() as conn:
            cursor = conn.cursor()
            migrated_count = 0
            
            for form_id, preview_result in preview_cache.items():
                try:
                    # Generate script ID
                    script_id = f"migrated_{form_id}_{int(datetime.now().timestamp())}"
                    
                    # Extract basic data
                    segments = getattr(preview_result, 'script_timeline', None)
                    if not segments or not hasattr(segments, 'segments'):
                        continue
                    
                    # Insert script record
                    cursor.execute("""
                        INSERT INTO persisted_scripts (
                            script_id, form_id, generated_at, generation_method,
                            total_duration_seconds, segment_count, generation_time_ms,
                            html_preview, estimated_metrics, status, created_by
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        script_id,
                        form_id,
                        datetime.now(),
                        "template",  # Assume template-based for migrated data
                        sum(s.estimated_duration_seconds for s in segments.segments),
                        len(segments.segments),
                        int(getattr(preview_result, 'generation_time_seconds', 0) * 1000),
                        getattr(preview_result, 'preview_html', None),
                        json.dumps(getattr(preview_result, 'estimated_metrics', {})),
                        "generated",
                        "migration_tool"
                    ))
                    
                    # Insert segments
                    for i, segment in enumerate(segments.segments):
                        cursor.execute("""
                            INSERT INTO script_segments (
                                segment_id, script_id, segment_type, title, content,
                                duration_seconds, priority, segment_order,
                                triggers, variables, generation_metadata
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            segment.segment_id,
                            script_id,
                            segment.segment_type.value,
                            segment.title,
                            segment.content,
                            segment.estimated_duration_seconds,
                            segment.priority.value,
                            i,
                            json.dumps(segment.triggers),
                            json.dumps(segment.variables),
                            json.dumps({"migrated": True})
                        ))
                    
                    migrated_count += 1
                    
                except Exception as e:
                    logger.warning(f"Failed to migrate script {form_id}: {e}")
                    continue
            
            conn.commit()
            logger.info(f"Successfully migrated {migrated_count} scripts")
            return migrated_count
            
    except Exception as e:
        logger.error(f"Preview cache migration failed: {e}")
        return 0


def initialize_persistence_database(config: Optional[PersistenceConfig] = None) -> bool:
    """Initialize the script persistence database"""
    try:
        migrator = DatabaseMigrator(config)
        
        if not migrator.ensure_database_exists():
            logger.error("Failed to initialize database")
            return False
        
        # Get and log database stats
        stats = migrator.get_database_stats()
        logger.info(f"Database initialized: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return False


# Migration utilities for development and maintenance
def reset_database(config: Optional[PersistenceConfig] = None) -> bool:
    """Reset database (DELETE ALL DATA - use with caution)"""
    try:
        migrator = DatabaseMigrator(config or PersistenceConfig())
        
        # Backup first
        backup_path = migrator.backup_database()
        logger.warning(f"Database backed up to {backup_path} before reset")
        
        # Remove database file
        if migrator.db_path.exists():
            migrator.db_path.unlink()
            logger.warning("Database file deleted")
        
        # Recreate
        return migrator.ensure_database_exists()
        
    except Exception as e:
        logger.error(f"Database reset failed: {e}")
        return False