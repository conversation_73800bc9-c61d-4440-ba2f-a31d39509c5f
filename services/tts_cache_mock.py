#!/usr/bin/env python3
"""
Mock TTS Cache Service
模拟 TTS 缓存服务，用于测试和开发，不依赖 MinIO 和实际的 CosyVoice API
"""

import asyncio
import hashlib
import json
import base64
import struct
from datetime import datetime
from pathlib import Path
from fastapi import FastAPI, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建应用
app = FastAPI(
    title="TTS-Cache-Mock",
    description="模拟 TTS 缓存服务，用于本地开发测试",
    version="1.0.0"
)

# CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class TTSRequest(BaseModel):
    """TTS 请求模型"""
    text: str = Field(..., description="要转换的文本")
    voice: str = Field(default="longanran", description="音色选择")
    format: str = Field(default="pcm", description="音频格式")
    sample_rate: int = Field(default=24000, description="采样率")

def generate_wav_header(data_size: int, sample_rate: int = 24000, channels: int = 1, bits_per_sample: int = 16) -> bytes:
    """生成 WAV 文件头"""
    byte_rate = sample_rate * channels * bits_per_sample // 8
    block_align = channels * bits_per_sample // 8
    
    header = struct.pack(
        '<4sI4s4sIHHIIHH4sI',
        b'RIFF',           # ChunkID
        36 + data_size,    # ChunkSize
        b'WAVE',           # Format
        b'fmt ',           # Subchunk1ID
        16,                # Subchunk1Size (PCM)
        1,                 # AudioFormat (PCM)
        channels,          # NumChannels
        sample_rate,       # SampleRate
        byte_rate,         # ByteRate
        block_align,       # BlockAlign
        bits_per_sample,   # BitsPerSample
        b'data',           # Subchunk2ID
        data_size          # Subchunk2Size
    )
    return header

def generate_mock_audio(text: str, sample_rate: int = 24000) -> bytes:
    """生成模拟音频数据（静音）"""
    # 根据文本长度估算音频长度（假设每个字符 0.1 秒）
    duration_seconds = len(text) * 0.1
    num_samples = int(duration_seconds * sample_rate)
    
    # 生成静音 PCM 数据（16-bit, 单声道）
    pcm_data = bytes(num_samples * 2)  # 每个样本 2 字节
    
    # 添加 WAV 头
    wav_data = generate_wav_header(len(pcm_data), sample_rate) + pcm_data
    
    return wav_data

@app.get("/", response_model=Dict[str, Any])
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "tts-cache-mock",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "minio_connected": True,  # 模拟连接正常
        "circuit_breaker_closed": True  # 模拟熔断器关闭
    }

@app.get("/metrics", response_model=Dict[str, Any])
async def get_metrics():
    """获取服务指标（模拟）"""
    return {
        "total_requests": 100,
        "cache_hits": 80,
        "cache_misses": 20,
        "cache_hit_rate": 0.8,
        "avg_response_time_ms": 50,
        "monthly_chars": 5000,
        "circuit_breaker_state": "closed"
    }

@app.post("/speech/", response_class=Response)
async def synthesize_speech(request: TTSRequest):
    """文本转语音接口（模拟）"""
    logger.info(f"Mock TTS request for text: {request.text[:50]}...")
    
    # 生成缓存键（用于日志）
    cache_key = hashlib.sha256(
        f"{request.text}:{request.voice}:{request.format}:{request.sample_rate}".encode()
    ).hexdigest()
    
    logger.info(f"Cache key: {cache_key}")
    
    # 生成模拟音频
    audio_data = generate_mock_audio(request.text, request.sample_rate)
    
    # 根据请求格式返回
    if request.format == "pcm":
        # 移除 WAV 头，只返回 PCM 数据
        audio_data = audio_data[44:]
        content_type = "audio/pcm"
    elif request.format == "wav":
        content_type = "audio/wav"
    else:
        content_type = "audio/pcm"
    
    return Response(
        content=audio_data,
        media_type=content_type,
        headers={
            "X-Cache-Hit": "false",  # 模拟缓存未命中
            "X-Response-Time-Ms": "50",
            "X-Text-Length": str(len(request.text))
        }
    )

@app.post("/cache/warmup")
async def warmup_cache(texts: list[str]):
    """缓存预热接口（模拟）"""
    logger.info(f"Mock cache warmup for {len(texts)} texts")
    return {
        "message": f"Mock warmup completed for {len(texts)} texts",
        "cached_items": len(texts)
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=22243)