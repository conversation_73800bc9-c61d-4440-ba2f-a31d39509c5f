# Docker Compose file for TTS Cache service

services:
  # AI Live Streamer 主应用
  ai-live-streamer:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai-live-streamer-app
    depends_on:
      - tts-cache
    # 将主应用的端口映射到宿主机，以便访问控制台等
    ports:
      - "8000:8000" # 假设主应用服务在 8000 端口
    volumes:
      - ./src:/app/src
      - ./config.yml:/app/config.yml
      - ./secrets:/app/secrets
      - ./logs:/app/logs
    networks:
      - ai-streamer-net

  # TTS 缓存服务
  tts-cache:
    build:
      context: ./services/tts-cache-cosyvoice
      dockerfile: Dockerfile
    container_name: tts-cache-service
    depends_on:
      - minio
    # 不映射端口到宿主机，实现网络隔离，提高安全性
    # ports:
    #   - "22243:22243" # 仅在需要从外部调试时打开
    volumes:
      - ./services/tts-cache-cosyvoice/logs:/app/logs
      # 使用 Docker Secrets 来安全地传递 API Key
      - type: bind
        source: ./secrets/dashscope_api_key
        target: /run/secrets/dashscope_api_key
        read_only: true
    environment:
      # 配置 MinIO 连接地址，使用服务名 minio
      MINIO_ENDPOINT: "minio:9000"
      MINIO_ACCESS_KEY: "minioadmin"
      MINIO_SECRET_KEY: "minioadmin"
      MINIO_SECURE: "false"
      BUCKET_NAME: "tts-cache"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:22243/').raise_for_status()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ai-streamer-net

  # MinIO 对象存储，用于存放缓存音频
  minio:
    image: minio/minio:latest
    container_name: minio-storage
    volumes:
      - ./data/minio_data:/data
    ports:
      - "9001:9001" # MinIO Console UI
      - "9000:9000" # MinIO API
    environment:
      MINIO_ROOT_USER: "minioadmin"
      MINIO_ROOT_PASSWORD: "minioadmin"
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - ai-streamer-net

# 定义共享网络和数据卷
networks:
  ai-streamer-net:
    driver: bridge

# volumes:
  # minio_data:  # 不再需要，使用本地目录挂载