---
description: Comprehensive session analysis and learning capture
argument-hint: none
allowed-tools: Read, Write, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>(git:*)
---

You are an expert in analyzing development sessions and optimizing AI-human collaboration. Your task is to reflect on today's work session and extract learnings that will improve future interactions.

## Session Analysis Phase

  Review the entire conversation history and identify:

### 1. Problems & Solutions

- **What problems did we encounter?**
  - Initial symptoms reported by user
  - Root causes discovered
  - Solutions implemented
  - Key insights learned

### 2. Code Patterns & Architecture

- **What patterns emerged?**
  - Design decisions made
  - Architecture choices
  - Code relationships discovered
  - Integration points identified

### 3. User Preferences & Workflow

- **How does the user prefer to work?**
  - Communication style
  - Decision-making patterns
  - Quality standards
  - Workflow preferences
  - Direct quotes that reveal preferences

### 4. System Understanding

- **What did we learn about the system?**
  - Component interactions
  - Critical paths and dependencies
  - Failure modes and recovery
  - Performance considerations

### 5. Knowledge Gaps & Improvements

- **Where can we improve?**
  - Misunderstandings that occurred
  - Information that was missing
  - Better approaches discovered
  - Future considerations

## Reflection Output Phase

  Structure your reflection in this format:

  <session_overview>

- Date: [Today's date]
- Primary objectives: [What we set out to do]
- Outcome: [What was accomplished]
- Time invested: [Approximate duration]
  </session_overview>

  <problems_solved>
  [For each major problem:]
  Problem: [Name]

- User Experience: [What the user saw/experienced]
- Technical Cause: [Why it happened]
- Solution Applied: [What we did]
- Key Learning: [Important insight for future]
- Related Files: [Key files involved]
  </problems_solved>

  <patterns_established>
  [For each pattern:]

- Pattern: [Name and description]
- Example: [Specific code/command]
- When to Apply: [Circumstances]
- Why It Matters: [Impact on system]
  </patterns_established>

  <user_preferences>
  [For each preference discovered:]

- Preference: [What user prefers]
- Evidence: "[Direct quote from user]"
- How to Apply: [Specific implementation]
- Priority: [High/Medium/Low]
  </user_preferences>

  <system_relationships>
  [For each relationship:]

- Component A → Component B: [Interaction description]
- Trigger: [What causes interaction]
- Effect: [What happens]
- Monitoring: [How to observe it]
  </system_relationships>

  <knowledge_updates>

## Updates for CLAUDE.md

  [Key points that should be added to project memory:]

- [Point 1]
- [Point 2]

## Code Comments Needed

  [Where comments would help future understanding:]

- File: [Path] - Explain: [What needs clarification]

## Documentation Improvements

  [What should be added to README or docs:]

- Topic: [What to document]
- Location: [Where to add it]
  </knowledge_updates>

  <commands_and_tools>

## Useful Commands Discovered

- `[command]`: [What it does and when to use it]

## Key File Locations

- [Path]: [What it contains and why it matters]

## Debugging Workflows

- When [X] happens: [Step-by-step approach]
  </commands_and_tools>

  <future_improvements>

## For Next Session

- Remember to: [Important points]
- Watch out for: [Potential issues]
- Consider: [Alternative approaches]

## Suggested Enhancements

- Tool/Command: [What could be improved]
- Workflow: [How to optimize]
- Documentation: [What's missing]
  </future_improvements>

  <collaboration_insights>

## Working Better Together

- Communication: [What worked well]
- Efficiency: [How to save time]
- Understanding: [How to clarify requirements]
- Trust: [Where autonomy is appropriate]
  </collaboration_insights>

## Action Items

  [What should be done after this reflection:]

  1. Update CLAUDE.md with: [Specific sections]
  2. Add comments to: [Specific files]
  3. Create documentation for: [Specific topics]
  4. Test: [What needs verification]

  Remember: The goal is to build cumulative knowledge that makes each session more effective than the last. Focus on patterns, preferences, and system understanding that will
  apply to future work.
