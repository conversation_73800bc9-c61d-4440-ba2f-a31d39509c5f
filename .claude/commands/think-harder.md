---
description: Enhanced analytical thinking for complex problems
argument-hint: [problem or question]
---

# Think Harder Command

Engage in intensive analytical thinking to think harder about: **$ARGUMENTS**

## Deep Analysis Protocol

Apply systematic reasoning with the following methodology:

### 1. Problem Clarification

- Define the core question and identify implicit assumptions
- Establish scope, constraints, and success criteria
- Surface potential ambiguities and multiple interpretations

### 2. Multi-Dimensional Analysis

- **Structural decomposition**: Break into fundamental components and dependencies
- **Stakeholder perspectives**: Consider viewpoints of all affected parties
- **Temporal analysis**: Examine short-term vs. long-term implications
- **Causal reasoning**: Map cause-effect relationships and feedback loops
- **Contextual factors**: Assess environmental, cultural, and situational influences

### 3. Critical Evaluation

- Challenge your initial assumptions and identify cognitive biases
- Generate and evaluate alternative hypotheses or solutions
- Conduct pre-mortem analysis: What could go wrong and why?
- Consider opportunity costs and trade-offs for each approach
- Assess confidence levels and sources of uncertainty

### 4. Synthesis and Integration

- Connect insights across different domains and disciplines
- Identify emergent properties from component interactions
- Reconcile apparent contradictions or paradoxes
- Develop meta-insights about the problem-solving process itself

## Output Structure

Present your analysis in this format:

1. **Problem Reframing**: How you understand the core issue
2. **Key Insights**: Most important discoveries from your analysis
3. **Reasoning Chain**: Step-by-step logical progression
4. **Alternatives Considered**: Different approaches evaluated
5. **Uncertainties**: What you don't know and why it matters
6. **Actionable Recommendations**: Specific, implementable next steps

Be thorough yet concise. Show your reasoning process, not just conclusions.
