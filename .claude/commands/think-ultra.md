---
description: Ultra-comprehensive analytical thinking for the most complex problems
argument-hint: [complex problem or question]
---

# Think Ultra Command

Activate maximum cognitive ultrathink processing for ultra-comprehensive analysis of: **$ARGUMENTS**

## Ultra-Analysis Framework

Deploy the most rigorous analytical methodology with exhaustive examination across all dimensions:

### Phase 1: Problem Architecture

- **Ontological analysis**: What is the fundamental nature of this problem?
- **Epistemological examination**: How do we know what we know about this?
- **Semantic decomposition**: Deconstruct all key terms and concepts
- **Boundary analysis**: What's included, excluded, and why?
- **Meta-problem identification**: What's the problem behind the problem?

### Phase 2: Multi-Paradigm Analysis

- **Reductionist approach**: Break down to smallest analyzable components
- **Holistic systems view**: Examine emergent properties and interactions
- **Dialectical reasoning**: Explore contradictions and their resolution
- **Phenomenological perspective**: How is this experienced subjectively?
- **Pragmatic evaluation**: What works in practice vs. theory?

### Phase 3: Cross-Disciplinary Integration

- **Scientific methodology**: Hypothesis formation, testing, validation
- **Mathematical modeling**: Quantitative relationships and patterns
- **Philosophical frameworks**: Logical consistency and ethical implications
- **Historical analysis**: Patterns, precedents, and evolutionary trends
- **Anthropological view**: Cultural, social, and behavioral dimensions
- **Economic analysis**: Resource allocation, incentives, and trade-offs

### Phase 4: Temporal and Spatial Scaling

- **Multi-timescale analysis**: Immediate, short-term, medium-term, long-term
- **Generational thinking**: Impact across multiple generations
- **Spatial scaling**: Local, regional, national, global implications
- **Fractal analysis**: Self-similar patterns across different scales
- **Path dependency**: How history constrains future options

### Phase 5: Uncertainty and Risk Modeling

- **Probabilistic reasoning**: Bayesian updating and confidence intervals
- **Scenario planning**: Multiple future pathways and their implications
- **Black swan analysis**: Low-probability, high-impact events
- **Antifragility assessment**: What benefits from disorder?
- **Robustness testing**: Performance under various stress conditions

### Phase 6: Decision Theory and Game Theory

- **Multi-criteria decision analysis**: Weighted evaluation of options
- **Strategic interactions**: How others' decisions affect outcomes
- **Mechanism design**: Optimal system architecture for desired outcomes
- **Behavioral economics**: Cognitive biases and psychological factors
- **Evolutionary stable strategies**: What persists over time?

### Phase 7: Meta-Cognitive Reflection

- **Cognitive bias audit**: Systematic identification of thinking errors
- **Perspective-taking**: Steel-manning opposing viewpoints
- **Assumption archaeology**: Digging deep into foundational beliefs
- **Reasoning transparency**: Making implicit logic explicit
- **Intellectual humility**: Acknowledging limits and uncertainties

## Ultra-Structured Output

Present your comprehensive analysis using this detailed format:

### 1. Problem Reconceptualization

- **Original question**: As stated
- **Refined question**: After deep analysis
- **Hidden assumptions**: Uncovered implicit beliefs
- **Reframing**: Alternative ways to view the issue

### 2. Multi-Dimensional Mapping

- **Core components**: Essential elements and their relationships
- **System dynamics**: Feedback loops and emergent behaviors
- **Stakeholder ecosystem**: All affected parties and their interests
- **Constraint analysis**: Limitations and boundary conditions

### 3. Evidence and Research Integration

- **Data synthesis**: Relevant empirical findings
- **Theoretical frameworks**: Applicable models and theories
- **Case studies**: Historical precedents and analogies
- **Expert consensus**: Areas of agreement and disagreement

### 4. Comprehensive Option Analysis

- **Option generation**: Creative alternatives beyond obvious choices
- **Multi-criteria evaluation**: Systematic comparison across dimensions
- **Sensitivity analysis**: How robust are conclusions to assumption changes?
- **Implementation pathways**: Practical steps for each option

### 5. Risk and Uncertainty Assessment

- **Known unknowns**: Identified areas of uncertainty
- **Unknown unknowns**: Potential blind spots
- **Failure modes**: What could go wrong and why
- **Mitigation strategies**: Risk reduction approaches

### 6. Strategic Recommendations

- **Primary recommendation**: Best course of action with rationale
- **Alternative pathways**: Backup options and contingencies
- **Implementation roadmap**: Sequenced steps with timelines
- **Success metrics**: How to measure progress and outcomes
- **Adaptation triggers**: When to reconsider the approach

### 7. Meta-Analysis and Reflection

- **Confidence assessment**: How certain are you and why?
- **Key insights**: Most important discoveries
- **Remaining questions**: What still needs investigation?
- **Learning opportunities**: What this analysis teaches about problem-solving

**Note**: This ultra-analysis may require significant processing time and computational resources. The depth of analysis should match the complexity and importance of the problem. Consider using `/think-harder` for less complex issues that don't require the full 7-phase ultra-comprehensive framework.
