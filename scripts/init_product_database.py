#!/usr/bin/env python3
"""初始化产品数据库表

创建产品相关的数据库表结构，包括产品表、标签表和关联表。
同时更新QA和配置表结构。
"""

import sqlite3
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ai_live_streamer.core.config import cfg


def create_product_tables(conn: sqlite3.Connection):
    """创建产品相关表"""
    cursor = conn.cursor()
    
    # 创建产品表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sku TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        description TEXT,
        price REAL,
        stock INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    
    # 创建产品索引
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)")
    
    # 创建标签表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        category TEXT DEFAULT 'other',
        description TEXT,
        usage_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    
    # 创建标签索引
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_tags_category ON tags(category)")
    
    # 创建产品标签关联表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS product_tags (
        product_id INTEGER NOT NULL,
        tag_id INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_id, tag_id),
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
    )
    """)
    
    # 创建关联表索引
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_product_tags_product ON product_tags(product_id)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_product_tags_tag ON product_tags(tag_id)")
    
    print("✅ 产品相关表创建成功")


def update_qa_table(conn: sqlite3.Connection):
    """更新QA表结构，添加product_id字段"""
    cursor = conn.cursor()
    
    # 创建qa_entries表（如果不存在）
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS qa_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER,
        question TEXT NOT NULL,
        answer TEXT NOT NULL,
        category TEXT,
        tags TEXT,
        hit_count INTEGER DEFAULT 0,
        confidence_score REAL DEFAULT 0.8,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id)
    )
    """)
    
    # 创建索引
    cursor.execute("""
    CREATE INDEX IF NOT EXISTS idx_qa_entries_product 
    ON qa_entries(product_id)
    """)
    
    cursor.execute("""
    CREATE INDEX IF NOT EXISTS idx_qa_entries_question 
    ON qa_entries(question)
    """)
    
    # 创建全文搜索表
    cursor.execute("""
    CREATE VIRTUAL TABLE IF NOT EXISTS qa_entries_fts USING fts5(
        question, answer, content=qa_entries
    )
    """)
    
    print("✅ QA表创建/更新成功")


def update_streaming_configs_table(conn: sqlite3.Connection):
    """更新直播配置表，添加product_id字段"""
    cursor = conn.cursor()
    
    # 创建直播配置表（如果不存在）
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS streaming_configs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER,
        config_name TEXT NOT NULL,
        persona_config TEXT,
        streaming_params TEXT,
        script_template TEXT,
        created_by TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id)
    )
    """)
    
    # 创建索引
    cursor.execute("""
    CREATE INDEX IF NOT EXISTS idx_streaming_configs_product 
    ON streaming_configs(product_id)
    """)
    
    # 检查operational_forms表是否需要更新
    cursor.execute("""
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name='operational_forms'
    """)
    if cursor.fetchone():
        # 检查是否已有product_id字段
        cursor.execute("PRAGMA table_info(operational_forms)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'product_id' not in columns:
            cursor.execute("""
            ALTER TABLE operational_forms 
            ADD COLUMN product_id INTEGER REFERENCES products(id)
            """)
            
            cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_operational_forms_product 
            ON operational_forms(product_id)
            """)
            
            print("✅ operational_forms表添加product_id字段成功")
    
    print("✅ 直播配置表更新成功")


def insert_default_data(conn: sqlite3.Connection):
    """插入默认数据"""
    cursor = conn.cursor()
    
    # 检查是否已有产品
    cursor.execute("SELECT COUNT(*) FROM products")
    if cursor.fetchone()[0] > 0:
        print("ℹ️ 产品表已有数据，跳过默认数据插入")
        return
    
    # 插入默认产品
    default_product = {
        'sku': 'DEFAULT-001',
        'name': '通用产品',
        'category': 'other',
        'description': '用于测试和演示的默认产品',
        'price': 0,
        'stock': 9999
    }
    
    cursor.execute("""
    INSERT INTO products (sku, name, category, description, price, stock)
    VALUES (?, ?, ?, ?, ?, ?)
    """, (
        default_product['sku'],
        default_product['name'],
        default_product['category'],
        default_product['description'],
        default_product['price'],
        default_product['stock']
    ))
    
    default_product_id = cursor.lastrowid
    
    # 插入一些默认标签
    default_tags = [
        ('热门', 'feature'),
        ('新品', 'feature'),
        ('优惠', 'price'),
        ('限时', 'price'),
        ('高品质', 'feature'),
        ('年轻人', 'audience'),
        ('家庭', 'audience'),
        ('日常', 'scene'),
        ('礼品', 'scene'),
        ('春季', 'season')
    ]
    
    for tag_name, tag_category in default_tags:
        cursor.execute("""
        INSERT OR IGNORE INTO tags (name, category)
        VALUES (?, ?)
        """, (tag_name, tag_category))
    
    # 为默认产品添加几个标签
    cursor.execute("SELECT id FROM tags WHERE name IN ('热门', '高品质', '日常')")
    tag_ids = [row[0] for row in cursor.fetchall()]
    
    for tag_id in tag_ids:
        cursor.execute("""
        INSERT OR IGNORE INTO product_tags (product_id, tag_id)
        VALUES (?, ?)
        """, (default_product_id, tag_id))
    
    # 更新现有的QA条目关联到默认产品
    cursor.execute("""
    UPDATE qa_entries 
    SET product_id = ? 
    WHERE product_id IS NULL
    """, (default_product_id,))
    
    updated_qa = cursor.rowcount
    if updated_qa > 0:
        print(f"✅ 已将{updated_qa}条QA关联到默认产品")
    
    print(f"✅ 插入默认产品和标签成功，产品ID: {default_product_id}")


def main():
    """主函数"""
    try:
        # 获取数据库路径
        db_path = cfg.database_path
        
        # 确保数据库目录存在
        db_dir = Path(db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 数据库路径: {db_path}")
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        conn.execute("PRAGMA foreign_keys = ON")  # 启用外键约束
        
        try:
            # 创建产品相关表
            create_product_tables(conn)
            
            # 更新QA表
            update_qa_table(conn)
            
            # 更新配置表
            update_streaming_configs_table(conn)
            
            # 插入默认数据
            insert_default_data(conn)
            
            # 提交事务
            conn.commit()
            
            print("\n🎉 数据库初始化成功！")
            
            # 显示表统计
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM products")
            product_count = cursor.fetchone()[0]
            cursor.execute("SELECT COUNT(*) FROM tags")
            tag_count = cursor.fetchone()[0]
            
            print(f"\n📊 统计信息:")
            print(f"  - 产品数量: {product_count}")
            print(f"  - 标签数量: {tag_count}")
            
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
            
    except Exception as e:
        print(f"\n❌ 数据库初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()