#!/usr/bin/env python3
"""
数据库迁移脚本 001: 添加商品关联字段
为运营表单添加对商品管理系统的关联支持

迁移内容：
1. 在operational_forms表中添加selected_product_id字段
2. 在operational_forms表中添加product_price_config字段
3. 更新form_version以支持版本控制
4. 迁移现有数据以保证兼容性
"""

import sqlite3
import json
import sys
import os
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.ai_live_streamer.core.config import cfg
from src.ai_live_streamer.services.persistence.database_manager import DatabaseManager


MIGRATION_VERSION = "001_product_association"
MIGRATION_DESCRIPTION = "添加商品关联字段到运营表单"


def check_migration_applied(db_manager: DatabaseManager) -> bool:
    """检查迁移是否已经应用"""
    try:
        result = db_manager.execute_query(
            "SELECT COUNT(*) FROM schema_version WHERE version = ?", 
            (MIGRATION_VERSION,)
        )
        return result[0][0] > 0
    except Exception:
        # schema_version表可能不存在，表示是全新数据库
        return False


def backup_database(db_path: str) -> str:
    """备份数据库"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        logger.info(f"✅ 数据库已备份到: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"❌ 数据库备份失败: {e}")
        raise


def add_product_association_columns(db_manager: DatabaseManager) -> None:
    """添加商品关联字段"""
    logger.info("📝 添加商品关联字段...")
    
    # 1. 添加selected_product_id字段
    try:
        db_manager.execute_update(
            "ALTER TABLE operational_forms ADD COLUMN selected_product_id INTEGER"
        )
        logger.info("✅ 添加selected_product_id字段成功")
    except Exception as e:
        if "duplicate column name" in str(e).lower():
            logger.info("⚠️ selected_product_id字段已存在，跳过")
        else:
            raise
    
    # 2. 添加product_price_config字段（JSON格式存储价格配置）
    try:
        db_manager.execute_update(
            "ALTER TABLE operational_forms ADD COLUMN product_price_config TEXT"
        )
        logger.info("✅ 添加product_price_config字段成功")
    except Exception as e:
        if "duplicate column name" in str(e).lower():
            logger.info("⚠️ product_price_config字段已存在，跳过")
        else:
            raise
    
    # 3. 添加产品关联时间戳
    try:
        db_manager.execute_update(
            "ALTER TABLE operational_forms ADD COLUMN product_associated_at TIMESTAMP"
        )
        logger.info("✅ 添加product_associated_at字段成功")
    except Exception as e:
        if "duplicate column name" in str(e).lower():
            logger.info("⚠️ product_associated_at字段已存在，跳过")
        else:
            raise


def migrate_existing_forms(db_manager: DatabaseManager) -> None:
    """迁移现有表单数据"""
    logger.info("🔄 迁移现有表单数据...")
    
    # 获取所有现有表单
    forms = db_manager.execute_query(
        "SELECT id, form_version FROM operational_forms WHERE selected_product_id IS NULL"
    )
    
    if not forms:
        logger.info("ℹ️ 没有需要迁移的表单")
        return
    
    logger.info(f"📊 发现 {len(forms)} 个需要迁移的表单")
    
    migrated_count = 0
    for form_id, current_version in forms:
        try:
            # 检查表单是否使用了产品信息
            sections = db_manager.execute_query(
                "SELECT content_json FROM form_sections WHERE form_id = ? AND section_number = 2",
                (form_id,)
            )
            
            if sections:
                section_data = json.loads(sections[0][0])
                
                # 检查是否有产品相关字段，尝试从历史数据推导商品关联
                product_sku = section_data.get('primary_sku')
                if product_sku and product_sku != 'TBD':
                    # 尝试从商品数据库中查找匹配的商品
                    products = db_manager.execute_query(
                        "SELECT id FROM products WHERE sku = ?",
                        (product_sku,)
                    )
                    
                    if products:
                        product_id = products[0][0]
                        price_config = {
                            "use_custom_price": False,
                            "custom_streaming_price": None,
                            "migration_source": "sku_match"
                        }
                        
                        # 更新表单添加商品关联
                        db_manager.execute_update(
                            """UPDATE operational_forms 
                               SET selected_product_id = ?, 
                                   product_price_config = ?,
                                   product_associated_at = CURRENT_TIMESTAMP,
                                   form_version = ?
                               WHERE id = ?""",
                            (product_id, json.dumps(price_config), "1.1", form_id)
                        )
                        migrated_count += 1
                        logger.debug(f"✅ 表单 {form_id} 已关联到商品 {product_id}")
            
            # 即使没有商品关联，也要更新版本号
            if not sections or not db_manager.execute_query(
                "SELECT id FROM operational_forms WHERE id = ? AND form_version = '1.1'",
                (form_id,)
            ):
                db_manager.execute_update(
                    "UPDATE operational_forms SET form_version = '1.1' WHERE id = ?",
                    (form_id,)
                )
                
        except Exception as e:
            logger.error(f"❌ 迁移表单 {form_id} 失败: {e}")
            continue
    
    logger.info(f"✅ 成功迁移 {migrated_count} 个表单的商品关联")


def create_index_for_performance(db_manager: DatabaseManager) -> None:
    """创建性能优化索引"""
    logger.info("🚀 创建性能优化索引...")
    
    indexes = [
        ("idx_operational_forms_product_id", "operational_forms", "selected_product_id"),
        ("idx_operational_forms_created_by", "operational_forms", "created_by"),
        ("idx_form_sections_form_id_section", "form_sections", "form_id, section_number"),
    ]
    
    for index_name, table_name, columns in indexes:
        try:
            db_manager.execute_update(
                f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({columns})"
            )
            logger.info(f"✅ 创建索引 {index_name} 成功")
        except Exception as e:
            logger.warning(f"⚠️ 创建索引 {index_name} 失败: {e}")


def record_migration(db_manager: DatabaseManager) -> None:
    """记录迁移版本"""
    logger.info("📝 记录迁移版本...")
    
    db_manager.execute_update(
        "INSERT OR REPLACE INTO schema_version (version, applied_at) VALUES (?, CURRENT_TIMESTAMP)",
        (MIGRATION_VERSION,)
    )
    logger.info(f"✅ 迁移版本 {MIGRATION_VERSION} 已记录")


def verify_migration(db_manager: DatabaseManager) -> bool:
    """验证迁移结果"""
    logger.info("🔍 验证迁移结果...")
    
    try:
        # 验证新字段存在
        schema_info = db_manager.execute_query("PRAGMA table_info(operational_forms)")
        column_names = [col[1] for col in schema_info]
        
        required_columns = ['selected_product_id', 'product_price_config', 'product_associated_at']
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            logger.error(f"❌ 缺少字段: {missing_columns}")
            return False
        
        # 验证迁移版本记录
        version_check = db_manager.execute_query(
            "SELECT COUNT(*) FROM schema_version WHERE version = ?",
            (MIGRATION_VERSION,)
        )
        
        if version_check[0][0] == 0:
            logger.error("❌ 迁移版本未正确记录")
            return False
        
        # 统计迁移结果
        stats = db_manager.execute_query("""
            SELECT 
                COUNT(*) as total_forms,
                COUNT(selected_product_id) as forms_with_products,
                COUNT(CASE WHEN form_version = '1.1' THEN 1 END) as updated_forms
            FROM operational_forms
        """)
        
        total, with_products, updated = stats[0]
        logger.info(f"📊 迁移统计:")
        logger.info(f"   总表单数: {total}")
        logger.info(f"   关联商品的表单: {with_products}")
        logger.info(f"   版本已更新的表单: {updated}")
        
        logger.info("✅ 迁移验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 迁移验证失败: {e}")
        return False


def main():
    """执行迁移"""
    logger.info("🚀 开始执行数据库迁移 001: 添加商品关联字段")
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager(cfg.database_path)
        db_manager.initialize_schema()
        
        # 检查迁移是否已应用
        if check_migration_applied(db_manager):
            logger.info("ℹ️ 迁移已经应用，跳过执行")
            return True
        
        # 备份数据库
        backup_path = backup_database(cfg.database_path)
        
        # 执行迁移步骤
        logger.info("📝 开始执行迁移步骤...")
        
        # 1. 添加新字段
        add_product_association_columns(db_manager)
        
        # 2. 迁移现有数据
        migrate_existing_forms(db_manager)
        
        # 3. 创建性能索引
        create_index_for_performance(db_manager)
        
        # 4. 记录迁移版本
        record_migration(db_manager)
        
        # 5. 验证迁移结果
        if not verify_migration(db_manager):
            raise Exception("迁移验证失败")
        
        logger.info("🎉 数据库迁移 001 执行成功")
        logger.info(f"💾 数据库备份位置: {backup_path}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库迁移失败: {e}")
        logger.error("🔄 请检查备份并考虑回滚")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)