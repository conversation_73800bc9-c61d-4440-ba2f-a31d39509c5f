#!/usr/bin/env python3
"""测试产品-QA-配置完整数据流

验证从产品创建到QA管理的完整流程。
"""

import asyncio
import aiohttp
import json
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# API基础URL
BASE_URL = "http://localhost:8000"


async def test_product_creation():
    """测试产品创建"""
    print("\n1️⃣ 测试产品创建...")
    
    product_data = {
        "sku": f"TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "name": "测试产品 - 智能音箱",
        "category": "electronics",
        "description": "这是一个用于测试的智能音箱产品",
        "price": 299.99,
        "stock": 100,
        "tags": ["智能", "音箱", "测试"]
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{BASE_URL}/api/v1/products",
            json=product_data
        ) as response:
            if response.status == 201:
                product = await response.json()
                print(f"✅ 产品创建成功: ID={product['id']}, SKU={product['sku']}")
                return product['id']
            else:
                error = await response.text()
                print(f"❌ 产品创建失败: {error}")
                return None


async def test_product_list():
    """测试产品列表"""
    print("\n2️⃣ 测试产品列表...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/v1/products") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 获取产品列表成功: 共{data.get('total', 0)}个产品")
                if data.get('items'):
                    for product in data['items'][:3]:  # 显示前3个
                        print(f"   - {product['name']} (SKU: {product['sku']})")
                return True
            else:
                print(f"❌ 获取产品列表失败")
                return False


async def test_add_qa_to_product(product_id: int):
    """测试为产品添加QA"""
    print(f"\n3️⃣ 测试为产品{product_id}添加QA...")
    
    qa_items = [
        {
            "question": "这个音箱的音质如何？",
            "answer": "采用高保真音频技术，支持Hi-Res音质，低音浑厚，高音清澈，能够完美还原音乐细节。",
            "category": "产品特性",
            "tags": ["音质", "技术"]
        },
        {
            "question": "支持哪些连接方式？",
            "answer": "支持蓝牙5.0、WiFi、AUX音频线等多种连接方式，还可以通过App进行远程控制。",
            "category": "功能",
            "tags": ["连接", "控制"]
        },
        {
            "question": "续航时间多长？",
            "answer": "内置5000mAh大容量电池，正常音量下可连续播放12小时，待机时间长达30天。",
            "category": "续航",
            "tags": ["电池", "续航"]
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        success_count = 0
        for qa in qa_items:
            async with session.post(
                f"{BASE_URL}/api/v1/products/{product_id}/qa",
                json=qa
            ) as response:
                if response.status == 201:
                    result = await response.json()
                    success_count += 1
                    print(f"   ✅ QA添加成功: {qa['question'][:30]}...")
                else:
                    print(f"   ❌ QA添加失败: {qa['question'][:30]}...")
        
        print(f"✅ 成功添加{success_count}/{len(qa_items)}条QA")
        return success_count > 0


async def test_get_product_qa(product_id: int):
    """测试获取产品QA列表"""
    print(f"\n4️⃣ 测试获取产品{product_id}的QA列表...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/v1/products/{product_id}/qa") as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 获取QA列表成功: 共{data['total']}条")
                for qa in data['items']:
                    print(f"   - Q: {qa['question'][:40]}...")
                    print(f"     A: {qa['answer'][:40]}...")
                return True
            else:
                print(f"❌ 获取QA列表失败")
                return False


async def test_search_qa(product_id: int, query: str):
    """测试搜索产品QA"""
    print(f"\n5️⃣ 测试搜索产品{product_id}的QA: '{query}'...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(
            f"{BASE_URL}/api/v1/products/{product_id}/qa/search",
            params={"q": query, "limit": 5}
        ) as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ 搜索成功: 找到{data['count']}条相关QA")
                for result in data['results']:
                    print(f"   - {result['question'][:50]}...")
                return True
            else:
                print(f"❌ 搜索失败")
                return False


async def test_product_stats(product_id: int):
    """测试获取产品统计信息"""
    print(f"\n6️⃣ 测试获取产品{product_id}的统计信息...")
    
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/api/v1/products/{product_id}/stats") as response:
            if response.status == 200:
                stats = await response.json()
                print(f"✅ 获取统计信息成功:")
                print(f"   - QA数量: {stats['qa_count']}")
                print(f"   - 配置数量: {stats['config_count']}")
                print(f"   - QA总命中: {stats['total_qa_hits']}")
                return True
            else:
                print(f"❌ 获取统计信息失败")
                return False


async def test_ui_access():
    """测试UI控制台访问"""
    print("\n7️⃣ 测试UI控制台访问...")
    
    endpoints = [
        ("/console/products", "产品管理控制台"),
        ("/api/qa/management", "QA管理界面（旧版）")
    ]
    
    async with aiohttp.ClientSession() as session:
        for endpoint, name in endpoints:
            async with session.get(f"{BASE_URL}{endpoint}") as response:
                if response.status == 200:
                    content = await response.text()
                    if "<html" in content.lower():
                        print(f"   ✅ {name}: {BASE_URL}{endpoint}")
                    else:
                        print(f"   ⚠️ {name}返回了非HTML内容")
                else:
                    print(f"   ❌ {name}访问失败 (状态码: {response.status})")


async def main():
    """主测试流程"""
    print("=" * 60)
    print("🧪 产品-QA-配置数据流测试")
    print("=" * 60)
    
    try:
        # 1. 创建测试产品
        product_id = await test_product_creation()
        if not product_id:
            print("\n❌ 测试中止: 产品创建失败")
            return
        
        # 2. 获取产品列表
        await test_product_list()
        
        # 3. 为产品添加QA
        await test_add_qa_to_product(product_id)
        
        # 4. 获取产品QA列表
        await test_get_product_qa(product_id)
        
        # 5. 搜索QA
        await test_search_qa(product_id, "音质")
        
        # 6. 获取产品统计
        await test_product_stats(product_id)
        
        # 7. 测试UI访问
        await test_ui_access()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！")
        print("\n📊 测试总结:")
        print(f"   - 创建的测试产品ID: {product_id}")
        print(f"   - 产品管理控制台: {BASE_URL}/console/products")
        print(f"   - 产品QA管理: {BASE_URL}/console/products/{product_id}/qa")
        print(f"   - API文档: {BASE_URL}/docs")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 检查服务是否运行
    import requests
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=2)
        if response.status_code == 200:
            print("✅ 服务正在运行")
            asyncio.run(main())
        else:
            print(f"⚠️ 服务返回异常状态码: {response.status_code}")
    except requests.exceptions.RequestException:
        print("❌ 无法连接到服务，请确保服务正在运行:")
        print("   python scripts/run_server.py")
        sys.exit(1)