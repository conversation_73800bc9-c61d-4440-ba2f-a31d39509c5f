#!/usr/bin/env python3
"""
数据库迁移执行器
自动检测并执行需要的数据库迁移
"""

import os
import sys
import importlib.util
from pathlib import Path
from typing import List, Tuple
from loguru import logger

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.ai_live_streamer.core.config import cfg
from src.ai_live_streamer.services.persistence.database_manager import DatabaseManager


def discover_migrations(migrations_dir: Path) -> List[Tuple[str, Path]]:
    """发现可用的迁移脚本"""
    migrations = []
    
    if not migrations_dir.exists():
        logger.warning(f"迁移目录不存在: {migrations_dir}")
        return migrations
    
    for migration_file in sorted(migrations_dir.glob("*.py")):
        if migration_file.name.startswith("__"):
            continue
            
        migration_name = migration_file.stem
        migrations.append((migration_name, migration_file))
    
    return migrations


def load_migration_module(migration_path: Path):
    """动态加载迁移模块"""
    spec = importlib.util.spec_from_file_location(
        migration_path.stem, 
        migration_path
    )
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def get_applied_migrations(db_manager: DatabaseManager) -> List[str]:
    """获取已应用的迁移列表"""
    try:
        result = db_manager.execute_query("SELECT version FROM schema_version ORDER BY version")
        return [row[0] for row in result]
    except Exception as e:
        logger.debug(f"无法获取已应用的迁移: {e}")
        return []


def main():
    """执行数据库迁移"""
    logger.info("🚀 数据库迁移系统启动")
    
    try:
        # 初始化数据库
        db_manager = DatabaseManager(cfg.database_path)
        db_manager.initialize_schema()
        
        # 发现迁移脚本
        migrations_dir = Path(__file__).parent / "migrations"
        available_migrations = discover_migrations(migrations_dir)
        
        if not available_migrations:
            logger.info("ℹ️ 没有发现迁移脚本")
            return True
        
        logger.info(f"📁 发现 {len(available_migrations)} 个迁移脚本")
        
        # 获取已应用的迁移
        applied_migrations = get_applied_migrations(db_manager)
        logger.info(f"📝 已应用 {len(applied_migrations)} 个迁移")
        
        # 执行待应用的迁移
        pending_migrations = [
            (name, path) for name, path in available_migrations 
            if name not in applied_migrations
        ]
        
        if not pending_migrations:
            logger.info("✅ 所有迁移都已应用，数据库是最新的")
            return True
        
        logger.info(f"🔄 需要应用 {len(pending_migrations)} 个迁移:")
        for name, _ in pending_migrations:
            logger.info(f"   - {name}")
        
        # 逐个执行迁移
        success_count = 0
        for migration_name, migration_path in pending_migrations:
            logger.info(f"🔧 执行迁移: {migration_name}")
            
            try:
                # 动态加载迁移模块
                migration_module = load_migration_module(migration_path)
                
                # 执行迁移的main函数
                if hasattr(migration_module, 'main'):
                    success = migration_module.main()
                    if success:
                        success_count += 1
                        logger.info(f"✅ 迁移 {migration_name} 执行成功")
                    else:
                        logger.error(f"❌ 迁移 {migration_name} 执行失败")
                        break
                else:
                    logger.error(f"❌ 迁移脚本 {migration_name} 缺少main函数")
                    break
                    
            except Exception as e:
                logger.error(f"❌ 执行迁移 {migration_name} 时发生错误: {e}")
                break
        
        # 总结执行结果
        if success_count == len(pending_migrations):
            logger.info(f"🎉 所有 {success_count} 个迁移执行成功")
            return True
        else:
            logger.error(f"❌ {len(pending_migrations) - success_count} 个迁移执行失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 迁移系统执行失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)