#!/usr/bin/env python
"""
并发合成场景 E2E 测试

测试目标：
1. 验证多个客户端同时请求同一音频片段时，只创建一个 TTS 合成任务
2. 确认所有客户端都能成功接收内容
3. 检查监控指标中无重复合成记录

Author: Claude Code
Date: 2025-08-10
"""

import asyncio
import json
import time
import base64
import struct
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import aiohttp
import websockets
from loguru import logger


class ConcurrentSynthesisTest:
    """并发合成测试类"""
    
    def __init__(self, api_base: str = "http://127.0.0.1:8000"):
        self.api_base = api_base
        self.ws_base = api_base.replace("http://", "ws://").replace("https://", "wss://")
        self.session_id: Optional[str] = None
        self.test_results = {
            "test_name": "concurrent_synthesis",
            "start_time": None,
            "end_time": None,
            "passed": False,
            "errors": [],
            "metrics": {}
        }
        
    async def start_session(self) -> str:
        """启动流会话"""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.api_base}/api/control/start",
                json={"script_id": "test_script"}
            ) as response:
                if response.status != 200:
                    raise Exception(f"Failed to start session: {response.status}")
                data = await response.json()
                return data["session_id"]
    
    async def stop_session(self, session_id: str):
        """停止流会话"""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.api_base}/api/control/stop",
                json={"session_id": session_id}
            ) as response:
                if response.status != 200:
                    logger.warning(f"Failed to stop session: {response.status}")
    
    async def client_request_content(self, client_id: str, session_id: str, 
                                    target_index: int, delay_ms: int = 0) -> Dict[str, Any]:
        """模拟客户端请求内容
        
        Args:
            client_id: 客户端标识
            session_id: 会话ID
            target_index: 请求的内容索引
            delay_ms: 延迟时间（毫秒）
            
        Returns:
            请求结果
        """
        result = {
            "client_id": client_id,
            "success": False,
            "received_audio": False,
            "audio_size": 0,
            "response_time_ms": 0,
            "error": None
        }
        
        try:
            # 延迟启动（用于制造并发）
            if delay_ms > 0:
                await asyncio.sleep(delay_ms / 1000.0)
            
            start_time = time.time()
            logger.info(f"[{client_id}] 开始连接 WebSocket...")
            
            # 连接 WebSocket
            ws_url = f"{self.ws_base}/ws/audio/v2/{session_id}"
            async with websockets.connect(ws_url) as ws:
                logger.info(f"[{client_id}] WebSocket 连接成功")
                
                # 发送内容请求
                request = {
                    "type": "content_request",
                    "current_index": target_index,
                    "request_id": f"{client_id}_{target_index}",
                    "client_id": client_id
                }
                
                await ws.send(json.dumps(request))
                logger.info(f"[{client_id}] 发送内容请求: index={target_index}")
                
                # 等待响应（最多等待 10 秒）
                max_wait = 10.0
                retry_count = 0
                
                while (time.time() - start_time) < max_wait:
                    try:
                        message = await asyncio.wait_for(ws.recv(), timeout=1.0)
                        
                        # 处理二进制音频数据
                        if isinstance(message, bytes):
                            logger.info(f"[{client_id}] 收到二进制音频数据: {len(message)} bytes")
                            result["received_audio"] = True
                            result["audio_size"] = len(message)
                            result["success"] = True
                            break
                        
                        # 处理 JSON 消息
                        data = json.loads(message)
                        msg_type = data.get("type")
                        
                        if msg_type == "content_next":
                            # 收到音频内容
                            audio_base64 = data.get("audio_base64")
                            if audio_base64:
                                audio_data = base64.b64decode(audio_base64)
                                logger.info(f"[{client_id}] 收到音频内容: {len(audio_data)} bytes")
                                result["received_audio"] = True
                                result["audio_size"] = len(audio_data)
                                result["success"] = True
                                break
                        
                        elif msg_type == "content_not_ready":
                            # 内容未准备好，需要重试
                            retry_count += 1
                            retry_after = data.get("retry_after_ms", 1000)
                            logger.info(f"[{client_id}] 内容未准备好，{retry_after}ms 后重试 (重试 {retry_count})")
                            await asyncio.sleep(retry_after / 1000.0)
                            
                            # 重新发送请求
                            await ws.send(json.dumps(request))
                        
                        elif msg_type == "error":
                            logger.error(f"[{client_id}] 收到错误: {data}")
                            result["error"] = data.get("message", "Unknown error")
                            break
                    
                    except asyncio.TimeoutError:
                        continue
                
                result["response_time_ms"] = (time.time() - start_time) * 1000
                
                if result["success"]:
                    logger.success(f"[{client_id}] ✅ 成功接收音频，耗时: {result['response_time_ms']:.1f}ms")
                else:
                    logger.error(f"[{client_id}] ❌ 未能接收音频")
        
        except Exception as e:
            logger.error(f"[{client_id}] 客户端请求失败: {e}")
            result["error"] = str(e)
        
        return result
    
    async def get_synthesis_metrics(self) -> Dict[str, Any]:
        """获取合成指标"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_base}/api/control/stats"
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("synthesis_metrics", {})
        except Exception as e:
            logger.error(f"获取合成指标失败: {e}")
        return {}
    
    async def run_test(self):
        """运行并发合成测试"""
        self.test_results["start_time"] = datetime.now().isoformat()
        
        try:
            logger.info("=" * 60)
            logger.info("开始并发合成 E2E 测试")
            logger.info("=" * 60)
            
            # 1. 启动会话
            logger.info("步骤 1: 启动流会话...")
            self.session_id = await self.start_session()
            logger.info(f"✅ 会话启动成功: {self.session_id}")
            
            # 等待系统初始化
            await asyncio.sleep(2)
            
            # 2. 获取初始合成指标
            initial_metrics = await self.get_synthesis_metrics()
            initial_tasks_created = initial_metrics.get("synthesis_tasks_created", 0)
            initial_tasks_waited = initial_metrics.get("synthesis_tasks_waited", 0)
            logger.info(f"初始指标: 创建={initial_tasks_created}, 等待={initial_tasks_waited}")
            
            # 3. 模拟两个客户端并发请求同一内容
            logger.info("\n步骤 2: 模拟并发请求...")
            target_index = 5  # 请求第 5 个内容项
            
            # 创建两个客户端任务，间隔 100ms
            client_tasks = [
                self.client_request_content("client_1", self.session_id, target_index, delay_ms=0),
                self.client_request_content("client_2", self.session_id, target_index, delay_ms=100)
            ]
            
            # 并发执行
            results = await asyncio.gather(*client_tasks)
            
            # 4. 分析结果
            logger.info("\n步骤 3: 分析测试结果...")
            
            success_count = sum(1 for r in results if r["success"])
            audio_received = sum(1 for r in results if r["received_audio"])
            
            logger.info(f"成功客户端: {success_count}/2")
            logger.info(f"接收音频: {audio_received}/2")
            
            for result in results:
                logger.info(f"  {result['client_id']}: "
                          f"成功={result['success']}, "
                          f"音频大小={result['audio_size']} bytes, "
                          f"耗时={result['response_time_ms']:.1f}ms")
            
            # 5. 获取最终合成指标
            await asyncio.sleep(1)  # 等待指标更新
            final_metrics = await self.get_synthesis_metrics()
            final_tasks_created = final_metrics.get("synthesis_tasks_created", 0)
            final_tasks_waited = final_metrics.get("synthesis_tasks_waited", 0)
            
            new_tasks_created = final_tasks_created - initial_tasks_created
            new_tasks_waited = final_tasks_waited - initial_tasks_waited
            
            logger.info(f"\n合成指标变化:")
            logger.info(f"  新创建任务: {new_tasks_created}")
            logger.info(f"  避免重复任务: {new_tasks_waited}")
            
            # 6. 验证测试结果
            test_passed = True
            errors = []
            
            # 验证：两个客户端都成功接收内容
            if success_count != 2:
                test_passed = False
                errors.append(f"只有 {success_count}/2 个客户端成功")
            
            # 验证：只创建了一个合成任务（允许误差）
            if new_tasks_created > 2:  # 允许少量额外任务
                test_passed = False
                errors.append(f"创建了过多合成任务: {new_tasks_created}")
            
            # 验证：至少有一个客户端等待了已存在的任务
            if new_tasks_waited < 1:
                logger.warning(f"⚠️ 没有检测到任务等待，可能请求间隔过大")
            
            # 更新测试结果
            self.test_results["passed"] = test_passed
            self.test_results["errors"] = errors
            self.test_results["metrics"] = {
                "success_count": success_count,
                "audio_received": audio_received,
                "new_tasks_created": new_tasks_created,
                "new_tasks_waited": new_tasks_waited,
                "client_results": results
            }
            
            # 7. 输出测试结论
            logger.info("\n" + "=" * 60)
            if test_passed:
                logger.success("✅ 并发合成测试通过！")
                logger.info("  - 两个客户端都成功接收内容")
                logger.info(f"  - 合成任务数量合理: {new_tasks_created}")
                if new_tasks_waited > 0:
                    logger.info(f"  - 成功避免重复合成: {new_tasks_waited} 次")
            else:
                logger.error("❌ 并发合成测试失败！")
                for error in errors:
                    logger.error(f"  - {error}")
            logger.info("=" * 60)
            
        except Exception as e:
            logger.error(f"测试执行失败: {e}")
            self.test_results["passed"] = False
            self.test_results["errors"].append(str(e))
        
        finally:
            # 清理：停止会话
            if self.session_id:
                await self.stop_session(self.session_id)
                logger.info("会话已停止")
            
            self.test_results["end_time"] = datetime.now().isoformat()
        
        return self.test_results


async def main():
    """主函数"""
    # 配置日志
    logger.remove()
    logger.add(sys.stdout, level="DEBUG", colorize=True)
    
    # 运行测试
    test = ConcurrentSynthesisTest()
    results = await test.run_test()
    
    # 返回退出码
    sys.exit(0 if results["passed"] else 1)


if __name__ == "__main__":
    asyncio.run(main())