#!/usr/bin/env python3
"""
Request ID Concurrency Control E2E Test

Tests the request_id mechanism for handling concurrent requests and preventing
race conditions in the streaming system.

Test Scenarios:
1. Normal request/response with request_id matching
2. Rapid consecutive requests (potential race condition)
3. Out-of-order response handling
4. Request timeout and retry with proper request_id tracking
"""

import asyncio
import json
import websockets
import aiohttp
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from loguru import logger
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Test configuration
TEST_CONFIG = {
    "api_base_url": "http://127.0.0.1:8000/api/control",
    "ws_base_url": "ws://127.0.0.1:8000/ws/audio/v2",
    "timeout_seconds": 30,
    "rapid_request_count": 5,
    "request_interval_ms": 100  # Interval between rapid requests
}


@dataclass
class RequestTracker:
    """Track request/response pairs"""
    request_id: str
    index: int
    sent_time: float
    response_time: Optional[float] = None
    response_index: Optional[int] = None
    matched: bool = False
    ignored: bool = False


class RequestIDConcurrencyTest:
    """Test request_id concurrency control mechanism"""
    
    def __init__(self):
        self.session_id = None
        self.websocket = None
        self.request_trackers: Dict[str, RequestTracker] = {}
        self.responses_received: List[Dict[str, Any]] = []
        self.test_passed = True
        self.error_message = None
        
    async def run(self) -> bool:
        """Run the complete test suite"""
        try:
            logger.info("🚀 Starting Request ID Concurrency Control Test")
            
            # Start stream session
            await self._start_stream()
            
            # Connect WebSocket
            await self._connect_websocket()
            
            # Test scenarios
            await self._test_normal_request_response()
            await self._test_rapid_consecutive_requests()
            await self._test_out_of_order_handling()
            
            # Analyze results
            self._analyze_results()
            
            return self.test_passed
            
        except Exception as e:
            logger.error(f"❌ Test failed with exception: {e}")
            self.test_passed = False
            self.error_message = str(e)
            return False
            
        finally:
            await self._cleanup()
    
    async def _start_stream(self):
        """Start a stream session"""
        logger.info("📡 Starting stream session...")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{TEST_CONFIG['api_base_url']}/start-stream") as resp:
                if resp.status != 200:
                    raise Exception(f"Failed to start stream: {resp.status}")
                    
                data = await resp.json()
                self.session_id = data["session_id"]
                logger.info(f"✅ Stream started: {self.session_id}")
    
    async def _connect_websocket(self):
        """Connect to WebSocket"""
        logger.info("🔌 Connecting to WebSocket...")
        
        ws_url = f"{TEST_CONFIG['ws_base_url']}?session_id={self.session_id}"
        self.websocket = await websockets.connect(ws_url)
        
        # Wait for connection confirmation
        msg = await self.websocket.recv()
        data = json.loads(msg)
        
        if data["type"] != "connection_established":
            raise Exception(f"Unexpected message type: {data['type']}")
            
        logger.info("✅ WebSocket connected")
    
    async def _test_normal_request_response(self):
        """Test normal request/response flow with request_id"""
        logger.info("\n📋 Test 1: Normal Request/Response Flow")
        
        # Send request with specific request_id
        request_id = f"test_normal_{int(time.time() * 1000)}"
        index = 0
        
        request = {
            "type": "content_request",
            "index": index,
            "request_id": request_id
        }
        
        # Track request
        tracker = RequestTracker(
            request_id=request_id,
            index=index,
            sent_time=time.time()
        )
        self.request_trackers[request_id] = tracker
        
        # Send request
        await self.websocket.send(json.dumps(request))
        logger.info(f"📤 Sent request: {request_id} for index {index}")
        
        # Wait for response
        response = await self._wait_for_content_response(timeout=5.0)
        
        if response:
            # Verify request_id matches
            if response.get("request_id") == request_id:
                tracker.matched = True
                tracker.response_time = time.time()
                tracker.response_index = response.get("index")
                logger.success(f"✅ Request ID matched: {request_id}")
            else:
                logger.error(f"❌ Request ID mismatch: expected {request_id}, got {response.get('request_id')}")
                self.test_passed = False
        else:
            logger.error("❌ No response received for normal request")
            self.test_passed = False
    
    async def _test_rapid_consecutive_requests(self):
        """Test rapid consecutive requests to trigger potential race conditions"""
        logger.info("\n📋 Test 2: Rapid Consecutive Requests")
        
        # Create multiple requests quickly
        tasks = []
        
        for i in range(TEST_CONFIG["rapid_request_count"]):
            request_id = f"test_rapid_{i}_{int(time.time() * 1000)}"
            index = i + 1  # Start from index 1
            
            request = {
                "type": "content_request",
                "index": index,
                "request_id": request_id
            }
            
            # Track request
            tracker = RequestTracker(
                request_id=request_id,
                index=index,
                sent_time=time.time()
            )
            self.request_trackers[request_id] = tracker
            
            # Send request
            await self.websocket.send(json.dumps(request))
            logger.info(f"📤 Sent rapid request {i+1}/{TEST_CONFIG['rapid_request_count']}: {request_id}")
            
            # Small delay between requests
            await asyncio.sleep(TEST_CONFIG["request_interval_ms"] / 1000)
        
        # Collect responses
        logger.info("📥 Collecting responses...")
        response_count = 0
        timeout = 10.0
        start_time = time.time()
        
        while response_count < TEST_CONFIG["rapid_request_count"] and (time.time() - start_time) < timeout:
            response = await self._wait_for_content_response(timeout=1.0)
            if response:
                response_count += 1
                request_id = response.get("request_id")
                
                if request_id in self.request_trackers:
                    tracker = self.request_trackers[request_id]
                    tracker.matched = True
                    tracker.response_time = time.time()
                    tracker.response_index = response.get("index")
                    logger.info(f"✅ Matched response {response_count}: {request_id}")
                else:
                    logger.warning(f"⚠️ Received response with unknown request_id: {request_id}")
        
        if response_count < TEST_CONFIG["rapid_request_count"]:
            logger.warning(f"⚠️ Only received {response_count}/{TEST_CONFIG['rapid_request_count']} responses")
            self.test_passed = False
    
    async def _test_out_of_order_handling(self):
        """Test handling of out-of-order responses"""
        logger.info("\n📋 Test 3: Out-of-Order Response Handling")
        
        # Send request for index that might not exist yet
        request_id = f"test_oor_{int(time.time() * 1000)}"
        index = 99  # High index that might not exist
        
        request = {
            "type": "content_request",
            "index": index,
            "request_id": request_id
        }
        
        # Track request
        tracker = RequestTracker(
            request_id=request_id,
            index=index,
            sent_time=time.time()
        )
        self.request_trackers[request_id] = tracker
        
        # Send request
        await self.websocket.send(json.dumps(request))
        logger.info(f"📤 Sent out-of-order request: {request_id} for index {index}")
        
        # Wait for response (might be an error)
        response = await self._wait_for_any_response(timeout=5.0)
        
        if response:
            if response["type"] == "error":
                # Error response should still contain request_id
                if response.get("request_id") == request_id:
                    logger.success(f"✅ Error response contains correct request_id: {request_id}")
                    tracker.matched = True
                    tracker.response_time = time.time()
                else:
                    logger.error(f"❌ Error response missing request_id")
                    self.test_passed = False
            elif response["type"] == "content":
                # If we somehow got content, verify request_id
                if response.get("request_id") == request_id:
                    tracker.matched = True
                    tracker.response_time = time.time()
                    tracker.response_index = response.get("index")
    
    async def _wait_for_content_response(self, timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """Wait for a content response message"""
        try:
            start_time = time.time()
            while (time.time() - start_time) < timeout:
                msg = await asyncio.wait_for(self.websocket.recv(), timeout=0.5)
                data = json.loads(msg)
                
                self.responses_received.append(data)
                
                if data["type"] == "content":
                    return data
                    
        except asyncio.TimeoutError:
            pass
            
        return None
    
    async def _wait_for_any_response(self, timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """Wait for any response message"""
        try:
            msg = await asyncio.wait_for(self.websocket.recv(), timeout=timeout)
            data = json.loads(msg)
            self.responses_received.append(data)
            return data
        except asyncio.TimeoutError:
            return None
    
    def _analyze_results(self):
        """Analyze test results"""
        logger.info("\n📊 Analyzing Test Results")
        
        total_requests = len(self.request_trackers)
        matched_requests = sum(1 for t in self.request_trackers.values() if t.matched)
        
        logger.info(f"Total requests sent: {total_requests}")
        logger.info(f"Matched responses: {matched_requests}")
        logger.info(f"Match rate: {(matched_requests/total_requests)*100:.1f}%")
        
        # Check for any unmatched requests
        unmatched = [t for t in self.request_trackers.values() if not t.matched]
        if unmatched:
            logger.warning(f"⚠️ {len(unmatched)} unmatched requests:")
            for tracker in unmatched:
                logger.warning(f"  - {tracker.request_id} (index: {tracker.index})")
        
        # Calculate response times
        response_times = []
        for tracker in self.request_trackers.values():
            if tracker.matched and tracker.response_time:
                response_time = (tracker.response_time - tracker.sent_time) * 1000
                response_times.append(response_time)
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            logger.info(f"Average response time: {avg_response_time:.1f}ms")
            logger.info(f"Min response time: {min(response_times):.1f}ms")
            logger.info(f"Max response time: {max(response_times):.1f}ms")
        
        # Check if all critical tests passed
        if matched_requests < total_requests * 0.9:  # Allow 10% failure rate
            logger.error("❌ Too many unmatched requests")
            self.test_passed = False
        
        # Verify no responses without request_id
        responses_without_id = [r for r in self.responses_received 
                               if r.get("type") == "content" and not r.get("request_id")]
        if responses_without_id:
            logger.error(f"❌ {len(responses_without_id)} responses missing request_id")
            self.test_passed = False
            
        if self.test_passed:
            logger.success("✅ All request_id concurrency tests passed!")
        else:
            logger.error("❌ Some tests failed - request_id mechanism needs attention")
    
    async def _cleanup(self):
        """Clean up resources"""
        if self.websocket:
            await self.websocket.close()
            
        if self.session_id:
            async with aiohttp.ClientSession() as session:
                await session.post(f"{TEST_CONFIG['api_base_url']}/stop-stream")
                
        logger.info("🧹 Cleanup completed")


async def main():
    """Run the request_id concurrency test"""
    test = RequestIDConcurrencyTest()
    success = await test.run()
    
    if success:
        logger.success("\n🎉 Request ID Concurrency Test Suite PASSED")
        sys.exit(0)
    else:
        logger.error("\n❌ Request ID Concurrency Test Suite FAILED")
        sys.exit(1)


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(sys.stdout, level="DEBUG", 
              format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")
    
    # Run test
    asyncio.run(main())