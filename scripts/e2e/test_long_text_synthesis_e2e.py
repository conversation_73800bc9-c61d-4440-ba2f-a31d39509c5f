#!/usr/bin/env python3
"""长文本TTS合成E2E测试

验证：
1. 长文本不会导致超时循环
2. 动态超时机制正常工作
3. 熔断器保护机制有效
4. Future生命周期管理正确

Author: Claude Code
Date: 2025-08-10
"""

import asyncio
import time
import sys
import os
from pathlib import Path
from typing import Optional, Tuple
from loguru import logger

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入必要的组件
from src.ai_live_streamer.services.streaming_content_provider import StreamingContentProvider, ContentNotReadyError
from src.ai_live_streamer.services.playlist_manager import PlaylistManager
from src.ai_live_streamer.services.client_state_tracker import ClientStateTracker
from src.ai_live_streamer.services.circuit_breaker import CircuitBreakerOpenError
from src.ai_live_streamer.models.playlist_models import PlaylistItem
from src.ai_live_streamer.core.streaming_config import StreamingConfig
from src.ai_live_streamer.api.state_broadcaster import StateBroadcaster

# 配置日志
logger.remove()
logger.add(sys.stderr, level="INFO", format="{time:HH:mm:ss.SSS} | {level} | {message}")


class MockTTSEngine:
    """模拟TTS引擎"""
    
    def __init__(self, synthesis_delay: float = 0.5):
        self.synthesis_delay = synthesis_delay
        self.synthesis_count = 0
        self.fail_next_n = 0  # 设置下N次合成失败
    
    async def synthesize_streaming(self, text: str):
        """模拟流式合成"""
        self.synthesis_count += 1
        
        # 模拟失败
        if self.fail_next_n > 0:
            self.fail_next_n -= 1
            await asyncio.sleep(10)  # 模拟超时
            raise TimeoutError("Simulated TTS timeout")
        
        # 根据文本长度调整延迟
        delay = min(self.synthesis_delay * (len(text) / 100), 3.0)
        await asyncio.sleep(delay)
        
        # 生成模拟音频数据
        audio_size = len(text) * 100  # 每个字符约100字节
        
        class AudioChunk:
            def __init__(self, data):
                self.audio_stream = type('obj', (object,), {'audio_data': data})()
        
        # 分块返回
        chunk_size = 4096
        for i in range(0, audio_size, chunk_size):
            chunk_data = b'A' * min(chunk_size, audio_size - i)
            yield AudioChunk(chunk_data)


async def test_long_text_no_timeout():
    """测试1：验证长文本不会导致超时循环"""
    logger.info("=" * 60)
    logger.info("测试1：长文本合成（无超时循环）")
    logger.info("=" * 60)
    
    # 准备长文本
    long_text = "朋友们，这个真的不能错过！" * 50  # 构造长文本，约700字符
    
    # 创建播放项
    item = PlaylistItem.create_script_item(long_text, 0)
    estimated_ms = item.estimate_duration_ms()
    logger.info(f"📝 文本长度: {len(long_text)} 字符")
    logger.info(f"⏱️ 预估时长: {estimated_ms}ms")
    
    # 初始化组件
    config = StreamingConfig()
    
    # 创建模拟的 main_content_player
    class MockMainContentPlayer:
        def __init__(self):
            self.current_index = 0
        
        def get_current_index(self):
            return self.current_index
    
    main_content_player = MockMainContentPlayer()
    playlist_manager = PlaylistManager(config, main_content_player)
    client_tracker = ClientStateTracker(config)
    tts_engine = MockTTSEngine(synthesis_delay=0.01)  # 快速合成
    state_broadcaster = StateBroadcaster()
    
    content_provider = StreamingContentProvider(
        playlist_manager=playlist_manager,
        client_tracker=client_tracker,
        tts_engine=tts_engine,
        config=config,
        state_broadcaster=state_broadcaster
    )
    
    # 添加到播放列表
    playlist_manager._playlist = [item]
    
    # 执行合成
    start = time.time()
    try:
        audio_data, duration_ms = await content_provider._get_or_synthesize_audio(item, "test_client")
        duration = time.time() - start
        
        logger.info(f"✅ 合成成功!")
        logger.info(f"   - 音频大小: {len(audio_data)} bytes")
        logger.info(f"   - 合成耗时: {duration:.2f}秒")
        logger.info(f"   - 动态超时设置正确: {duration < 30}")
        
        assert audio_data is not None, "音频数据不应为空"
        assert len(audio_data) > 10000, f"音频太小: {len(audio_data)} bytes"
        assert duration < 30, f"合成时间过长: {duration}秒"
        
        logger.info("✅ 测试1通过: 长文本合成成功，无超时循环")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试1失败: {e}")
        return False


async def test_concurrent_requests():
    """测试2：验证并发请求的Future共享机制"""
    logger.info("=" * 60)
    logger.info("测试2：并发请求Future共享")
    logger.info("=" * 60)
    
    # 初始化组件
    config = StreamingConfig()
    
    # 创建模拟的 main_content_player
    class MockMainContentPlayer:
        def __init__(self):
            self.current_index = 0
        
        def get_current_index(self):
            return self.current_index
    
    main_content_player = MockMainContentPlayer()
    playlist_manager = PlaylistManager(config, main_content_player)
    client_tracker = ClientStateTracker(config)
    tts_engine = MockTTSEngine(synthesis_delay=2.0)  # 慢速合成
    state_broadcaster = StateBroadcaster()
    
    content_provider = StreamingContentProvider(
        playlist_manager=playlist_manager,
        client_tracker=client_tracker,
        tts_engine=tts_engine,
        config=config,
        state_broadcaster=state_broadcaster
    )
    
    # 创建测试项
    text = "这是一个并发测试文本"
    item = PlaylistItem.create_script_item(text, 0)
    playlist_manager._playlist = [item]
    
    # 并发请求同一内容
    async def make_request(client_id: str):
        try:
            start = time.time()
            audio_data, _ = await content_provider._get_or_synthesize_audio(item, client_id)
            duration = time.time() - start
            logger.info(f"客户端 {client_id} 获取成功，耗时: {duration:.2f}秒")
            return True
        except ContentNotReadyError:
            logger.info(f"客户端 {client_id} 收到生成中响应")
            return False
    
    # 启动3个并发请求
    tasks = [
        asyncio.create_task(make_request(f"client_{i}"))
        for i in range(3)
    ]
    
    results = await asyncio.gather(*tasks)
    
    # 验证：至少有一个请求等待了Future（共享机制生效）
    synthesis_metrics = content_provider._synthesis_metrics
    logger.info(f"📊 合成统计:")
    logger.info(f"   - 创建的Future数: {synthesis_metrics['synthesis_tasks_created']}")
    logger.info(f"   - 等待的Future数: {synthesis_metrics['synthesis_tasks_waited']}")
    
    assert synthesis_metrics['synthesis_tasks_created'] == 1, "应该只创建一个合成任务"
    assert synthesis_metrics['synthesis_tasks_waited'] >= 1, "应该有请求等待Future"
    
    logger.info("✅ 测试2通过: 并发请求正确共享Future")
    return True


async def test_circuit_breaker_protection():
    """测试3：验证熔断器保护机制"""
    logger.info("=" * 60)
    logger.info("测试3：熔断器保护机制")
    logger.info("=" * 60)
    
    # 初始化组件
    config = StreamingConfig()
    
    # 创建模拟的 main_content_player
    class MockMainContentPlayer:
        def __init__(self):
            self.current_index = 0
        
        def get_current_index(self):
            return self.current_index
    
    main_content_player = MockMainContentPlayer()
    playlist_manager = PlaylistManager(config, main_content_player)
    client_tracker = ClientStateTracker(config)
    tts_engine = MockTTSEngine(synthesis_delay=0.1)
    state_broadcaster = StateBroadcaster()
    
    content_provider = StreamingContentProvider(
        playlist_manager=playlist_manager,
        client_tracker=client_tracker,
        tts_engine=tts_engine,
        config=config,
        state_broadcaster=state_broadcaster
    )
    
    # 配置TTS引擎连续失败
    tts_engine.fail_next_n = 15  # 让接下来15次合成都失败
    
    # 创建多个测试项
    items = []
    for i in range(20):
        item = PlaylistItem.create_script_item(f"测试文本 {i}", i)
        items.append(item)
    playlist_manager._playlist = items
    
    # 尝试合成多个项目，触发熔断器
    circuit_breaker_triggered = False
    for i, item in enumerate(items):
        try:
            await content_provider._execute_synthesis(item)
        except ContentNotReadyError as e:
            if "SERVICE_UNAVAILABLE" in str(e):
                logger.info(f"⚡ 请求 {i+1} 被熔断器拒绝")
                circuit_breaker_triggered = True
                break
        except Exception as e:
            logger.debug(f"请求 {i+1} 失败: {e}")
    
    # 获取熔断器状态
    cb_stats = content_provider.circuit_breaker.get_stats()
    logger.info(f"📊 熔断器统计:")
    logger.info(f"   - 状态: {cb_stats['state']}")
    logger.info(f"   - 总请求数: {cb_stats['total_requests']}")
    logger.info(f"   - 阻塞请求数: {cb_stats['blocked_requests']}")
    logger.info(f"   - 失败率: {cb_stats['current_failure_rate']:.1%}")
    
    assert circuit_breaker_triggered, "熔断器应该被触发"
    assert cb_stats['state'] == 'OPEN', "熔断器应该处于开启状态"
    
    logger.info("✅ 测试3通过: 熔断器正确保护TTS服务")
    return True


async def test_fast_response_with_background_synthesis():
    """测试4：验证快速响应与后台合成机制"""
    logger.info("=" * 60)
    logger.info("测试4：快速响应与后台合成")
    logger.info("=" * 60)
    
    # 初始化组件
    config = StreamingConfig()
    
    # 创建模拟的 main_content_player
    class MockMainContentPlayer:
        def __init__(self):
            self.current_index = 0
        
        def get_current_index(self):
            return self.current_index
    
    main_content_player = MockMainContentPlayer()
    playlist_manager = PlaylistManager(config, main_content_player)
    client_tracker = ClientStateTracker(config)
    tts_engine = MockTTSEngine(synthesis_delay=8.0)  # 超长合成时间
    state_broadcaster = StateBroadcaster()
    
    content_provider = StreamingContentProvider(
        playlist_manager=playlist_manager,
        client_tracker=client_tracker,
        tts_engine=tts_engine,
        config=config,
        state_broadcaster=state_broadcaster
    )
    
    # 创建测试项
    text = "需要很长时间合成的文本"
    item = PlaylistItem.create_script_item(text, 0)
    playlist_manager._playlist = [item]
    
    # 第一次请求（应该快速返回"生成中"）
    start = time.time()
    try:
        await content_provider._get_or_synthesize_audio(item, "client_1")
        assert False, "第一次请求应该超时"
    except ContentNotReadyError as e:
        response_time = time.time() - start
        logger.info(f"⏱️ 第一次请求快速响应: {response_time:.2f}秒")
        logger.info(f"   - 错误信息: {e}")
        assert response_time < 6, f"响应时间过长: {response_time}秒"
    
    # 等待后台合成完成
    logger.info("等待后台合成完成...")
    await asyncio.sleep(10)
    
    # 第二次请求（应该立即获得结果）
    start = time.time()
    try:
        audio_data, _ = await content_provider._get_or_synthesize_audio(item, "client_2")
        response_time = time.time() - start
        logger.info(f"✅ 第二次请求立即成功: {response_time:.2f}秒")
        assert response_time < 1, f"应该立即返回缓存结果: {response_time}秒"
        assert len(audio_data) > 0, "音频数据不应为空"
    except Exception as e:
        logger.error(f"第二次请求失败: {e}")
        return False
    
    logger.info("✅ 测试4通过: 快速响应机制正常工作")
    return True


async def main():
    """运行所有测试"""
    logger.info("🚀 开始长文本TTS合成E2E测试")
    logger.info("=" * 60)
    
    tests = [
        ("长文本无超时", test_long_text_no_timeout),
        ("并发请求Future共享", test_concurrent_requests),
        ("熔断器保护", test_circuit_breaker_protection),
        ("快速响应与后台合成", test_fast_response_with_background_synthesis),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            logger.info(f"\n运行测试: {test_name}")
            result = await test_func()
            results.append((test_name, result))
            logger.info("")
        except Exception as e:
            logger.error(f"测试异常: {test_name} - {e}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("=" * 60)
    logger.info("📊 测试结果汇总:")
    logger.info("=" * 60)
    
    passed = 0
    failed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
        else:
            failed += 1
    
    logger.info("=" * 60)
    logger.info(f"总计: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        logger.info("🎉 所有测试通过!")
        return 0
    else:
        logger.error(f"⚠️ {failed} 个测试失败")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)