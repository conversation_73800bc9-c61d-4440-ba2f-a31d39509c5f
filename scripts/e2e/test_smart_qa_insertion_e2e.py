#!/usr/bin/env python3
"""
E2E测试: 智能QA插入功能
验证从QA请求到音频播放的完整流程，包括中断协议、缓存复用和状态同步
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.ai_live_streamer.services.playlist_manager import PlaylistManager
from src.ai_live_streamer.api.state_broadcaster import StateBroadcaster
from src.ai_live_streamer.models.playlist_models import QAInsertionRequest
from src.ai_live_streamer.core.streaming_config import StreamingConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockWebSocketConnection:
    """模拟WebSocket连接"""
    
    def __init__(self, client_id: str):
        self.client_id = client_id
        self.sent_messages = []
        self.is_open = True
        
        # 模拟客户端状态
        self.current_playing_index = 31
        self.buffered_indices = [32, 33]
        self.audio_cache_keys = {
            32: "cache_key_32_abc",
            33: "cache_key_33_def"
        }
    
    async def send(self, message: dict):
        """发送消息到客户端"""
        self.sent_messages.append(message)
        logger.info(f"[{self.client_id}] Sent: {message.get('type', 'unknown')}")
        
    
    async def send_json(self, message: dict):
        """兼容StateBroadcaster的send_json方法"""
        await self.send(message)
    

class MockMainContentPlayer:
    """模拟主内容播放器"""
    
    def __init__(self):
        self.current_item_id = None
        self.current_index = 0
    
    async def generate_content(self, text: str, item_id: str) -> dict:
        """模拟内容生成"""
        return {
            'audio_data': b'mock_audio_data',
            'duration_ms': 3000,
            'format': 'wav'
        }

class SmartQAInsertionE2ETest:
    """智能QA插入E2E测试类"""
    
    def __init__(self):
        self.playlist_manager = None
        self.state_broadcaster = None
        self.mock_clients = {}
        self.test_results = []
        self.config = None
        self.mock_main_player = None
    
    async def setup(self):
        """设置测试环境"""
        logger.info("🔧 Setting up Smart QA Insertion E2E test environment...")
        
        # 初始化配置
        self.config = StreamingConfig()
        
        # 初始化模拟主内容播放器
        self.mock_main_player = MockMainContentPlayer()
        
        # 初始化播放列表管理器
        self.playlist_manager = PlaylistManager(
            config=self.config,
            main_content_player=self.mock_main_player
        )
        
        # 初始化状态广播器
        self.state_broadcaster = StateBroadcaster()
        
        # 创建模拟客户端
        client_ids = ["client_001", "client_002", "client_003"]
        for client_id in client_ids:
            mock_conn = MockWebSocketConnection(client_id)
            self.mock_clients[client_id] = mock_conn
            
            # 将模拟客户端注册到广播器
            await self.state_broadcaster.register_connection(client_id, mock_conn)
        
        logger.info(f"✅ Setup complete. Created {len(self.mock_clients)} mock clients")
    
    async def create_initial_playlist(self):
        """创建初始播放列表"""
        logger.info("📋 Creating initial playlist...")
        
        # 创建包含40个项目的播放列表（模拟正在进行的直播）
        class MockSentence:
            def __init__(self, text):
                self.text = text
        
        initial_content = [
            MockSentence(f"这是第{i+1}条直播内容，包含了丰富的信息和有趣的话题。")
            for i in range(40)
        ]
        
        session_id = "test_session_smart_qa"
        
        # 使用initialize_from_script方法初始化播放列表
        success = await self.playlist_manager.initialize_from_script(initial_content)
        
        if not success:
            raise Exception("Failed to initialize playlist")
        
        logger.info(f"✅ Created playlist with {len(initial_content)} items")
        return session_id
    
    async def test_basic_broadcast(self):
        """测试基本广播功能"""
        logger.info("📢 Testing basic broadcast functionality...")
        
        # 测试广播消息
        test_message = {
            "type": "test_broadcast",
            "qa_id": "test_qa_001",
            "timestamp": time.time()
        }
        
        start_time = time.time()
        await self.state_broadcaster.broadcast_to_all(test_message)
        broadcast_time = time.time() - start_time
        
        # 验证所有客户端都收到了消息
        received_count = 0
        for client_id, mock_client in self.mock_clients.items():
            if mock_client.sent_messages:
                received_count += 1
        
        self.test_results.append({
            "test": "basic_broadcast",
            "expected_clients": len(self.mock_clients),
            "actual_clients": received_count,
            "broadcast_time_ms": round(broadcast_time * 1000, 2),
            "passed": received_count == len(self.mock_clients) and broadcast_time < 0.1
        })
        
        logger.info(f"✅ Broadcast completed in {broadcast_time:.3f}s, reached {received_count} clients")
        
        # 模拟客户端报告
        mock_reports = []
        for client_id, mock_client in self.mock_clients.items():
            report = {
                'client_id': client_id,
                'playing_index': mock_client.current_playing_index,
                'buffered_indices': mock_client.buffered_indices,
                'audio_cache_keys': mock_client.audio_cache_keys,
                'client_time': time.time()
            }
            mock_reports.append(report)
        
        return mock_reports
    
    async def test_smart_insertion_logic(self, session_id: str, client_reports: list):
        """测试智能插入逻辑"""
        logger.info("🧠 Testing smart insertion logic...")
        
        # 创建QA插入请求
        qa_request = QAInsertionRequest(
            qa_id="test_qa_smart_001",
            question="这个产品有什么特色功能吗？",
            answer="这个产品有三大特色功能：智能语音识别、实时语言翻译和个性化推荐算法。",
            priority=1  # 高优先级
        )
        
        # 模拟客户端追踪器（简化版）
        class MockClientTracker:
            def get_aggregated_state(self):
                return {
                    'min_playing_index': min(r.get('playing_index', 0) for r in client_reports),
                    'max_buffered_index': max(
                        max(r.get('buffered_indices', [0])) 
                        for r in client_reports
                    ),
                    'active_clients': len(client_reports)
                }
        
        client_tracker = MockClientTracker()
        
        # 执行智能插入
        start_time = time.time()
        result = await self.playlist_manager.handle_qa_event(qa_request, client_tracker)
        insertion_time = time.time() - start_time
        
        self.test_results.append({
            "test": "smart_qa_insertion",
            "insertion_time_ms": round(insertion_time * 1000, 2),
            "success": result.success,
            "insertion_index": result.insertion_index if result.success else None,
            "items_inserted": len(result.items_inserted) if result.items_inserted else 0,
            "passed": result.success and insertion_time < 0.5  # 500ms内完成
        })
        
        logger.info(f"✅ Smart insertion completed in {insertion_time:.3f}s")
        logger.info(f"   QA inserted at index: {result.insertion_index}")
        logger.info(f"   Inserted {len(result.items_inserted)} items")
        
        return result
    
    async def test_cache_reuse_simulation(self, client_reports: list):
        """测试缓存复用模拟"""
        logger.info("♻️ Testing cache reuse simulation...")
        
        total_cached_items = 0
        reusable_items = 0
        
        for report in client_reports:
            cache_keys = report.get('audio_cache_keys', {})
            buffered_indices = report.get('buffered_indices', [])
            
            total_cached_items += len(cache_keys)
            
            # 模拟缓存复用逻辑：假设缓冲中的内容可以被复用
            for idx in buffered_indices:
                if str(idx) in cache_keys:
                    reusable_items += 1
        
        reuse_rate = (reusable_items / total_cached_items * 100) if total_cached_items > 0 else 0
        
        self.test_results.append({
            "test": "cache_reuse_simulation",
            "total_cached_items": total_cached_items,
            "reusable_items": reusable_items,
            "reuse_rate_percent": round(reuse_rate, 1),
            "passed": reuse_rate >= 50.0  # 期望至少50%的缓存复用率
        })
        
        logger.info(f"✅ Cache reuse simulation: {reusable_items}/{total_cached_items} items ({reuse_rate:.1f}%)")
    
    async def test_performance_metrics(self):
        """测试性能指标"""
        logger.info("📊 Testing performance metrics...")
        
        # 测试并发广播处理
        concurrent_broadcasts = 5
        start_time = time.time()
        
        tasks = []
        for i in range(concurrent_broadcasts):
            message = {
                "type": "concurrent_test",
                "broadcast_id": f"broadcast_{i:03d}",
                "timestamp": time.time()
            }
            task = self.state_broadcaster.broadcast_to_all(message)
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        concurrent_time = time.time() - start_time
        
        self.test_results.append({
            "test": "concurrent_broadcasts",
            "concurrent_broadcasts": concurrent_broadcasts,
            "processing_time_ms": round(concurrent_time * 1000, 2),
            "avg_time_per_broadcast_ms": round(concurrent_time * 1000 / concurrent_broadcasts, 2),
            "passed": concurrent_time < 1.0  # 1秒内处理5个并发广播
        })
        
        logger.info(f"✅ Processed {concurrent_broadcasts} concurrent broadcasts in {concurrent_time:.3f}s")
    
    async def run_full_test_suite(self):
        """运行完整测试套件"""
        logger.info("🚀 Starting Smart QA Insertion E2E Test Suite...")
        
        try:
            # 1. 设置测试环境
            await self.setup()
            
            # 2. 创建初始播放列表
            session_id = await self.create_initial_playlist()
            
            # 3. 测试基本广播功能
            client_reports = await self.test_basic_broadcast()
            
            # 4. 测试智能插入逻辑
            insertion_result = await self.test_smart_insertion_logic(session_id, client_reports)
            
            # 5. 测试缓存复用
            await self.test_cache_reuse_simulation(client_reports)
            
            # 6. 测试性能指标
            await self.test_performance_metrics()
            
            # 7. 生成测试报告
            await self.generate_test_report()
            
            return all(result.get('passed', False) for result in self.test_results)
            
        except Exception as e:
            logger.error(f"❌ Test suite failed with exception: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def generate_test_report(self):
        """生成测试报告"""
        logger.info("📋 Generating test report...")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result.get('passed', False))
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print("\n" + "="*80)
        print("🎯 SMART QA INSERTION E2E TEST REPORT")
        print("="*80)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        print("-"*80)
        
        for i, result in enumerate(self.test_results, 1):
            status = "✅ PASS" if result.get('passed', False) else "❌ FAIL"
            test_name = result.get('test', 'unknown')
            print(f"{i:2d}. {status} {test_name}")
            
            # 显示关键指标
            for key, value in result.items():
                if key not in ['test', 'passed'] and isinstance(value, (int, float, str)):
                    print(f"    {key}: {value}")
        
        print("="*80)
        
        if success_rate == 100.0:
            print("🎉 ALL TESTS PASSED! Smart QA insertion is working correctly.")
        elif success_rate >= 80.0:
            print("⚠️ Most tests passed, but some issues need attention.")
        else:
            print("❌ Multiple test failures detected. System needs debugging.")
        
        print("="*80)

async def main():
    """主函数"""
    test_suite = SmartQAInsertionE2ETest()
    
    try:
        success = await test_suite.run_full_test_suite()
        exit_code = 0 if success else 1
        
        logger.info(f"🏁 Test suite completed with exit code: {exit_code}")
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # 设置事件循环策略（兼容不同操作系统）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())