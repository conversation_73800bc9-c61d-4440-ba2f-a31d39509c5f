#!/usr/bin/env python3
"""
Comprehensive E2E Test Suite Runner

Runs all E2E tests in sequence, providing comprehensive validation
of the AI Live Streamer system functionality.

Test Coverage:
1. Core Playback Functionality (basic audio streaming)
2. QA Broadcast Integration (QA submission → broadcast → playback)
3. Version Control & Message Handling
4. Request ID Concurrency Control (race condition prevention)
"""

import asyncio
import sys
from pathlib import Path
from loguru import logger
from typing import List, Dict, Any

# Import test classes
sys.path.insert(0, str(Path(__file__).parent))
from test_core_playback_e2e import CorePlaybackE2ETest, TEST_CONFIG
from test_qa_broadcast_e2e import QABroadcastE2ETest
from test_request_id_concurrency_e2e import RequestIDConcurrencyTest


class ComprehensiveE2ETestSuite:
    """Complete E2E test suite runner"""
    
    def __init__(self, fast_mode: bool = False):
        self.results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.fast_mode = fast_mode
        
    async def run_all_tests(self) -> bool:
        """Run all E2E tests and return overall success status"""
        logger.info("🚀 Starting Comprehensive E2E Test Suite")
        if self.fast_mode:
            logger.info("⚡ Fast mode enabled - running core tests only")
        logger.info("=" * 70)
        
        # Adjust test sequence based on mode
        if self.fast_mode:
            # Fast mode: only run core tests
            test_sequence = [
                ("Core Playback", CorePlaybackE2ETest),
                ("QA Broadcast Integration", QABroadcastE2ETest),
                # Skip Request ID Concurrency Control test in fast mode
            ]
        else:
            # Full mode: run all tests
            test_sequence = [
                ("Core Playback", CorePlaybackE2ETest),
                ("QA Broadcast Integration", QABroadcastE2ETest),
                ("Request ID Concurrency Control", RequestIDConcurrencyTest),
                # Note: Safe Distance QA Insertion test is standalone, not included in suite
                # Run separately with: python scripts/e2e/test_safe_distance_qa_insertion_e2e.py
            ]
        
        overall_success = True
        
        for test_name, test_class in test_sequence:
            logger.info(f"\n{'='*20} {test_name} Test {'='*20}")
            
            try:
                test_instance = test_class()
                result = await test_instance.run()
                
                self.results[test_name] = result
                self.total_tests += 1
                
                if result.passed:
                    self.passed_tests += 1
                    logger.success(f"✅ {test_name} Test: PASSED")
                else:
                    overall_success = False
                    logger.error(f"❌ {test_name} Test: FAILED")
                    if result.error_message:
                        logger.error(f"   Error: {result.error_message}")
                        
            except Exception as e:
                overall_success = False
                logger.error(f"❌ {test_name} Test: EXCEPTION - {e}")
                self.results[test_name] = None
                self.total_tests += 1
        
        self._print_comprehensive_summary()
        return overall_success
    
    def _print_comprehensive_summary(self):
        """Print comprehensive test suite summary"""
        logger.info("\n" + "=" * 70)
        logger.info("📊 COMPREHENSIVE E2E TEST SUITE SUMMARY")
        logger.info("=" * 70)
        
        overall_status = "✅ PASSED" if self.passed_tests == self.total_tests else "❌ FAILED"
        logger.info(f"Overall Status: {overall_status}")
        logger.info(f"Test Results: {self.passed_tests}/{self.total_tests} passed")
        
        logger.info("\n📋 Individual Test Results:")
        for test_name, result in self.results.items():
            if result is None:
                logger.info(f"  ❌ {test_name}: EXCEPTION")
            elif result.passed:
                logger.info(f"  ✅ {test_name}: PASSED ({len(result.steps_completed)} steps)")
            else:
                logger.info(f"  ❌ {test_name}: FAILED ({len(result.steps_completed)} steps)")
        
        # Detailed functionality coverage
        logger.info("\n🎯 Functionality Coverage Analysis:")
        
        core_features = [
            "Stream Start/Stop", "WebSocket Connection", "Content Request/Response", 
            "Audio Format Validation", "Multiple Segment Playback"
        ]
        
        qa_features = [
            "QA Submission", "Item Inserted Broadcast", "Playlist Version Control",
            "QA Audio Playback", "WebSocket Message Handling"
        ]
        
        concurrency_features = [
            "Request ID Matching", "Rapid Request Handling", "Out-of-Order Responses",
            "Race Condition Prevention", "Error Response Tracking"
        ]
        
        # Analyze core playback coverage
        core_result = self.results.get("Core Playback")
        if core_result:
            core_coverage = self._analyze_coverage(core_result.steps_completed, 
                                                 ["start_stream", "websocket_connect", "content_request", 
                                                  "audio_validation", "playback_test"])
            logger.info(f"  Core Playback Coverage: {core_coverage}%")
        
        # Analyze QA broadcast coverage  
        qa_result = self.results.get("QA Broadcast Integration")
        if qa_result:
            qa_coverage = self._analyze_coverage(qa_result.steps_completed,
                                               ["qa_submission", "version_control", "qa_audio_playback", 
                                                "websocket_handling"])
            logger.info(f"  QA Broadcast Coverage: {qa_coverage}%")
        
        # Analyze request ID concurrency coverage
        concurrency_result = self.results.get("Request ID Concurrency Control")
        if concurrency_result:
            # For simple test classes that return bool, assume 100% if passed
            concurrency_coverage = 100 if concurrency_result else 0
            logger.info(f"  Request ID Concurrency Coverage: {concurrency_coverage}%")
        
        # System health indicators
        logger.info("\n🏥 System Health Indicators:")
        
        if core_result and core_result.websocket_connected:
            logger.info("  ✅ WebSocket Infrastructure: Healthy")
        else:
            logger.info("  ❌ WebSocket Infrastructure: Issues Detected")
            
        if core_result and core_result.audio_data_received and core_result.audio_format_valid:
            logger.info("  ✅ Audio Pipeline: Healthy")
        else:
            logger.info("  ❌ Audio Pipeline: Issues Detected")
            
        if qa_result and "qa_submission" in qa_result.steps_completed:
            logger.info("  ✅ QA Integration: Healthy")
        else:
            logger.info("  ❌ QA Integration: Issues Detected")
        
        # Performance insights
        logger.info("\n⚡ Performance Insights:")
        logger.info(f"  Test Configuration: {TEST_CONFIG['timeout_seconds']}s timeout")
        logger.info(f"  API Endpoint: {TEST_CONFIG['api_base_url']}")
        logger.info(f"  WebSocket Endpoint: {TEST_CONFIG['ws_base_url']}")
        
        # Recommendations based on results
        self._print_recommendations()
    
    def _analyze_coverage(self, completed_steps: List[str], expected_steps: List[str]) -> int:
        """Calculate test coverage percentage"""
        if not expected_steps:
            return 100
        
        completed_count = sum(1 for step in expected_steps if step in completed_steps)
        return int((completed_count / len(expected_steps)) * 100)
    
    def _print_recommendations(self):
        """Print recommendations based on test results"""
        logger.info("\n💡 Recommendations:")
        
        failed_tests = [name for name, result in self.results.items() 
                       if result is None or not result.passed]
        
        if not failed_tests:
            logger.info("  🎉 All tests passed! System is ready for production.")
            logger.info("  🔄 Consider running tests regularly in CI/CD pipeline.")
            logger.info("  📈 Monitor performance metrics in production environment.")
        else:
            logger.info(f"  ⚠️ {len(failed_tests)} test(s) failed. Address issues before deployment:")
            
            for test_name in failed_tests:
                result = self.results.get(test_name)
                if result and result.error_message:
                    logger.info(f"     - {test_name}: {result.error_message}")
                else:
                    logger.info(f"     - {test_name}: Check logs for details")
            
            logger.info("  🔧 Debug steps:")
            logger.info("     1. Check server logs for error details")
            logger.info("     2. Verify all dependencies are running")
            logger.info("     3. Validate configuration parameters")
            logger.info("     4. Test individual components separately")


async def main():
    """Main test suite runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Comprehensive E2E Test Suite")
    parser.add_argument("--api-url", default=TEST_CONFIG["api_base_url"],
                       help=f"API base URL (default: {TEST_CONFIG['api_base_url']})")
    parser.add_argument("--ws-url", default=TEST_CONFIG["ws_base_url"],
                       help=f"WebSocket base URL (default: {TEST_CONFIG['ws_base_url']})")
    parser.add_argument("--timeout", type=int, default=TEST_CONFIG["timeout_seconds"],
                       help=f"Timeout in seconds (default: {TEST_CONFIG['timeout_seconds']})")
    parser.add_argument("--verbose", action="store_true",
                       help="Enable verbose logging")
    parser.add_argument("--fast", action="store_true",
                       help="Run tests with shorter timeouts for faster feedback")
    
    args = parser.parse_args()
    
    # Update global test configuration
    if args.api_url:
        TEST_CONFIG["api_base_url"] = args.api_url
    if args.ws_url:
        TEST_CONFIG["ws_base_url"] = args.ws_url
    if args.timeout:
        TEST_CONFIG["timeout_seconds"] = args.timeout
    
    # Fast mode adjustments
    if args.fast:
        TEST_CONFIG["timeout_seconds"] = min(TEST_CONFIG["timeout_seconds"], 5)
        logger.info("🚀 Fast mode enabled - reduced timeouts")
    
    logger.info(f"📝 Test Suite Configuration:")
    logger.info(f"  API URL: {TEST_CONFIG['api_base_url']}")
    logger.info(f"  WebSocket URL: {TEST_CONFIG['ws_base_url']}")
    logger.info(f"  Timeout: {TEST_CONFIG['timeout_seconds']}s")
    logger.info(f"  Verbose Mode: {args.verbose}")
    
    # Run comprehensive test suite
    suite = ComprehensiveE2ETestSuite(fast_mode=args.fast)
    success = await suite.run_all_tests()
    
    # Exit with appropriate code
    exit_code = 0 if success else 1
    logger.info(f"\n🏁 Test Suite Complete - Exit Code: {exit_code}")
    sys.exit(exit_code)


if __name__ == "__main__":
    # Configure logging based on verbosity
    logger.remove()
    logger.add(sys.stdout, level="DEBUG", 
              format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")
    
    # Add comprehensive test log file
    log_file = Path(__file__).parent / "comprehensive_e2e_results.log"
    logger.add(log_file, level="DEBUG", rotation="10 MB")
    
    # Run comprehensive test suite
    asyncio.run(main())