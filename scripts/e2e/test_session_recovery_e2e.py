#!/usr/bin/env python3
"""
End-to-End Test for Session Recovery After Server Restart

This test validates the critical regression fix for WebSocket v2 client reconnection
after server restarts. It ensures clients with stale session_ids are gracefully
handled rather than hard-rejected with 403/4001 errors.

Test Scenario:
1. Start server and create a session
2. Simulate client connection with valid session
3. Simulate server restart (session state lost)
4. Test client reconnection with stale session_id
5. Verify graceful recovery (auto-creation) instead of rejection

IMPORTANT: This test prevents regression of the stale session handling bug.
"""

import asyncio
import json
import os
import sys
import time
import traceback
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, List

import aiohttp
import websockets
from loguru import logger

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Test configuration
TEST_CONFIG = {
    "api_base_url": os.getenv("E2E_API_BASE_URL", "http://localhost:8000"),
    "ws_base_url": os.getenv("E2E_WS_BASE_URL", "ws://localhost:8000"),
    "timeout_seconds": int(os.getenv("E2E_TIMEOUT_SECONDS", "30")),
    "request_timeout_ms": int(os.getenv("E2E_REQUEST_TIMEOUT_MS", "5000")),
}

# Test content for session creation
TEST_CONTENT = [
    "欢迎来到直播间，今天我们要测试会话恢复功能。",
    "这是第二句测试内容，用于验证播放列表初始化。",
    "第三句用于确认系统能够正常处理多个内容项目。"
]


class SessionRecoveryE2ETest:
    """Session Recovery End-to-End Test Suite"""
    
    def __init__(self):
        self.api_base = TEST_CONFIG["api_base_url"]
        self.ws_base = TEST_CONFIG["ws_base_url"]
        self.timeout = TEST_CONFIG["timeout_seconds"]
        
        # Test state
        self.test_session_id: Optional[str] = None
        self.test_client_id: Optional[str] = None
        self.results: Dict[str, Any] = {}
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run complete session recovery test suite"""
        logger.info("🚀 Starting Session Recovery E2E Test Suite")
        start_time = time.time()
        
        try:
            # Test 1: Normal session creation and connection
            await self.test_normal_session_creation()
            
            # Test 2: Valid session connection
            await self.test_valid_session_connection()
            
            # Test 3: Stale session recovery (main test)
            await self.test_stale_session_recovery()
            
            # Test 4: Multiple stale connections (edge case)
            await self.test_multiple_stale_connections()
            
            # Test 5: Recovery with empty playlist
            await self.test_recovery_with_empty_playlist()
            
            # Calculate overall results
            total_time = time.time() - start_time
            passed_tests = sum(1 for result in self.results.values() if result.get("passed", False))
            total_tests = len(self.results)
            
            overall_result = {
                "overall_passed": passed_tests == total_tests,
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "total_time_seconds": round(total_time, 2),
                "detailed_results": self.results
            }
            
            if overall_result["overall_passed"]:
                logger.info(f"✅ All session recovery tests passed ({passed_tests}/{total_tests}) in {total_time:.2f}s")
            else:
                logger.error(f"❌ Session recovery tests failed ({passed_tests}/{total_tests}) in {total_time:.2f}s")
                
            return overall_result
            
        except Exception as e:
            logger.error(f"💥 Session recovery test suite crashed: {e}")
            logger.error(f"Traceback:\n{traceback.format_exc()}")
            return {
                "overall_passed": False,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    async def test_normal_session_creation(self) -> None:
        """Test 1: Normal session creation should work correctly"""
        logger.info("🧪 Test 1: Normal session creation")
        test_name = "normal_session_creation"
        start_time = time.time()
        
        try:
            # Create a new session
            session_response = await self.create_test_session()
            
            if not session_response or session_response.get("status") != "started":
                raise Exception(f"Session creation failed: {session_response}")
                
            self.test_session_id = session_response["session_id"]
            logger.info(f"✅ Session created successfully: {self.test_session_id}")
            
            # Verify session exists in active sessions
            status_response = await self.get_session_status(self.test_session_id)
            if not status_response or status_response.get("status") not in ["active", "stopped"]:
                raise Exception(f"Session status check failed: {status_response}")
            
            self.results[test_name] = {
                "passed": True,
                "session_id": self.test_session_id,
                "duration_seconds": round(time.time() - start_time, 2)
            }
            logger.info(f"✅ Test 1 passed: Normal session creation works")
            
        except Exception as e:
            self.results[test_name] = {
                "passed": False,
                "error": str(e),
                "duration_seconds": round(time.time() - start_time, 2)
            }
            logger.error(f"❌ Test 1 failed: {e}")
    
    async def test_valid_session_connection(self) -> None:
        """Test 2: Connection with valid session should work normally"""
        logger.info("🧪 Test 2: Valid session WebSocket connection")
        test_name = "valid_session_connection"
        start_time = time.time()
        
        try:
            if not self.test_session_id:
                raise Exception("No test session available from previous test")
            
            self.test_client_id = f"test_client_{uuid.uuid4().hex[:8]}"
            
            # Connect to WebSocket with valid session
            ws_url = f"{self.ws_base}/ws/v2/stream"
            params = {
                "client_id": self.test_client_id,
                "session_id": self.test_session_id,
                "protocol_version": "2.0"
            }
            
            connection_result = await self.test_websocket_connection(ws_url, params, expected_recovery_method="normal")
            
            if not connection_result["connected"]:
                raise Exception(f"Valid session connection failed: {connection_result}")
            
            # Verify recovery info indicates no recovery was needed
            recovery_info = connection_result.get("session_recovery", {})
            if recovery_info.get("recovered") is not False:
                raise Exception(f"Expected no recovery for valid session, got: {recovery_info}")
            
            self.results[test_name] = {
                "passed": True,
                "connection_result": connection_result,
                "duration_seconds": round(time.time() - start_time, 2)
            }
            logger.info("✅ Test 2 passed: Valid session connection works normally")
            
        except Exception as e:
            self.results[test_name] = {
                "passed": False,
                "error": str(e),
                "duration_seconds": round(time.time() - start_time, 2)
            }
            logger.error(f"❌ Test 2 failed: {e}")
    
    async def test_stale_session_recovery(self) -> None:
        """Test 3: MAIN TEST - Stale session should be gracefully recovered"""
        logger.info("🧪 Test 3: MAIN TEST - Stale session recovery")
        test_name = "stale_session_recovery"
        start_time = time.time()
        
        try:
            if not self.test_session_id:
                raise Exception("No test session available from previous tests")
            
            # Simulate server restart by clearing active sessions
            logger.info("🔄 Simulating server restart by clearing active sessions...")
            clear_result = await self.clear_active_sessions()
            
            if not clear_result:
                logger.warning("⚠️ Could not clear active sessions via API, continuing with test")
            
            # Wait a moment for any cleanup
            await asyncio.sleep(1)
            
            # Try to connect with the now-stale session_id
            stale_client_id = f"stale_client_{uuid.uuid4().hex[:8]}"
            ws_url = f"{self.ws_base}/ws/v2/stream"
            params = {
                "client_id": stale_client_id,
                "session_id": self.test_session_id,  # This is now stale
                "protocol_version": "2.0"
            }
            
            logger.info(f"🔌 Attempting connection with stale session: {self.test_session_id}")
            connection_result = await self.test_websocket_connection(
                ws_url, params, 
                expected_recovery_method="auto_created",
                test_timeout=15  # Longer timeout for recovery
            )
            
            if not connection_result["connected"]:
                raise Exception(f"Stale session recovery failed - connection rejected: {connection_result}")
            
            # Verify recovery info indicates auto-creation
            recovery_info = connection_result.get("session_recovery", {})
            if recovery_info.get("recovered") is not True:
                raise Exception(f"Expected recovery=True for stale session, got: {recovery_info}")
            
            recovery_method = recovery_info.get("recovery_method")
            if recovery_method != "auto_created":
                raise Exception(f"Expected recovery_method=auto_created, got: {recovery_method}")
            
            # Verify recovery message is user-friendly
            recovery_message = recovery_info.get("message", "")
            if not recovery_message or "server was restarted" not in recovery_message.lower():
                logger.warning(f"Recovery message could be more user-friendly: {recovery_message}")
            
            self.results[test_name] = {
                "passed": True,
                "connection_result": connection_result,
                "recovery_info": recovery_info,
                "duration_seconds": round(time.time() - start_time, 2)
            }
            logger.info("✅ Test 3 passed: MAIN TEST - Stale session gracefully recovered")
            
        except Exception as e:
            self.results[test_name] = {
                "passed": False,
                "error": str(e),
                "duration_seconds": round(time.time() - start_time, 2)
            }
            logger.error(f"❌ Test 3 FAILED: CRITICAL - Stale session recovery broken: {e}")
    
    async def test_multiple_stale_connections(self) -> None:
        """Test 4: Multiple clients with same stale session should all recover"""
        logger.info("🧪 Test 4: Multiple stale connections")
        test_name = "multiple_stale_connections"
        start_time = time.time()
        
        try:
            if not self.test_session_id:
                raise Exception("No test session available")
            
            # Create multiple concurrent connections with the same stale session
            tasks = []
            num_clients = 3
            
            for i in range(num_clients):
                client_id = f"multi_stale_{i}_{uuid.uuid4().hex[:6]}"
                ws_url = f"{self.ws_base}/ws/v2/stream"
                params = {
                    "client_id": client_id,
                    "session_id": self.test_session_id,
                    "protocol_version": "2.0"
                }
                
                task = self.test_websocket_connection(
                    ws_url, params, 
                    expected_recovery_method="auto_created",
                    test_timeout=10
                )
                tasks.append(task)
            
            # Run all connections concurrently
            logger.info(f"🔗 Testing {num_clients} concurrent stale session connections...")
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Analyze results
            successful_connections = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Client {i} connection failed with exception: {result}")
                    continue
                    
                if result.get("connected", False):
                    successful_connections += 1
                    recovery_info = result.get("session_recovery", {})
                    if recovery_info.get("recovery_method") != "auto_created":
                        logger.warning(f"Client {i} unexpected recovery method: {recovery_info}")
                else:
                    logger.error(f"Client {i} failed to connect: {result}")
            
            if successful_connections == num_clients:
                self.results[test_name] = {
                    "passed": True,
                    "successful_connections": successful_connections,
                    "total_attempted": num_clients,
                    "duration_seconds": round(time.time() - start_time, 2)
                }
                logger.info(f"✅ Test 4 passed: All {num_clients} stale connections recovered")
            else:
                raise Exception(f"Only {successful_connections}/{num_clients} connections succeeded")
                
        except Exception as e:
            self.results[test_name] = {
                "passed": False,
                "error": str(e),
                "duration_seconds": round(time.time() - start_time, 2)
            }
            logger.error(f"❌ Test 4 failed: {e}")
    
    async def test_recovery_with_empty_playlist(self) -> None:
        """Test 5: Recovery should work even when auto-created session has empty playlist"""
        logger.info("🧪 Test 5: Recovery with empty playlist")
        test_name = "recovery_empty_playlist"
        start_time = time.time()
        
        try:
            # Use a completely new, never-existed session ID
            fake_session_id = f"fake_session_{uuid.uuid4().hex[:12]}"
            fake_client_id = f"fake_client_{uuid.uuid4().hex[:8]}"
            
            ws_url = f"{self.ws_base}/ws/v2/stream"
            params = {
                "client_id": fake_client_id,
                "session_id": fake_session_id,
                "protocol_version": "2.0"
            }
            
            logger.info(f"🔌 Testing connection with non-existent session: {fake_session_id}")
            connection_result = await self.test_websocket_connection(
                ws_url, params,
                expected_recovery_method="auto_created",
                test_timeout=10
            )
            
            if not connection_result["connected"]:
                raise Exception(f"Connection with fake session failed: {connection_result}")
            
            # Verify recovery happened
            recovery_info = connection_result.get("session_recovery", {})
            if recovery_info.get("recovery_method") != "auto_created":
                raise Exception(f"Expected auto_created recovery for fake session, got: {recovery_info}")
            
            # Check playlist info - should be initialized (even if empty)
            playlist_info = connection_result.get("playlist_info", {})
            if "total_items" not in playlist_info:
                raise Exception("Playlist info missing from connection response")
            
            self.results[test_name] = {
                "passed": True,
                "connection_result": connection_result,
                "playlist_info": playlist_info,
                "duration_seconds": round(time.time() - start_time, 2)
            }
            logger.info("✅ Test 5 passed: Recovery works with empty playlist")
            
        except Exception as e:
            self.results[test_name] = {
                "passed": False,
                "error": str(e),
                "duration_seconds": round(time.time() - start_time, 2)
            }
            logger.error(f"❌ Test 5 failed: {e}")
    
    async def create_test_session(self) -> Dict[str, Any]:
        """Create a test session with static content"""
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            url = f"{self.api_base}/api/control/start-stream"
            payload = {
                "mode": "static",
                "content": TEST_CONTENT,
                "user_id": "session_recovery_e2e_test"
            }
            
            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Session creation failed: {response.status} - {error_text}")
    
    async def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """Get session status"""
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            url = f"{self.api_base}/api/control/sessions/{session_id}/status"
            
            async with session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Session status check failed: {response.status} - {error_text}")
    
    async def clear_active_sessions(self) -> bool:
        """Attempt to clear active sessions (simulate server restart)"""
        try:
            # Try to stop the current session to simulate clearing
            if self.test_session_id:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                    url = f"{self.api_base}/api/control/sessions/{self.test_session_id}/stop"
                    payload = {"reason": "E2E test session cleanup", "user_id": "e2e_test"}
                    
                    async with session.post(url, json=payload) as response:
                        if response.status == 200:
                            logger.info(f"✅ Successfully stopped session {self.test_session_id}")
                            return True
            return False
        except Exception as e:
            logger.warning(f"⚠️ Could not clear sessions: {e}")
            return False
    
    async def test_websocket_connection(self, ws_url: str, params: Dict[str, str], 
                                      expected_recovery_method: str = "normal",
                                      test_timeout: int = 10) -> Dict[str, Any]:
        """Test WebSocket connection and validate recovery behavior"""
        
        # Build WebSocket URL with parameters
        param_string = "&".join(f"{k}={v}" for k, v in params.items())
        full_ws_url = f"{ws_url}?{param_string}"
        
        connection_result = {
            "connected": False,
            "ws_url": full_ws_url,
            "params": params
        }
        
        try:
            # Connect to WebSocket
            logger.info(f"🔌 Connecting to: {full_ws_url}")
            
            async with websockets.connect(
                full_ws_url,
                timeout=test_timeout,
                ping_interval=None,
                ping_timeout=None
            ) as websocket:
                
                logger.info(f"✅ WebSocket connection established for {params['client_id']}")
                
                # Wait for connection_established message
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response = json.loads(message)
                    
                    if response.get("type") == "connection_established":
                        connection_result.update({
                            "connected": True,
                            "connection_response": response
                        })
                        
                        # Extract and validate session recovery info
                        session_recovery = response.get("session_recovery", {})
                        connection_result["session_recovery"] = session_recovery
                        
                        recovery_method = session_recovery.get("recovery_method", "unknown")
                        if recovery_method == expected_recovery_method:
                            logger.info(f"✅ Recovery method matches expected: {recovery_method}")
                        else:
                            logger.warning(f"⚠️ Recovery method mismatch - expected: {expected_recovery_method}, got: {recovery_method}")
                        
                        # Extract playlist info
                        playlist_info = response.get("playlist_info", {})
                        connection_result["playlist_info"] = playlist_info
                        
                        logger.info(f"📊 Connection successful with recovery: {recovery_method}")
                        
                    else:
                        logger.error(f"❌ Unexpected first message type: {response.get('type')}")
                        connection_result["error"] = f"Unexpected message type: {response.get('type')}"
                        
                except asyncio.TimeoutError:
                    logger.error("⏰ Timeout waiting for connection_established message")
                    connection_result["error"] = "Timeout waiting for connection_established"
                except json.JSONDecodeError as e:
                    logger.error(f"❌ JSON decode error: {e}")
                    connection_result["error"] = f"JSON decode error: {e}"
                
        except websockets.exceptions.WebSocketException as e:
            logger.error(f"❌ WebSocket connection failed: {e}")
            connection_result["error"] = f"WebSocket connection failed: {e}"
        except Exception as e:
            logger.error(f"❌ Unexpected connection error: {e}")
            connection_result["error"] = f"Unexpected error: {e}"
        
        return connection_result


async def main():
    """Main test execution"""
    
    # Configure logging for test visibility
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    logger.info("🚀 Starting Session Recovery E2E Test")
    logger.info(f"📡 API Base URL: {TEST_CONFIG['api_base_url']}")
    logger.info(f"🔌 WebSocket Base URL: {TEST_CONFIG['ws_base_url']}")
    logger.info(f"⏰ Timeout: {TEST_CONFIG['timeout_seconds']}s")
    
    # Run the test suite
    test_runner = SessionRecoveryE2ETest()
    results = await test_runner.run_all_tests()
    
    # Print final results
    print("\n" + "="*80)
    if results["overall_passed"]:
        print("✅ SESSION RECOVERY E2E TEST SUITE PASSED")
        print(f"📊 Results: {results['passed_tests']}/{results['total_tests']} tests passed")
        print(f"⏱️  Total time: {results['total_time_seconds']}s")
        return 0
    else:
        print("❌ SESSION RECOVERY E2E TEST SUITE FAILED") 
        print(f"📊 Results: {results['passed_tests']}/{results['total_tests']} tests passed")
        if 'total_time_seconds' in results:
            print(f"⏱️  Total time: {results['total_time_seconds']}s")
            
        # Print failure details
        print("\n🔍 FAILURE DETAILS:")
        for test_name, test_result in results.get("detailed_results", {}).items():
            if not test_result.get("passed", True):
                print(f"  💥 {test_name}: {test_result.get('error', 'Unknown error')}")
        
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)