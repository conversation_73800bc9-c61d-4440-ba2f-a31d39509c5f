#!/usr/bin/env python3
"""
E2E测试: 安全距离QA插入功能
验证新的简化插入策略：使用配置的偏移量确保内容安全，避免复杂的客户端状态同步
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.ai_live_streamer.services.playlist_manager import PlaylistManager
from src.ai_live_streamer.models.playlist_models import QAInsertionRequest
from src.ai_live_streamer.core.streaming_config import StreamingConfig
from src.ai_live_streamer.core.config import cfg

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockMainContentPlayer:
    """模拟主内容播放器"""
    
    def __init__(self):
        self.current_item_id = None
        self.current_index = 0
    
    async def generate_content(self, text: str, item_id: str) -> dict:
        """模拟内容生成"""
        return {
            'audio_data': b'mock_audio_data',
            'duration_ms': 3000,
            'format': 'wav'
        }

class SafeDistanceQAInsertionE2ETest:
    """安全距离QA插入E2E测试类"""
    
    def __init__(self):
        self.playlist_manager = None
        self.test_results = []
        self.config = None
        self.mock_main_player = None
        self.original_offset = None
    
    async def setup(self):
        """设置测试环境"""
        logger.info("🔧 Setting up Safe Distance QA Insertion E2E test environment...")
        
        # 备份原始配置偏移量
        self.original_offset = cfg.qa_insertion_offset
        
        # 初始化配置
        self.config = StreamingConfig()
        
        # 初始化模拟主内容播放器
        self.mock_main_player = MockMainContentPlayer()
        
        # 初始化播放列表管理器
        self.playlist_manager = PlaylistManager(
            config=self.config,
            main_content_player=self.mock_main_player
        )
        
        logger.info(f"✅ Setup complete. Current QA insertion offset: {cfg.qa_insertion_offset}")
    
    async def teardown(self):
        """清理测试环境"""
        logger.info("🧹 Tearing down test environment...")
        # 恢复原始偏移量配置（如果有必要的话）
        # 注意：在当前实现中，cfg.qa_insertion_offset是从环境变量读取的，不能直接修改
    
    async def create_initial_playlist(self):
        """创建初始播放列表"""
        logger.info("📋 Creating initial playlist...")
        
        # 创建包含40个项目的播放列表（模拟正在进行的直播）
        class MockSentence:
            def __init__(self, text):
                self.text = text
        
        initial_content = [
            MockSentence(f"这是第{i+1}条直播内容，包含了丰富的信息和有趣的话题。")
            for i in range(40)
        ]
        
        session_id = "test_session_safe_distance"
        
        # 初始化播放列表（模拟直播开始）
        await self.playlist_manager.initialize_from_script(initial_content)
        
        # 模拟播放进展到第31个项目
        self.playlist_manager._current_playback_index = 31
        logger.info(f"📍 Set current playback index to: {self.playlist_manager._current_playback_index}")
        
        return session_id
    
    async def test_safe_distance_offset_2(self):
        """测试偏移量为2的安全距离插入（默认稳定模式）"""
        logger.info("🧪 Testing safe distance insertion with offset=2...")
        
        start_time = time.time()
        
        # 确认当前配置
        current_offset = cfg.qa_insertion_offset
        current_playing_index = self.playlist_manager._current_playback_index
        original_playlist_length = len(self.playlist_manager._playlist)
        
        logger.info(f"Test parameters: offset={current_offset}, current_index={current_playing_index}, playlist_length={original_playlist_length}")
        
        # 创建QA请求
        qa_request = QAInsertionRequest(
            qa_id="test_qa_offset_2",
            question="用户询问当前话题的详细信息",
            answer="这是一个详细的回答，解释了相关概念和背景信息。",
            priority="normal"
        )
        
        # 执行插入
        result = await self.playlist_manager.handle_qa_event(qa_request, None)
        
        # 验证结果
        test_result = {
            'test_name': 'safe_distance_offset_2',
            'passed': False,
            'duration_ms': (time.time() - start_time) * 1000,
            'details': {}
        }
        
        try:
            # 验证插入成功
            assert result.success, f"QA insertion should succeed, but got: {result.error_message}"
            
            # 验证插入位置
            expected_position = current_playing_index + current_offset
            assert result.insertion_index == expected_position, f"Expected position {expected_position}, got {result.insertion_index}"
            
            # 验证播放列表长度增加
            new_playlist_length = len(self.playlist_manager._playlist)
            expected_new_length = original_playlist_length + len(result.items_inserted)
            assert new_playlist_length == expected_new_length, f"Expected playlist length {expected_new_length}, got {new_playlist_length}"
            
            # 验证内容完整性：插入点之前的内容应该保持不变
            for i in range(expected_position):
                # 原始内容应该在正确位置
                assert i < new_playlist_length, f"Original content at index {i} should still exist"
            
            # 验证插入的QA内容
            qa_items_count = len(result.items_inserted)
            for i in range(qa_items_count):
                qa_item = self.playlist_manager._playlist[expected_position + i]
                assert qa_item.content is not None, f"QA item at position {expected_position + i} should have content"
            
            # 验证后续内容正确后移
            # (这个验证比较复杂，暂时跳过详细检查，只验证长度)
            
            test_result['passed'] = True
            test_result['details'] = {
                'insertion_position': result.insertion_index,
                'qa_items_inserted': qa_items_count,
                'playlist_length_before': original_playlist_length,
                'playlist_length_after': new_playlist_length,
                'offset_used': current_offset
            }
            
            logger.info(f"✅ Safe distance offset=2 test PASSED: {test_result['details']}")
            
        except AssertionError as e:
            test_result['passed'] = False
            test_result['error'] = str(e)
            logger.error(f"❌ Safe distance offset=2 test FAILED: {e}")
        
        self.test_results.append(test_result)
        return test_result
    
    async def test_insertion_conflict_analysis(self):
        """测试插入冲突分析功能"""
        logger.info("🧪 Testing insertion conflict analysis...")
        
        start_time = time.time()
        
        # 测试多个场景的冲突分析
        current_index = self.playlist_manager._current_playback_index
        test_cases = [
            {'insert_index': current_index + 1, 'expected_conflict': True},   # 激进位置，预期冲突
            {'insert_index': current_index + 2, 'expected_conflict': False},  # 安全位置，预期无冲突
            {'insert_index': current_index + 3, 'expected_conflict': False},  # 非常安全，预期无冲突
        ]
        
        test_result = {
            'test_name': 'insertion_conflict_analysis',
            'passed': False,
            'duration_ms': (time.time() - start_time) * 1000,
            'details': {'cases': []}
        }
        
        all_cases_passed = True
        
        for case in test_cases:
            insert_index = case['insert_index']
            expected_conflict = case['expected_conflict']
            
            # 调用冲突分析方法
            actual_conflict = self.playlist_manager._analyze_insertion_conflict(insert_index)
            
            case_passed = actual_conflict == expected_conflict
            if not case_passed:
                all_cases_passed = False
                logger.warning(f"⚠️ Conflict analysis mismatch for index {insert_index}: expected {expected_conflict}, got {actual_conflict}")
            
            test_result['details']['cases'].append({
                'insert_index': insert_index,
                'expected_conflict': expected_conflict,
                'actual_conflict': actual_conflict,
                'passed': case_passed
            })
        
        test_result['passed'] = all_cases_passed
        
        if all_cases_passed:
            logger.info(f"✅ Insertion conflict analysis test PASSED")
        else:
            logger.error(f"❌ Insertion conflict analysis test FAILED")
        
        self.test_results.append(test_result)
        return test_result
    
    async def test_multiple_qa_insertions(self):
        """测试多次QA插入的行为"""
        logger.info("🧪 Testing multiple QA insertions...")
        
        start_time = time.time()
        
        # 记录初始状态
        initial_index = self.playlist_manager._current_playback_index
        initial_length = len(self.playlist_manager._playlist)
        
        # 执行3次连续QA插入
        qa_requests = [
            QAInsertionRequest(
                qa_id=f"test_qa_multi_{i}",
                question=f"第{i+1}个问题",
                answer=f"第{i+1}个问题的回答内容",
                priority="normal"
            )
            for i in range(3)
        ]
        
        insertion_results = []
        for qa_request in qa_requests:
            result = await self.playlist_manager.handle_qa_event(qa_request, None)
            insertion_results.append(result)
            
            # 模拟播放进展（每次插入后播放位置可能会发生变化）
            # 在实际场景中，播放会继续进行
            # 这里简单地增加播放位置模拟播放进展
            self.playlist_manager._current_playback_index += 1
        
        # 验证结果
        test_result = {
            'test_name': 'multiple_qa_insertions',
            'passed': False,
            'duration_ms': (time.time() - start_time) * 1000,
            'details': {'insertions': []}
        }
        
        try:
            # 验证所有插入都成功
            all_success = all(result.success for result in insertion_results)
            assert all_success, "All QA insertions should succeed"
            
            # 验证插入位置的合理性
            for i, result in enumerate(insertion_results):
                expected_min_position = initial_index + cfg.qa_insertion_offset
                # 由于每次插入后播放位置都在变化，位置计算会比较复杂
                # 这里只验证基本的合理性：插入位置应该大于初始位置
                assert result.insertion_index >= expected_min_position, f"Insertion {i} position should be >= {expected_min_position}"
                
                test_result['details']['insertions'].append({
                    'qa_id': result.qa_id,
                    'insertion_index': result.insertion_index,
                    'success': result.success
                })
            
            # 验证播放列表总长度
            final_length = len(self.playlist_manager._playlist)
            total_inserted_items = sum(len(result.items_inserted) for result in insertion_results)
            expected_final_length = initial_length + total_inserted_items
            assert final_length == expected_final_length, f"Expected final length {expected_final_length}, got {final_length}"
            
            test_result['passed'] = True
            test_result['details']['summary'] = {
                'initial_length': initial_length,
                'final_length': final_length,
                'total_inserted_items': total_inserted_items,
                'successful_insertions': len([r for r in insertion_results if r.success])
            }
            
            logger.info(f"✅ Multiple QA insertions test PASSED: {test_result['details']['summary']}")
            
        except AssertionError as e:
            test_result['passed'] = False
            test_result['error'] = str(e)
            logger.error(f"❌ Multiple QA insertions test FAILED: {e}")
        
        self.test_results.append(test_result)
        return test_result
    
    async def test_performance_metrics(self):
        """测试性能监控集成"""
        logger.info("🧪 Testing performance metrics integration...")
        
        start_time = time.time()
        
        # 创建QA请求
        qa_request = QAInsertionRequest(
            qa_id="test_qa_performance",
            question="性能测试问题",
            answer="这是一个用于测试性能监控集成的回答。",
            priority="normal"
        )
        
        # 执行插入
        result = await self.playlist_manager.handle_qa_event(qa_request, None)
        
        # 验证监控集成
        test_result = {
            'test_name': 'performance_metrics',
            'passed': False,
            'duration_ms': (time.time() - start_time) * 1000,
            'details': {}
        }
        
        try:
            # 验证插入成功
            assert result.success, f"QA insertion should succeed for performance test"
            
            # 检查监控数据（通过获取性能监控器的状态）
            from src.ai_live_streamer.monitoring.qa_performance_monitor import get_qa_performance_monitor
            monitor = get_qa_performance_monitor()
            
            # 获取当前统计信息
            stats = monitor.get_current_stats()
            
            # 验证统计信息中包含我们的请求
            assert stats['total_qa_requests'] > 0, "Should have recorded QA requests"
            
            # 验证成功插入计数
            assert stats['successful_insertions'] > 0, "Should have recorded successful insertions"
            
            test_result['passed'] = True
            test_result['details'] = {
                'total_requests': stats['total_qa_requests'],
                'successful_insertions': stats['successful_insertions'],
                'success_rate': stats['success_rate'],
                'average_latency_ms': stats['average_latency_ms']
            }
            
            logger.info(f"✅ Performance metrics test PASSED: {test_result['details']}")
            
        except Exception as e:
            test_result['passed'] = False
            test_result['error'] = str(e)
            logger.error(f"❌ Performance metrics test FAILED: {e}")
        
        self.test_results.append(test_result)
        return test_result
    
    async def run_full_test_suite(self):
        """运行完整测试套件"""
        logger.info("🚀 Starting Safe Distance QA Insertion E2E Test Suite...")
        
        try:
            # 1. 设置测试环境
            await self.setup()
            
            # 2. 创建初始播放列表
            session_id = await self.create_initial_playlist()
            
            # 3. 测试安全距离插入（偏移量=2）
            await self.test_safe_distance_offset_2()
            
            # 4. 测试插入冲突分析
            await self.test_insertion_conflict_analysis()
            
            # 5. 测试多次QA插入
            await self.test_multiple_qa_insertions()
            
            # 6. 测试性能指标集成
            await self.test_performance_metrics()
            
            # 7. 生成测试报告
            await self.generate_test_report()
            
            return all(result.get('passed', False) for result in self.test_results)
            
        except Exception as e:
            logger.error(f"❌ Test suite failed with exception: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            await self.teardown()
    
    async def generate_test_report(self):
        """生成测试报告"""
        logger.info("📋 Generating test report...")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result.get('passed', False))
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 计算总时间
        total_duration = sum(result.get('duration_ms', 0) for result in self.test_results)
        
        report = {
            'test_suite': 'Safe Distance QA Insertion E2E',
            'timestamp': time.time(),
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'success_rate': success_rate,
                'total_duration_ms': total_duration
            },
            'test_results': self.test_results,
            'configuration': {
                'qa_insertion_offset': cfg.qa_insertion_offset,
                'insertion_strategy': 'safe_distance'
            }
        }
        
        # 保存报告到文件
        report_path = Path("tests") / "results" / "safe_distance_qa_insertion_e2e_report.json"
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        logger.info(f"📊 Test Results Summary:")
        logger.info(f"   Total Tests: {total_tests}")
        logger.info(f"   Passed: {passed_tests}")
        logger.info(f"   Failed: {total_tests - passed_tests}")
        logger.info(f"   Success Rate: {success_rate:.1f}%")
        logger.info(f"   Total Duration: {total_duration:.0f}ms")
        logger.info(f"   Report saved to: {report_path}")
        
        # 打印失败的测试详情
        failed_tests = [result for result in self.test_results if not result.get('passed', False)]
        if failed_tests:
            logger.error("❌ Failed tests:")
            for failed_test in failed_tests:
                logger.error(f"   - {failed_test['test_name']}: {failed_test.get('error', 'Unknown error')}")

async def main():
    """主函数"""
    test_suite = SafeDistanceQAInsertionE2ETest()
    
    try:
        success = await test_suite.run_full_test_suite()
        exit_code = 0 if success else 1
        
        logger.info(f"🏁 Test suite completed with exit code: {exit_code}")
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # 设置事件循环策略（兼容不同操作系统）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())