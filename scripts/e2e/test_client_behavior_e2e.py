#!/usr/bin/env python3
"""
客户端行为端到端测试
测试客户端的请求风暴防护、状态管理和错误恢复能力

Author: Claude Code
Date: 2025-08-09
"""

import asyncio
import json
import time
import base64
from typing import Dict, Any, List
import websockets
from loguru import logger
import aiohttp

# 测试配置
TEST_SERVER = "http://localhost:8000"
WS_SERVER = "ws://localhost:8000/ws/v2/stream"

class ClientBehaviorTester:
    """客户端行为测试器"""
    
    def __init__(self):
        self.session_id = None
        self.client_id = f"test_client_{int(time.time() * 1000)}"
        self.request_history: List[Dict[str, Any]] = []
        self.response_history: List[Dict[str, Any]] = []
    
    async def setup(self):
        """设置测试环境"""
        logger.info("Setting up test environment...")
        
        # 启动流会话
        async with aiohttp.ClientSession() as session:
            payload = {
                "mode": "static",
                "content": [
                    "测试内容1",
                    "测试内容2",
                    "测试内容3",
                    "测试内容4",
                    "测试内容5"
                ]
            }
            
            async with session.post(f"{TEST_SERVER}/api/control/start-stream", 
                                   json=payload) as resp:
                if resp.status != 200:
                    raise Exception(f"Failed to start stream: {await resp.text()}")
                
                data = await resp.json()
                self.session_id = data["session_id"]
                logger.info(f"Stream started with session_id: {self.session_id}")
    
    async def test_request_storm_prevention(self):
        """测试请求风暴防护机制"""
        logger.info("\n=== Test: Request Storm Prevention ===")
        
        ws_url = f"{WS_SERVER}?client_id={self.client_id}&session_id={self.session_id}&protocol_version=2.0"
        
        async with websockets.connect(ws_url) as websocket:
            # 等待连接建立消息
            message = await websocket.recv()
            data = json.loads(message)
            assert data["type"] == "connection_established"
            
            # 快速连续发送多个相同索引的请求
            request_count = 10
            request_index = 0
            requests_sent = []
            
            logger.info(f"Sending {request_count} rapid requests for index {request_index}...")
            
            for i in range(request_count):
                request = {
                    "type": "content_request",
                    "index": request_index,
                    "request_id": f"req_storm_{i}"
                }
                await websocket.send(json.dumps(request))
                requests_sent.append(request)
                self.request_history.append({
                    "time": time.time(),
                    "request": request
                })
                # 极短延迟模拟请求风暴
                await asyncio.sleep(0.01)
            
            # 收集响应
            responses_received = []
            timeout = 5  # 5秒超时
            start_time = time.time()
            
            while len(responses_received) < request_count and (time.time() - start_time) < timeout:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=0.5)
                    data = json.loads(message)
                    responses_received.append(data)
                    self.response_history.append({
                        "time": time.time(),
                        "response": data
                    })
                except asyncio.TimeoutError:
                    break
            
            # 分析结果
            logger.info(f"Sent {request_count} requests, received {len(responses_received)} responses")
            
            # 验证：不应该收到所有请求的响应（请求风暴应该被防护）
            content_responses = [r for r in responses_received if r.get("type") == "content"]
            error_responses = [r for r in responses_received if r.get("type") == "error"]
            
            logger.info(f"Content responses: {len(content_responses)}")
            logger.info(f"Error responses: {len(error_responses)}")
            
            # 期望行为：
            # 1. 第一个请求应该成功
            # 2. 后续重复请求应该被忽略或返回错误
            assert len(content_responses) >= 1, "Should receive at least one content response"
            
            # 检查是否有重复的音频数据（不应该有）
            audio_data_set = set()
            for resp in content_responses:
                if "data" in resp:
                    # 使用音频数据的前100个字符作为指纹
                    fingerprint = resp["data"][:100] if len(resp["data"]) > 100 else resp["data"]
                    if fingerprint in audio_data_set:
                        logger.warning("⚠️ Duplicate audio data detected!")
                    audio_data_set.add(fingerprint)
            
            logger.info("✅ Request storm prevention test passed")
            return True
    
    async def test_request_lock_mechanism(self):
        """测试请求锁机制"""
        logger.info("\n=== Test: Request Lock Mechanism ===")
        
        ws_url = f"{WS_SERVER}?client_id={self.client_id}_lock&session_id={self.session_id}&protocol_version=2.0"
        
        async with websockets.connect(ws_url) as websocket:
            # 等待连接建立
            await websocket.recv()
            
            # 发送第一个请求
            request1 = {
                "type": "content_request",
                "index": 0,
                "request_id": "req_lock_1"
            }
            await websocket.send(json.dumps(request1))
            logger.info("Sent first request")
            
            # 立即发送第二个请求（不同索引）
            request2 = {
                "type": "content_request",
                "index": 1,
                "request_id": "req_lock_2"
            }
            await websocket.send(json.dumps(request2))
            logger.info("Sent second request immediately")
            
            # 收集响应
            responses = []
            for _ in range(2):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=3)
                    data = json.loads(message)
                    if data.get("type") == "content":
                        responses.append(data)
                        logger.info(f"Received response for index: {data.get('index')}")
                except asyncio.TimeoutError:
                    break
            
            # 验证：应该按顺序收到响应
            if len(responses) == 2:
                assert responses[0]["index"] == 0, "First response should be for index 0"
                assert responses[1]["index"] == 1, "Second response should be for index 1"
                logger.info("✅ Request lock mechanism test passed")
            else:
                logger.warning(f"⚠️ Expected 2 responses, got {len(responses)}")
            
            return True
    
    async def test_state_progression(self):
        """测试状态推进逻辑"""
        logger.info("\n=== Test: State Progression ===")
        
        ws_url = f"{WS_SERVER}?client_id={self.client_id}_state&session_id={self.session_id}&protocol_version=2.0"
        
        async with websockets.connect(ws_url) as websocket:
            # 等待连接建立
            await websocket.recv()
            
            # 按顺序请求多个内容块
            expected_sequence = [0, 1, 2, 3]
            received_sequence = []
            
            for index in expected_sequence:
                request = {
                    "type": "content_request",
                    "index": index,
                    "request_id": f"req_state_{index}"
                }
                await websocket.send(json.dumps(request))
                logger.info(f"Requested index: {index}")
                
                # 等待响应
                message = await asyncio.wait_for(websocket.recv(), timeout=3)
                data = json.loads(message)
                
                if data.get("type") == "content":
                    received_index = data.get("index")
                    received_sequence.append(received_index)
                    logger.info(f"Received index: {received_index}")
                    
                    # 验证响应的index是否正确
                    assert received_index == index, f"Expected index {index}, got {received_index}"
                
                # 短暂延迟，模拟播放
                await asyncio.sleep(0.1)
            
            # 验证序列完整性
            assert received_sequence == expected_sequence, \
                f"Sequence mismatch: expected {expected_sequence}, got {received_sequence}"
            
            logger.info("✅ State progression test passed")
            return True
    
    async def test_error_recovery(self):
        """测试错误恢复能力"""
        logger.info("\n=== Test: Error Recovery ===")
        
        ws_url = f"{WS_SERVER}?client_id={self.client_id}_error&session_id={self.session_id}&protocol_version=2.0"
        
        async with websockets.connect(ws_url) as websocket:
            # 等待连接建立
            await websocket.recv()
            
            # 发送一个无效的请求（超出范围的索引）
            invalid_request = {
                "type": "content_request",
                "index": 999,  # 超出范围
                "request_id": "req_error_1"
            }
            await websocket.send(json.dumps(invalid_request))
            logger.info("Sent invalid request (index out of range)")
            
            # 等待错误响应
            message = await asyncio.wait_for(websocket.recv(), timeout=3)
            data = json.loads(message)
            
            # 验证错误响应格式（符合API契约）
            if data.get("type") == "error":
                assert "error_code" in data, "Error response must have error_code"
                assert "message" in data, "Error response must have message"
                assert "recoverable" in data, "Error response must have recoverable flag"
                logger.info(f"Received error: {data['error_code']} - {data['message']}")
                
                # 验证可恢复性
                if data["recoverable"]:
                    # 发送有效请求进行恢复
                    valid_request = {
                        "type": "content_request",
                        "index": 0,
                        "request_id": "req_error_recovery"
                    }
                    await websocket.send(json.dumps(valid_request))
                    logger.info("Sent valid request for recovery")
                    
                    # 验证恢复成功
                    recovery_message = await asyncio.wait_for(websocket.recv(), timeout=3)
                    recovery_data = json.loads(recovery_message)
                    
                    assert recovery_data.get("type") == "content", "Should recover with content response"
                    logger.info("✅ Error recovery successful")
            elif data.get("type") == "stream_end":
                # 索引999可能触发流结束响应
                logger.info("Received stream_end response (expected for out-of-range index)")
            
            logger.info("✅ Error recovery test passed")
            return True
    
    async def cleanup(self):
        """清理测试环境"""
        if self.session_id:
            async with aiohttp.ClientSession() as session:
                url = f"{TEST_SERVER}/api/control/stop-stream/{self.session_id}"
                async with session.post(url) as resp:
                    if resp.status == 200:
                        logger.info("Stream stopped successfully")
                    else:
                        logger.warning(f"Failed to stop stream: {resp.status}")
    
    def print_summary(self):
        """打印测试摘要"""
        logger.info("\n=== Test Summary ===")
        logger.info(f"Total requests sent: {len(self.request_history)}")
        logger.info(f"Total responses received: {len(self.response_history)}")
        
        # 分析请求响应时间
        if self.request_history and self.response_history:
            avg_response_time = sum(
                r["time"] - self.request_history[0]["time"] 
                for r in self.response_history[:len(self.request_history)]
            ) / len(self.response_history)
            logger.info(f"Average response time: {avg_response_time*1000:.2f}ms")


async def main():
    """主测试流程"""
    tester = ClientBehaviorTester()
    
    try:
        # 设置
        await tester.setup()
        
        # 运行测试
        tests = [
            tester.test_request_storm_prevention(),
            tester.test_request_lock_mechanism(),
            tester.test_state_progression(),
            tester.test_error_recovery()
        ]
        
        results = await asyncio.gather(*tests, return_exceptions=True)
        
        # 检查结果
        success_count = sum(1 for r in results if r is True)
        failure_count = len(results) - success_count
        
        logger.info(f"\n=== Final Results ===")
        logger.info(f"Tests passed: {success_count}/{len(results)}")
        
        if failure_count > 0:
            logger.error(f"Tests failed: {failure_count}")
            for i, result in enumerate(results):
                if result is not True:
                    logger.error(f"Test {i+1} failed: {result}")
        
        # 打印摘要
        tester.print_summary()
        
        return success_count == len(results)
        
    finally:
        # 清理
        await tester.cleanup()


if __name__ == "__main__":
    # 配置日志
    logger.add("test_client_behavior.log", rotation="10 MB")
    
    # 运行测试
    success = asyncio.run(main())
    
    if success:
        logger.success("✅ All client behavior tests passed!")
        exit(0)
    else:
        logger.error("❌ Some client behavior tests failed")
        exit(1)