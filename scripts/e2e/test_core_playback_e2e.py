#!/usr/bin/env python3
"""
End-to-End Test for Core Audio Playback Functionality

This test serves as the "Guardian" of the system's most critical feature:
the ability to play audio through the WebSocket connection.

IMPORTANT: This test MUST pass before any code can be deployed.
"""

import asyncio
import base64
import json
import os
import struct
import sys
import time
import traceback
from pathlib import Path
from typing import Dict, Any, Optional

import aiohttp
import websockets
from loguru import logger

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Test configuration - support environment variables for CI/CD
TEST_CONFIG = {
    "api_base_url": os.getenv("E2E_API_BASE_URL", "http://localhost:8000"),
    "ws_base_url": os.getenv("E2E_WS_BASE_URL", "ws://localhost:8000"),
    "timeout_seconds": int(os.getenv("E2E_TIMEOUT_SECONDS", "10")),
    "request_timeout_ms": int(os.getenv("E2E_REQUEST_TIMEOUT_MS", "5000")),
    "min_audio_size_bytes": int(os.getenv("E2E_MIN_AUDIO_SIZE", "1000")),  # Minimum expected audio data size
}

# Test script content
TEST_SCRIPT = [
    {
        "index": 0,
        "content": "大家好，欢迎来到直播间！",
        "duration": 2.0,
        "metadata": {"type": "greeting"}
    },
    {
        "index": 1,
        "content": "今天我们要介绍一款非常棒的产品。",
        "duration": 3.0,
        "metadata": {"type": "introduction"}
    },
    {
        "index": 2,
        "content": "这个产品有三大特点。",
        "duration": 2.5,
        "metadata": {"type": "features"}
    }
]


class E2ETestResult:
    """Test result container"""
    def __init__(self):
        self.passed = False
        self.error_message = None
        self.steps_completed = []
        self.audio_data_received = False
        self.audio_format_valid = False
        self.websocket_connected = False
        self.session_id = None
        

class CorePlaybackE2ETest:
    """
    Core E2E test for audio playback functionality.
    Tests the complete flow from API to WebSocket to audio data.
    """
    
    def __init__(self):
        self.result = E2ETestResult()
        self.session_id = None
        self.client_id = f"e2e_test_{int(time.time())}"
        
    async def run(self) -> E2ETestResult:
        """Run the complete E2E test"""
        logger.info("=" * 60)
        logger.info("🚀 Starting Core Playback E2E Test")
        logger.info("=" * 60)
        
        try:
            # Step 1: Start stream via HTTP API
            await self._test_start_stream()
            
            # Step 2: Connect WebSocket
            await self._test_websocket_connection()
            
            # Step 3: Request and validate audio content
            await self._test_audio_playback()
            
            # Step 4: Clean up
            await self._test_stop_stream()
            
            self.result.passed = True
            logger.success("✅ All E2E tests passed!")
            
        except Exception as e:
            self.result.passed = False
            self.result.error_message = str(e)
            logger.error(f"❌ E2E test failed: {e}")
            logger.error(traceback.format_exc())
            
        finally:
            # Clean up WebSocket connection if still open
            if hasattr(self, 'websocket') and self.websocket:
                try:
                    await self.websocket.close()
                    logger.debug("WebSocket connection closed")
                except:
                    pass
            
            self._print_summary()
            
        return self.result
    
    async def _test_start_stream(self):
        """Test 1: Start stream via HTTP API"""
        logger.info("\n--- Test 1: Start Stream API ---")
        
        url = f"{TEST_CONFIG['api_base_url']}/api/control/start-stream"
        
        # Convert test script to simple content list for STATIC mode
        content = [item["content"] for item in TEST_SCRIPT]
        
        payload = {
            "mode": "static",  # Use STATIC mode for simplest test
            "content": content,  # List of strings for static content
            "persona_id": "王哥"  # Optional persona
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload) as response:
                if response.status != 200:
                    text = await response.text()
                    raise Exception(f"Start stream failed: {response.status} - {text}")
                
                data = await response.json()
                self.session_id = data.get("session_id")
                self.result.session_id = self.session_id
                
                if not self.session_id:
                    raise Exception("No session_id in response")
                
                logger.info(f"✅ Stream started with session_id: {self.session_id}")
                self.result.steps_completed.append("start_stream")
                
                # Give server time to initialize
                await asyncio.sleep(1)
    
    async def _test_websocket_connection(self):
        """Test 2: Connect to WebSocket and keep connection open"""
        logger.info("\n--- Test 2: WebSocket Connection ---")
        
        ws_url = (f"{TEST_CONFIG['ws_base_url']}/ws/v2/stream"
                 f"?client_id={self.client_id}"
                 f"&session_id={self.session_id}"
                 f"&protocol_version=2.0")
        
        try:
            # Connect and store websocket for use in other tests
            self.websocket = await websockets.connect(ws_url)
            self.result.websocket_connected = True
            
            # Wait for connection confirmation
            message = await asyncio.wait_for(
                self.websocket.recv(),
                timeout=TEST_CONFIG['timeout_seconds']
            )
            
            data = json.loads(message)
            if data.get("type") == "connection_established":
                logger.info(f"✅ WebSocket connected: {data}")
                self.result.steps_completed.append("websocket_connect")
            else:
                raise Exception(f"Unexpected connection response: {data}")
                
            # Test first content request
            await self._test_content_request(self.websocket)
                
        except asyncio.TimeoutError:
            raise Exception("WebSocket connection timeout")
        except Exception as e:
            raise Exception(f"WebSocket connection failed: {e}")
    
    async def _test_content_request(self, websocket):
        """Test 3: Request content and validate audio data"""
        logger.info("\n--- Test 3: Content Request & Audio Validation ---")
        
        # Send content request for first segment
        request = {
            "type": "content_request",
            "index": 0,
            "request_id": f"req_{int(time.time() * 1000)}"
        }
        
        logger.info(f"📤 Sending content request: {request}")
        await websocket.send(json.dumps(request))
        
        # Wait for audio response
        try:
            message = await asyncio.wait_for(
                websocket.recv(),
                timeout=TEST_CONFIG['timeout_seconds']
            )
            
            data = json.loads(message)
            logger.info(f"📥 Received response type: {data.get('type')}")
            logger.debug(f"📥 Full response: {json.dumps(data, indent=2)[:1000]}...")  # Log first 1000 chars
            
            if data.get("type") == "content":
                self._validate_audio_response(data)
                self.result.steps_completed.append("content_request")
            elif data.get("type") == "error":
                error_msg = data.get("message", "Unknown error")
                error_code = data.get("error_code", "UNKNOWN")
                logger.error(f"❌ Server error: {error_code} - {error_msg}")
                raise Exception(f"Server error: {error_code} - {error_msg}")
            else:
                raise Exception(f"Unexpected response type: {data.get('type')}")
                
        except asyncio.TimeoutError:
            raise Exception(f"Content request timeout after {TEST_CONFIG['timeout_seconds']}s")
    
    def _validate_audio_response(self, data: Dict[str, Any]):
        """Validate the audio response format and data
        
        Strictly validates against API Contract v2.0 specification.
        NO COMPATIBILITY MODE - responses MUST match the contract.
        """
        logger.info("\n--- Validating Audio Response (API Contract v2.0) ---")
        
        # Required fields according to API Contract v2.0
        required_fields = ["type", "index", "request_id", "format", 
                          "sample_rate", "channels", "duration_ms", "data", "metadata"]
        
        # Check for missing required fields
        missing_fields = [f for f in required_fields if f not in data]
        if missing_fields:
            raise Exception(f"❌ Response violates API Contract v2.0! Missing required fields: {missing_fields}\n"
                          f"Received fields: {list(data.keys())}")
        
        # Strict validation of field values
        if data["type"] != "content":
            raise Exception(f"❌ Invalid type: {data['type']} (API Contract requires: 'content')")
        
        if not isinstance(data["index"], int) or data["index"] < 0:
            raise Exception(f"❌ Invalid index: {data['index']} (API Contract requires: non-negative integer)")
        
        if data["format"] != "wav":
            raise Exception(f"❌ Invalid format: {data['format']} (API Contract requires: 'wav')")
        
        if data["sample_rate"] != 24000:
            raise Exception(f"❌ Invalid sample rate: {data['sample_rate']} (API Contract requires: 24000)")
        
        if data["channels"] != 1:
            raise Exception(f"❌ Invalid channels: {data['channels']} (API Contract requires: 1)")
        
        if not isinstance(data["duration_ms"], (int, float)) or data["duration_ms"] <= 0:
            raise Exception(f"❌ Invalid duration_ms: {data['duration_ms']} (API Contract requires: positive number)")
        
        if not isinstance(data["metadata"], dict):
            raise Exception(f"❌ Invalid metadata: {type(data['metadata'])} (API Contract requires: dictionary)")
        
        # Validate metadata required fields
        if "text" not in data["metadata"]:
            logger.warning("⚠️ metadata.text is missing (recommended by API Contract)")
        
        if "type" not in data["metadata"]:
            logger.warning("⚠️ metadata.type is missing (recommended by API Contract)")
        
        if "is_qa" not in data["metadata"]:
            logger.warning("⚠️ metadata.is_qa is missing (recommended by API Contract)")
        
        # Get audio data from the contract-specified field
        if "data" not in data:
            raise Exception(f"❌ No 'data' field found (API Contract requires 'data' field for audio)")
        
        # Log successful validation
        logger.info("✅ Response fully complies with API Contract v2.0")
        
        # Decode and validate audio data
        try:
            audio_bytes = base64.b64decode(data["data"])
            self.result.audio_data_received = True
            
            if len(audio_bytes) < TEST_CONFIG["min_audio_size_bytes"]:
                raise Exception(f"Audio data too small: {len(audio_bytes)} bytes")
            
            # Try to validate WAV header (might be raw PCM in current implementation)
            try:
                if self._validate_wav_header(audio_bytes):
                    logger.info("✅ Valid WAV format with header")
                    self.result.audio_format_valid = True
                else:
                    # Might be raw PCM without WAV header
                    logger.warning("⚠️ No valid WAV header found, assuming raw PCM")
                    # Still mark as valid if we have data
                    self.result.audio_format_valid = True
            except Exception as e:
                logger.warning(f"WAV header validation error: {e}, assuming raw PCM")
                self.result.audio_format_valid = True
            
            logger.info(f"✅ Audio data validated: {len(audio_bytes)} bytes")
            self.result.steps_completed.append("audio_validation")
            
        except Exception as e:
            raise Exception(f"Audio data validation failed: {e}")
    
    def _validate_wav_header(self, audio_bytes: bytes) -> bool:
        """Validate WAV file header"""
        if len(audio_bytes) < 44:
            return False
        
        # Check RIFF header
        if audio_bytes[0:4] != b'RIFF':
            logger.error(f"Invalid RIFF header: {audio_bytes[0:4]}")
            return False
        
        # Check WAVE format
        if audio_bytes[8:12] != b'WAVE':
            logger.error(f"Invalid WAVE format: {audio_bytes[8:12]}")
            return False
        
        # Check fmt chunk
        if audio_bytes[12:16] != b'fmt ':
            logger.error(f"Invalid fmt chunk: {audio_bytes[12:16]}")
            return False
        
        # Parse audio format (should be 1 for PCM)
        audio_format = struct.unpack('<H', audio_bytes[20:22])[0]
        if audio_format != 1:
            logger.error(f"Invalid audio format: {audio_format} (expected: 1 for PCM)")
            return False
        
        # Parse sample rate (should be 24000)
        sample_rate = struct.unpack('<I', audio_bytes[24:28])[0]
        if sample_rate != 24000:
            logger.error(f"Invalid sample rate in header: {sample_rate} (expected: 24000)")
            return False
        
        logger.info("✅ WAV header validation passed")
        return True
    
    async def _test_audio_playback(self):
        """Extended test: Request and validate all segments"""
        logger.info("\n--- Test 4: Multiple Segment Playback ---")
        
        # Already tested segment 0 in _test_content_request
        # Now test segments 1 and 2 to ensure complete playback works
        segments_validated = 1  # We already validated segment 0
        
        for segment_index in range(1, len(TEST_SCRIPT)):
            logger.info(f"\n📤 Requesting segment {segment_index}...")
            
            # Send content request for this segment
            request = {
                "type": "content_request",
                "index": segment_index,
                "request_id": f"req_{int(time.time() * 1000)}_{segment_index}"
            }
            
            await self.websocket.send(json.dumps(request))
            
            try:
                # Wait for audio response
                message = await asyncio.wait_for(
                    self.websocket.recv(),
                    timeout=TEST_CONFIG['timeout_seconds']
                )
                
                data = json.loads(message)
                
                if data.get("type") == "content":
                    # Validate this segment
                    logger.info(f"📥 Received segment {segment_index}")
                    
                    # Verify the index matches what we requested
                    if data.get("index") != segment_index:
                        raise Exception(f"Index mismatch: expected {segment_index}, got {data.get('index')}")
                    
                    # Validate audio format (reuse existing validation)
                    self._validate_audio_response(data)
                    
                    # Verify metadata text matches expected content
                    expected_text = TEST_SCRIPT[segment_index]["content"]
                    received_text = data.get("metadata", {}).get("text", "")
                    if received_text != expected_text:
                        logger.warning(f"Text mismatch for segment {segment_index}: expected '{expected_text}', got '{received_text}'")
                    
                    segments_validated += 1
                    logger.info(f"✅ Segment {segment_index} validated successfully")
                    
                elif data.get("type") == "error":
                    raise Exception(f"Error for segment {segment_index}: {data.get('message')}")
                else:
                    raise Exception(f"Unexpected response for segment {segment_index}: {data.get('type')}")
                    
            except asyncio.TimeoutError:
                raise Exception(f"Timeout waiting for segment {segment_index}")
        
        # Verify we validated all segments
        if segments_validated == len(TEST_SCRIPT):
            logger.info(f"✅ All {segments_validated} segments validated successfully!")
            self.result.steps_completed.append("playback_test")
        else:
            raise Exception(f"Only validated {segments_validated}/{len(TEST_SCRIPT)} segments")
    
    async def _test_stop_stream(self):
        """Test 5: Stop stream via HTTP API"""
        logger.info("\n--- Test 5: Stop Stream ---")
        
        if not self.session_id:
            logger.warning("No session_id, skipping stop stream test")
            return
        
        url = f"{TEST_CONFIG['api_base_url']}/api/control/stop-stream/{self.session_id}"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url) as response:
                if response.status == 200:
                    logger.info("✅ Stream stopped successfully")
                    self.result.steps_completed.append("stop_stream")
                else:
                    logger.warning(f"Stop stream returned: {response.status}")
    
    def _print_summary(self):
        """Print test summary"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 E2E Test Summary")
        logger.info("=" * 60)
        
        logger.info(f"Overall Result: {'✅ PASSED' if self.result.passed else '❌ FAILED'}")
        logger.info(f"Steps Completed: {len(self.result.steps_completed)}/5")
        
        # Detailed results
        checks = [
            ("Stream Started", "start_stream" in self.result.steps_completed),
            ("WebSocket Connected", self.result.websocket_connected),
            ("Content Requested", "content_request" in self.result.steps_completed),
            ("Audio Data Received", self.result.audio_data_received),
            ("Audio Format Valid", self.result.audio_format_valid),
        ]
        
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            logger.info(f"  {status} {check_name}")
        
        if self.result.error_message:
            logger.error(f"\nError: {self.result.error_message}")
        
        if self.result.session_id:
            logger.info(f"\nSession ID: {self.result.session_id}")


async def main():
    """Main test runner"""
    import argparse
    
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="E2E Test for Core Audio Playback")
    parser.add_argument("--api-url", default=TEST_CONFIG["api_base_url"],
                       help=f"API base URL (default: {TEST_CONFIG['api_base_url']})")
    parser.add_argument("--ws-url", default=TEST_CONFIG["ws_base_url"],
                       help=f"WebSocket base URL (default: {TEST_CONFIG['ws_base_url']})")
    parser.add_argument("--timeout", type=int, default=TEST_CONFIG["timeout_seconds"],
                       help=f"Timeout in seconds (default: {TEST_CONFIG['timeout_seconds']})")
    parser.add_argument("--verbose", action="store_true",
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Update configuration from command-line arguments
    if args.api_url:
        TEST_CONFIG["api_base_url"] = args.api_url
    if args.ws_url:
        TEST_CONFIG["ws_base_url"] = args.ws_url
    if args.timeout:
        TEST_CONFIG["timeout_seconds"] = args.timeout
    
    # Log the configuration being used
    logger.info(f"📝 Test Configuration:")
    logger.info(f"  API URL: {TEST_CONFIG['api_base_url']}")
    logger.info(f"  WebSocket URL: {TEST_CONFIG['ws_base_url']}")
    logger.info(f"  Timeout: {TEST_CONFIG['timeout_seconds']}s")
    
    test = CorePlaybackE2ETest()
    result = await test.run()
    
    # Exit with appropriate code
    sys.exit(0 if result.passed else 1)


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(sys.stdout, level="DEBUG", 
              format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")
    
    # Add file logging
    log_file = Path(__file__).parent / "e2e_test_results.log"
    logger.add(log_file, level="DEBUG", rotation="10 MB")
    
    # Run test
    asyncio.run(main())