#!/usr/bin/env python3
"""
End-to-End Test for QA Broadcast Integration

Tests the complete flow from QA submission to client playback,
verifying the new item_inserted message type and version control mechanism.

This test extends the core playback functionality with QA-specific scenarios.
"""

import asyncio
import base64
import json
import os
import sys
import time
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, List

import aiohttp
import websockets
from loguru import logger

# Import base test functionality
sys.path.insert(0, str(Path(__file__).parent))
from test_core_playback_e2e import CorePlaybackE2ETest, E2ETestResult, TEST_CONFIG


class QABroadcastE2ETest(CorePlaybackE2ETest):
    """
    Extended E2E test for QA broadcast functionality.
    Tests the complete QA flow: submission → broadcast → playback
    """
    
    def __init__(self):
        super().__init__()
        self.qa_messages_received = []
        self.playlist_version_received = 0
        self.qa_trace_id = None
        
    async def run(self) -> E2ETestResult:
        """Run the complete QA broadcast E2E test"""
        logger.info("=" * 60)
        logger.info("🚀 Starting QA Broadcast E2E Test")
        logger.info("=" * 60)
        
        try:
            # Phase 1: Basic setup (inherited from parent)
            await self._test_start_stream()
            await self._test_websocket_connection()
            
            # Phase 2: QA-specific tests
            await self._test_qa_submission_flow()
            await self._test_playlist_version_control()
            await self._test_qa_audio_playback()
            
            # Phase 3: Message handling validation
            await self._test_websocket_message_handling()
            
            # Phase 4: Cleanup
            await self._test_stop_stream()
            
            self.result.passed = True
            logger.success("✅ All QA Broadcast E2E tests passed!")
            
        except Exception as e:
            self.result.passed = False
            self.result.error_message = str(e)
            logger.error(f"❌ QA Broadcast E2E test failed: {e}")
            logger.error(traceback.format_exc())
            
        finally:
            if hasattr(self, 'websocket') and self.websocket:
                try:
                    await self.websocket.close()
                    logger.debug("WebSocket connection closed")
                except:
                    pass
            
            self._print_qa_summary()
            
        return self.result
    
    async def _test_qa_submission_flow(self):
        """Test QA submission and verify item_inserted message reception"""
        logger.info("\n--- QA Test 1: Submission Flow ---")
        
        if not self.session_id:
            raise Exception("No session_id available for QA test")
        
        # Get initial playlist state for comparison
        initial_playlist_state = await self._get_playlist_state()
        
        # Submit QA question via HTTP API
        qa_payload = {
            "text": "这个产品有什么特别的优势吗？",
            "user_name": "测试用户",
            "priority": "medium"
        }
        
        url = f"{TEST_CONFIG['api_base_url']}/api/control/sessions/{self.session_id}/questions"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=qa_payload) as response:
                if response.status != 200:
                    text = await response.text()
                    raise Exception(f"QA submission failed: {response.status} - {text}")
                
                qa_response = await response.json()
                # The actual response contains trace_id, not question_id
                trace_id = qa_response.get("trace_id")
                success = qa_response.get("success", False)
                
                if not trace_id:
                    logger.warning(f"QA response: {qa_response}")
                    raise Exception("No trace_id in QA response")
                
                if not success:
                    raise Exception(f"QA insertion failed: {qa_response}")
                
                logger.info(f"✅ QA submitted successfully: {trace_id}")
                
                # Store for later validation
                self.qa_trace_id = trace_id
        
        # Listen for item_inserted message on WebSocket
        logger.info("📡 Listening for item_inserted message...")
        
        try:
            # Wait for item_inserted notification
            message = await asyncio.wait_for(
                self.websocket.recv(),
                timeout=TEST_CONFIG['timeout_seconds']
            )
            
            data = json.loads(message)
            logger.info(f"📥 Received message type: {data.get('type')}")
            
            if data.get("type") == "item_inserted":
                await self._validate_item_inserted_message(data)
                self.qa_messages_received.append(data)
                logger.info("✅ item_inserted message validated successfully")
                self.result.steps_completed.append("qa_submission")
            else:
                # Could be playlist_updated instead
                logger.info(f"Received {data.get('type')} instead of item_inserted")
                if data.get("type") == "playlist_updated" and data.get("update_reason") == "qa_inserted":
                    logger.info("✅ Received playlist_updated for QA insertion")
                    self.qa_messages_received.append(data)
                    self.result.steps_completed.append("qa_submission")
                else:
                    raise Exception(f"Expected item_inserted or playlist_updated, got: {data.get('type')}")
                    
        except asyncio.TimeoutError:
            raise Exception(f"No QA broadcast message received within {TEST_CONFIG['timeout_seconds']}s")
    
    async def _validate_item_inserted_message(self, data: Dict[str, Any]):
        """Validate item_inserted message format against API Contract v2.1"""
        logger.info("🔍 Validating item_inserted message format...")
        
        # Required fields per API Contract v2.1
        required_fields = [
            "type", "schema_version", "insert_index", "items", 
            "playlist_version", "reason", "server_time"
        ]
        
        missing_fields = [f for f in required_fields if f not in data]
        if missing_fields:
            raise Exception(f"item_inserted message missing required fields: {missing_fields}")
        
        # Validate field values
        if data["type"] != "item_inserted":
            raise Exception(f"Invalid type: {data['type']}")
        
        if data["schema_version"] != "1.0":
            logger.warning(f"Unexpected schema version: {data['schema_version']}")
        
        if not isinstance(data["insert_index"], int) or data["insert_index"] < 0:
            raise Exception(f"Invalid insert_index: {data['insert_index']}")
        
        if not isinstance(data["items"], list) or len(data["items"]) == 0:
            raise Exception(f"Invalid items array: {data['items']}")
        
        if not isinstance(data["playlist_version"], int) or data["playlist_version"] <= 0:
            raise Exception(f"Invalid playlist_version: {data['playlist_version']}")
        
        # Store received version for version control tests
        self.playlist_version_received = data["playlist_version"]
        
        # Validate QA-specific fields
        if data["reason"] not in ["qa_inserted", "manual_update", "script_loaded"]:
            logger.warning(f"Unexpected reason: {data['reason']}")
        
        # Validate items structure (should have 3 items for QA: transition_in, answer, transition_out)
        if len(data["items"]) != 3:
            raise Exception(f"Expected 3 items for QA insertion, got {len(data['items'])}")
        
        for i, item in enumerate(data["items"]):
            # Updated to check for 'item_id' instead of 'id'
            if "item_id" not in item or "content" not in item:
                raise Exception(f"Invalid item structure: {item}")
            
            # Validate item types
            if i == 0 and item.get("type") != "qa_transition_in":
                logger.warning(f"First item type is not qa_transition_in: {item.get('type')}")
            elif i == 1 and item.get("type") != "qa_answer":
                logger.warning(f"Second item type is not qa_answer: {item.get('type')}")
            elif i == 2 and item.get("type") != "qa_transition_out":
                logger.warning(f"Third item type is not qa_transition_out: {item.get('type')}")
        
        logger.info("✅ item_inserted message format validation passed")
    
    async def _test_playlist_version_control(self):
        """Test playlist version control mechanism"""
        logger.info("\n--- QA Test 2: Playlist Version Control ---")
        
        if self.playlist_version_received <= 0:
            logger.warning("No playlist version received, skipping version control test")
            return
        
        # Record initial version
        initial_version = self.playlist_version_received
        logger.info(f"📊 Initial playlist version: {initial_version}")
        
        # Submit another QA to trigger version increment
        qa_payload = {
            "text": "产品的价格是多少？",
            "user_name": "测试用户2",
            "priority": "high"
        }
        
        url = f"{TEST_CONFIG['api_base_url']}/api/control/sessions/{self.session_id}/questions"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=qa_payload) as response:
                if response.status != 200:
                    logger.warning("Second QA submission failed, continuing with existing test")
                else:
                    logger.info("✅ Second QA submitted for version test")
        
        # Listen for version update
        try:
            message = await asyncio.wait_for(
                self.websocket.recv(),
                timeout=TEST_CONFIG['timeout_seconds']
            )
            
            data = json.loads(message)
            
            if "playlist_version" in data:
                new_version = data["playlist_version"]
                
                if new_version > initial_version:
                    logger.info(f"✅ Version control working: {initial_version} → {new_version}")
                    self.result.steps_completed.append("version_control")
                else:
                    raise Exception(f"Version did not increment: {initial_version} → {new_version}")
            else:
                logger.warning("No playlist_version field in response")
                
        except asyncio.TimeoutError:
            logger.warning("No version update message received")
    
    async def _test_qa_audio_playback(self):
        """Test QA audio content request and playback"""
        logger.info("\n--- QA Test 3: QA Audio Playback ---")
        
        if not self.qa_messages_received:
            logger.warning("No QA messages received, skipping QA audio test")
            return
        
        # Get the insert index from the first QA message
        qa_message = self.qa_messages_received[0]
        
        if qa_message.get("type") == "item_inserted":
            insert_index = qa_message.get("insert_index", 0)
            items = qa_message.get("items", [])
            
            logger.info(f"🎵 Testing QA audio at index {insert_index}, {len(items)} items")
            
            # Test each QA item (question + answer)
            for i, item in enumerate(items):
                current_index = insert_index + i
                await self._test_qa_audio_at_index(current_index, item)
                
            self.result.steps_completed.append("qa_audio_playback")
        else:
            logger.info("Skipping QA audio test for playlist_updated message type")
    
    async def _test_qa_audio_at_index(self, index: int, expected_item: Dict[str, Any]):
        """Test audio content for a specific QA item"""
        logger.info(f"🎵 Requesting QA audio at index {index}")
        
        request = {
            "type": "content_request",
            "index": index,
            "request_id": f"qa_req_{int(time.time() * 1000)}_{index}"
        }
        
        await self.websocket.send(json.dumps(request))
        
        try:
            message = await asyncio.wait_for(
                self.websocket.recv(),
                timeout=TEST_CONFIG['timeout_seconds']
            )
            
            data = json.loads(message)
            
            if data.get("type") == "content":
                # Validate QA audio response
                self._validate_qa_audio_response(data, expected_item)
                logger.info(f"✅ QA audio validated at index {index}")
                
            elif data.get("type") == "error":
                error_code = data.get("error_code", "UNKNOWN")
                if error_code == "CONTENT_NOT_FOUND":
                    logger.warning(f"QA content not ready yet at index {index}")
                else:
                    raise Exception(f"QA audio error: {error_code} - {data.get('message')}")
            else:
                raise Exception(f"Unexpected response for QA audio: {data.get('type')}")
                
        except asyncio.TimeoutError:
            raise Exception(f"Timeout waiting for QA audio at index {index}")
    
    def _validate_qa_audio_response(self, data: Dict[str, Any], expected_item: Dict[str, Any]):
        """Validate QA audio response format and content"""
        # Use parent validation for basic format
        self._validate_audio_response(data)
        
        # Additional QA-specific validation
        metadata = data.get("metadata", {})
        
        # Check if metadata indicates this is QA content
        if "is_qa" in metadata and metadata["is_qa"] is not True:
            logger.warning("Expected QA content but is_qa=False")
        
        # Validate content matches expected
        received_text = metadata.get("text", "")
        expected_text = expected_item.get("content", "")
        
        if expected_text and received_text != expected_text:
            logger.warning(f"Content mismatch - Expected: '{expected_text}', Got: '{received_text}'")
        
        logger.info("✅ QA audio content validated")
    
    async def _test_websocket_message_handling(self):
        """Test comprehensive WebSocket message handling"""
        logger.info("\n--- QA Test 4: WebSocket Message Handling ---")
        
        # Summary of messages received during test
        message_types = [msg.get("type") for msg in self.qa_messages_received]
        logger.info(f"📨 Message types received: {message_types}")
        
        # Validate we received expected message types
        expected_types = ["item_inserted", "playlist_updated"]
        received_qa_types = [t for t in message_types if t in expected_types]
        
        if not received_qa_types:
            logger.warning("No QA-related messages received")
        else:
            logger.info(f"✅ QA message types received: {received_qa_types}")
            self.result.steps_completed.append("websocket_handling")
        
        # Validate schema versions
        schema_versions = [
            msg.get("schema_version") for msg in self.qa_messages_received 
            if "schema_version" in msg
        ]
        
        if schema_versions:
            unique_versions = set(schema_versions)
            logger.info(f"📋 Schema versions seen: {unique_versions}")
            
            for version in unique_versions:
                if version != "1.0":
                    logger.warning(f"Non-standard schema version: {version}")
        
        logger.info("✅ WebSocket message handling validation completed")
    
    async def _get_playlist_state(self) -> Dict[str, Any]:
        """Get current playlist state via API for state validation"""
        url = f"{TEST_CONFIG['api_base_url']}/api/control/sessions/{self.session_id}/status"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status != 200:
                    logger.warning(f"Failed to get playlist state: {response.status}")
                    return {"items": [], "version": 0}
                
                data = await response.json()
                playlist_info = data.get("playlist", {})
                return {
                    "items": playlist_info.get("items", []),
                    "version": playlist_info.get("version", 0),
                    "total_items": playlist_info.get("total_items", 0)
                }
    
    def _print_qa_summary(self):
        """Print QA-specific test summary"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 QA Broadcast E2E Test Summary")
        logger.info("=" * 60)
        
        logger.info(f"Overall Result: {'✅ PASSED' if self.result.passed else '❌ FAILED'}")
        logger.info(f"Steps Completed: {len(self.result.steps_completed)}")
        
        # QA-specific results
        qa_checks = [
            ("Stream Started", "start_stream" in self.result.steps_completed),
            ("WebSocket Connected", self.result.websocket_connected),
            ("QA Submitted & Broadcast", "qa_submission" in self.result.steps_completed),
            ("Version Control", "version_control" in self.result.steps_completed),
            ("QA Audio Playback", "qa_audio_playback" in self.result.steps_completed),
            ("WebSocket Handling", "websocket_handling" in self.result.steps_completed),
        ]
        
        for check_name, check_result in qa_checks:
            status = "✅" if check_result else "❌"
            logger.info(f"  {status} {check_name}")
        
        # QA-specific statistics
        logger.info(f"\n📈 QA Statistics:")
        logger.info(f"  QA Messages Received: {len(self.qa_messages_received)}")
        logger.info(f"  Playlist Version Updates: {self.playlist_version_received}")
        
        if self.qa_messages_received:
            message_types = [msg.get("type") for msg in self.qa_messages_received]
            type_counts = {t: message_types.count(t) for t in set(message_types)}
            logger.info(f"  Message Type Breakdown: {type_counts}")
        
        if self.result.error_message:
            logger.error(f"\nError: {self.result.error_message}")


async def main():
    """Main test runner for QA broadcast E2E test"""
    import argparse
    
    parser = argparse.ArgumentParser(description="E2E Test for QA Broadcast Integration")
    parser.add_argument("--api-url", default=TEST_CONFIG["api_base_url"],
                       help=f"API base URL (default: {TEST_CONFIG['api_base_url']})")
    parser.add_argument("--ws-url", default=TEST_CONFIG["ws_base_url"],
                       help=f"WebSocket base URL (default: {TEST_CONFIG['ws_base_url']})")
    parser.add_argument("--timeout", type=int, default=TEST_CONFIG["timeout_seconds"],
                       help=f"Timeout in seconds (default: {TEST_CONFIG['timeout_seconds']})")
    parser.add_argument("--verbose", action="store_true",
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Update configuration
    if args.api_url:
        TEST_CONFIG["api_base_url"] = args.api_url
    if args.ws_url:
        TEST_CONFIG["ws_base_url"] = args.ws_url
    if args.timeout:
        TEST_CONFIG["timeout_seconds"] = args.timeout
    
    logger.info(f"📝 QA Broadcast Test Configuration:")
    logger.info(f"  API URL: {TEST_CONFIG['api_base_url']}")
    logger.info(f"  WebSocket URL: {TEST_CONFIG['ws_base_url']}")
    logger.info(f"  Timeout: {TEST_CONFIG['timeout_seconds']}s")
    
    test = QABroadcastE2ETest()
    result = await test.run()
    
    sys.exit(0 if result.passed else 1)


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(sys.stdout, level="DEBUG", 
              format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")
    
    # Add file logging
    log_file = Path(__file__).parent / "qa_broadcast_e2e_results.log"
    logger.add(log_file, level="DEBUG", rotation="10 MB")
    
    # Run test
    asyncio.run(main())