#!/usr/bin/env python3
"""
QA Failure Path Tests
Tests edge cases and error handling for QA functionality
"""

import asyncio
import aiohttp
import json
import os
import sys
from pathlib import Path
from typing import Dict, Any
from loguru import logger

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Test configuration
TEST_CONFIG = {
    "api_base_url": os.getenv("E2E_API_BASE_URL", "http://localhost:8000"),
    "timeout_seconds": int(os.getenv("E2E_TIMEOUT_SECONDS", "10")),
}


class QAFailurePathTests:
    """QA功能失败路径测试"""
    
    def __init__(self):
        self.api_base_url = TEST_CONFIG["api_base_url"]
        self.valid_session_id = None
        self.test_results = []
    
    async def setup(self):
        """Setup a valid session for testing"""
        logger.info("Setting up test environment...")
        
        # Start a valid stream session
        async with aiohttp.ClientSession() as session:
            start_payload = {
                "content_type": "static",
                "content": [
                    {"index": 0, "content": "Welcome to the test stream"},
                    {"index": 1, "content": "This is test content"},
                    {"index": 2, "content": "Thank you for testing"}
                ]
            }
            
            async with session.post(f"{self.api_base_url}/api/control/start", json=start_payload) as response:
                if response.status == 200:
                    data = await response.json()
                    self.valid_session_id = data.get("session_id")
                    logger.info(f"✅ Valid session created: {self.valid_session_id}")
                else:
                    logger.warning("Failed to create valid session, some tests may fail")
    
    async def submit_qa(self, session_id: str, text: str, priority: str = "medium") -> aiohttp.ClientResponse:
        """Submit a QA question"""
        qa_payload = {
            "text": text,
            "user_name": "Test User",
            "priority": priority
        }
        
        url = f"{self.api_base_url}/api/control/sessions/{session_id}/questions"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=qa_payload) as response:
                return {
                    "status": response.status,
                    "json": await response.json() if response.content_type == "application/json" else None,
                    "text": await response.text()
                }
    
    async def test_submit_qa_to_invalid_session(self):
        """测试向无效会话提交QA"""
        logger.info("\n--- Test: Submit QA to Invalid Session ---")
        
        result = await self.submit_qa(
            session_id="invalid_session_123456",
            text="Test question for invalid session"
        )
        
        success = result["status"] == 404
        if success:
            logger.success(f"✅ Test passed: Got expected 404 for invalid session")
        else:
            logger.error(f"❌ Test failed: Expected 404, got {result['status']}")
        
        self.test_results.append(("invalid_session", success))
        return success
    
    async def test_submit_empty_question(self):
        """测试提交空问题"""
        logger.info("\n--- Test: Submit Empty Question ---")
        
        if not self.valid_session_id:
            logger.warning("No valid session, skipping test")
            return False
        
        result = await self.submit_qa(
            session_id=self.valid_session_id,
            text=""
        )
        
        success = result["status"] in [400, 422]  # Bad Request or Unprocessable Entity
        if success:
            logger.success(f"✅ Test passed: Got expected error {result['status']} for empty question")
        else:
            logger.error(f"❌ Test failed: Expected 400/422, got {result['status']}")
        
        self.test_results.append(("empty_question", success))
        return success
    
    async def test_submit_invalid_priority(self):
        """测试提交无效优先级"""
        logger.info("\n--- Test: Submit Invalid Priority ---")
        
        if not self.valid_session_id:
            logger.warning("No valid session, skipping test")
            return False
        
        qa_payload = {
            "text": "Test question with invalid priority",
            "user_name": "Test User",
            "priority": "super_urgent"  # Invalid priority value
        }
        
        url = f"{self.api_base_url}/api/control/sessions/{self.valid_session_id}/questions"
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=qa_payload) as response:
                success = response.status in [400, 422]
                
                if success:
                    logger.success(f"✅ Test passed: Got expected error {response.status} for invalid priority")
                else:
                    logger.error(f"❌ Test failed: Expected 400/422, got {response.status}")
                
                self.test_results.append(("invalid_priority", success))
                return success
    
    async def test_concurrent_qa_submissions(self):
        """测试并发QA提交的锁机制"""
        logger.info("\n--- Test: Concurrent QA Submissions ---")
        
        if not self.valid_session_id:
            logger.warning("No valid session, skipping test")
            return False
        
        # Submit multiple QA requests concurrently
        tasks = [
            self.submit_qa(self.valid_session_id, f"Concurrent question {i}")
            for i in range(3)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successful and conflict responses
        statuses = []
        for result in results:
            if isinstance(result, dict):
                statuses.append(result["status"])
        
        success_count = sum(1 for s in statuses if s == 200)
        conflict_count = sum(1 for s in statuses if s == 409)
        
        # At least one should succeed, others might get conflict
        success = success_count >= 1
        
        if success:
            logger.success(f"✅ Test passed: {success_count} succeeded, {conflict_count} conflicts")
        else:
            logger.error(f"❌ Test failed: No successful submissions in concurrent test")
        
        self.test_results.append(("concurrent_submissions", success))
        return success
    
    async def test_extremely_long_question(self):
        """测试超长问题文本"""
        logger.info("\n--- Test: Extremely Long Question ---")
        
        if not self.valid_session_id:
            logger.warning("No valid session, skipping test")
            return False
        
        # Create a very long question (10000 characters)
        long_text = "这是一个非常长的问题 " * 1000
        
        result = await self.submit_qa(
            session_id=self.valid_session_id,
            text=long_text
        )
        
        # Should either succeed (with truncation) or fail gracefully
        success = result["status"] in [200, 400, 413, 422]
        
        if success:
            logger.success(f"✅ Test passed: System handled long question gracefully (status: {result['status']})")
        else:
            logger.error(f"❌ Test failed: Unexpected status {result['status']} for long question")
        
        self.test_results.append(("long_question", success))
        return success
    
    async def run_all_tests(self):
        """Run all failure path tests"""
        logger.info("=" * 60)
        logger.info("🚀 Starting QA Failure Path Tests")
        logger.info("=" * 60)
        
        # Setup
        await self.setup()
        
        # Run tests
        await self.test_submit_qa_to_invalid_session()
        await self.test_submit_empty_question()
        await self.test_submit_invalid_priority()
        await self.test_concurrent_qa_submissions()
        await self.test_extremely_long_question()
        
        # Print summary
        self.print_summary()
        
        # Return overall success
        return all(result[1] for result in self.test_results)
    
    def print_summary(self):
        """Print test summary"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 Test Summary")
        logger.info("=" * 60)
        
        passed = sum(1 for _, success in self.test_results if success)
        total = len(self.test_results)
        
        for test_name, success in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"  {test_name}: {status}")
        
        logger.info("")
        if passed == total:
            logger.success(f"🎉 All tests passed! ({passed}/{total})")
        else:
            logger.error(f"⚠️ Some tests failed: {passed}/{total} passed")
        
        logger.info("=" * 60)


async def main():
    """Main test runner"""
    tests = QAFailurePathTests()
    success = await tests.run_all_tests()
    
    if not success:
        sys.exit(1)
    
    return success


if __name__ == "__main__":
    asyncio.run(main())