#!/bin/bash

# TTS Cache 守护进程启动脚本（纯缓存服务）
# 适用于生产环境后台运行
# 注意：此服务为纯缓存服务，不进行 TTS 合成
# 所有合成由主应用的 cache_miss_engine 处理

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$PROJECT_ROOT/logs"
PID_DIR="$PROJECT_ROOT/run"

# 创建必要的目录
mkdir -p "$LOG_DIR" "$PID_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }

# 检查服务是否运行
check_service() {
    local url=$1
    curl -s "$url" > /dev/null 2>&1
}

# 启动 MinIO
start_minio() {
    print_info "启动 MinIO 服务..."
    
    # 创建数据目录
    mkdir -p "$PROJECT_ROOT/data/minio_data"
    
    # 检查是否已运行
    if check_service "http://localhost:9000/minio/health/live"; then
        print_success "MinIO 服务已在运行"
        return 0
    fi
    
    # 停止旧容器
    docker stop minio-standalone 2>/dev/null || true
    docker rm minio-standalone 2>/dev/null || true
    
    # 启动容器
    docker run -d --name minio-standalone \
        --restart unless-stopped \
        -p 9000:9000 -p 9001:9001 \
        -e MINIO_ROOT_USER=minioadmin \
        -e MINIO_ROOT_PASSWORD=minioadmin123 \
        -v "$PROJECT_ROOT/data/minio_data:/data" \
        minio/minio:latest server /data --console-address ":9001" \
        > "$LOG_DIR/minio_container.log" 2>&1
    
    # 等待启动
    print_info "等待 MinIO 启动..."
    for i in {1..30}; do
        if check_service "http://localhost:9000/minio/health/live"; then
            print_success "MinIO 服务启动成功"
            return 0
        fi
        sleep 1
    done
    
    print_error "MinIO 启动超时"
    return 1
}

# 启动 TTS Cache
start_tts_cache() {
    print_info "启动 TTS Cache 服务..."
    
    # 检查是否已运行
    if check_service "http://localhost:22243/metrics"; then
        print_success "TTS Cache 服务已在运行"
        return 0
    fi
    
    # 设置环境变量
    export MINIO_ENDPOINT="localhost:9000"
    export MINIO_ACCESS_KEY="minioadmin"
    export MINIO_SECRET_KEY="minioadmin123"
    export BUCKET_NAME="tts-cache"
    export CACHE_EXPIRY_DAYS=365
    export MONTHLY_CHAR_LIMIT=1000000
    export CIRCUIT_FAILURE_THRESHOLD=5
    export CIRCUIT_RECOVERY_TIMEOUT=60
    
    # 纯缓存服务不需要 API 密钥
    # TTS 合成由主应用的 cache_miss_engine 处理
    
    # 启动服务
    cd "$PROJECT_ROOT/services/tts-cache-cosyvoice"
    
    nohup python -m uvicorn src.main:app \
        --host 0.0.0.0 \
        --port 22243 \
        --workers 1 \
        > "$LOG_DIR/tts_cache.log" 2>&1 &
    
    TTS_PID=$!
    echo $TTS_PID > "$PID_DIR/tts_cache.pid"
    
    print_success "TTS Cache 服务已启动 (PID: $TTS_PID)"
    
    # 等待启动
    print_info "等待 TTS Cache 启动..."
    for i in {1..30}; do
        if check_service "http://localhost:22243/metrics"; then
            print_success "TTS Cache 服务启动成功"
            return 0
        fi
        sleep 1
    done
    
    print_error "TTS Cache 启动超时"
    return 1
}

# 停止服务
stop_services() {
    print_info "停止 TTS Cache 服务..."
    
    # 停止 TTS Cache
    if [ -f "$PID_DIR/tts_cache.pid" ]; then
        PID=$(cat "$PID_DIR/tts_cache.pid")
        if kill -0 $PID 2>/dev/null; then
            kill $PID
            print_success "TTS Cache 服务已停止"
        fi
        rm -f "$PID_DIR/tts_cache.pid"
    fi
    
    # 停止 MinIO
    docker stop minio-standalone 2>/dev/null || true
    docker rm minio-standalone 2>/dev/null || true
    print_success "MinIO 服务已停止"
}

# 显示状态
show_status() {
    print_info "服务状态检查"
    echo "========================"
    
    # MinIO 状态
    if check_service "http://localhost:9000/minio/health/live"; then
        print_success "MinIO 服务: 运行中"
    else
        print_error "MinIO 服务: 未运行"
    fi
    
    # TTS Cache 状态
    if check_service "http://localhost:22243/metrics"; then
        print_success "TTS Cache 服务: 运行中"
        if [ -f "$PID_DIR/tts_cache.pid" ]; then
            PID=$(cat "$PID_DIR/tts_cache.pid")
            echo "   PID: $PID"
        fi
    else
        print_error "TTS Cache 服务: 未运行"
    fi
    
    # 日志文件
    if [ -f "$LOG_DIR/tts_cache.log" ]; then
        SIZE=$(du -h "$LOG_DIR/tts_cache.log" | cut -f1)
        echo "   日志: $LOG_DIR/tts_cache.log ($SIZE)"
    fi
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            print_info "启动 TTS Cache 守护进程..."
            if start_minio && start_tts_cache; then
                echo ""
                print_success "TTS Cache 服务已在后台启动！"
                echo ""
                echo "📊 服务地址:"
                echo "  - TTS Cache API: http://localhost:22243"
                echo "  - MinIO 控制台: http://localhost:9001"
                echo ""
                echo "📋 管理命令:"
                echo "  - 查看状态: $0 status"
                echo "  - 停止服务: $0 stop"
                echo "  - 查看日志: tail -f $LOG_DIR/tts_cache.log"
            else
                print_error "服务启动失败"
                exit 1
            fi
            ;;
        stop)
            stop_services
            ;;
        status)
            show_status
            ;;
        restart)
            print_info "重启服务..."
            stop_services
            sleep 3
            exec $0 start
            ;;
        logs)
            if [ -f "$LOG_DIR/tts_cache.log" ]; then
                tail -f "$LOG_DIR/tts_cache.log"
            else
                print_error "日志文件不存在"
            fi
            ;;
        *)
            echo "用法: $0 {start|stop|status|restart|logs}"
            echo ""
            echo "命令:"
            echo "  start    启动服务（后台运行）"
            echo "  stop     停止服务"
            echo "  status   查看状态"
            echo "  restart  重启服务"
            echo "  logs     查看日志"
            exit 1
            ;;
    esac
}

# 检查依赖
if ! command -v docker &> /dev/null; then
    print_error "Docker 未安装"
    exit 1
fi

if ! command -v python &> /dev/null; then
    print_error "Python 未安装"
    exit 1
fi

# 运行主函数
main "$@"
