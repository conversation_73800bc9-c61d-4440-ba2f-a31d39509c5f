#!/bin/bash

# TTS Cache 集成验证脚本

set -e

echo "🔍 TTS Cache 集成验证"
echo "=" * 50

# 1. 检查 TTS Cache 服务
echo "1️⃣ 检查 TTS Cache 服务..."
if curl -s http://localhost:22243/metrics > /dev/null; then
    echo "✅ TTS Cache 服务正常运行"
    METRICS=$(curl -s http://localhost:22243/metrics)
    echo "📊 TTS Cache 指标："
    echo "$METRICS" | python3 -m json.tool 2>/dev/null || echo "$METRICS"
else
    echo "❌ TTS Cache 服务不可访问"
    exit 1
fi

# 2. 检查 MinIO 服务
echo ""
echo "2️⃣ 检查 MinIO 服务..."
if curl -s http://localhost:9000/minio/health/live > /dev/null; then
    echo "✅ MinIO 服务正常运行"
else
    echo "❌ MinIO 服务不可访问"
    exit 1
fi

# 3. 检查主 API 服务
echo ""
echo "3️⃣ 检查主 API 服务..."
if curl -s http://localhost:8001/docs > /dev/null; then
    echo "✅ 主 API 服务正常运行"
else
    echo "❌ 主 API 服务不可访问"
    exit 1
fi

# 4. 检查目录结构
echo ""
echo "4️⃣ 检查目录结构..."
if [ -d "data/minio_data" ]; then
    echo "✅ MinIO 数据目录存在: data/minio_data"
else
    echo "❌ MinIO 数据目录不存在"
fi

if [ -d "data/htmlcov" ]; then
    echo "✅ 测试覆盖率目录存在: data/htmlcov"
else
    echo "⚠️  测试覆盖率目录不存在（可选）"
fi

if [ -f "design_docs/TTS_CACHE_安装成功总结.md" ]; then
    echo "✅ 安装文档已移动到正确位置"
else
    echo "❌ 安装文档位置不正确"
fi

# 5. 检查配置
echo ""
echo "5️⃣ 检查配置..."
if grep -q "http://localhost:22243" config.yml; then
    echo "✅ TTS Cache URL 配置正确"
else
    echo "❌ TTS Cache URL 配置错误"
fi

if grep -q "tts_cache_proxy" config.yml; then
    echo "✅ TTS 引擎配置为 tts_cache_proxy"
else
    echo "❌ TTS 引擎配置错误"
fi

# 6. 检查启动脚本
echo ""
echo "6️⃣ 检查启动脚本..."
if [ -f "scripts/run_server_with_tts_cache.py" ]; then
    echo "✅ 集成启动脚本存在"
else
    echo "❌ 集成启动脚本不存在"
fi

if [ -f "scripts/install_tts_cache.sh" ]; then
    echo "✅ 安装脚本存在"
else
    echo "❌ 安装脚本不存在"
fi

if [ -f "scripts/test_tts_cache.sh" ]; then
    echo "✅ 测试脚本存在"
else
    echo "❌ 测试脚本不存在"
fi

# 7. 测试 TTS Cache 功能（如果有 API 密钥）
echo ""
echo "7️⃣ 测试 TTS Cache 功能..."
if [ -f "secrets/dashscope_api_key.txt" ]; then
    API_KEY=$(cat secrets/dashscope_api_key.txt)
    if [ "$API_KEY" != "mock_key_for_testing" ] && [ -n "$API_KEY" ]; then
        echo "🔑 检测到真实 API 密钥，测试 TTS 合成..."
        RESPONSE=$(curl -s -X POST http://localhost:22243/speech/ \
            -H "Content-Type: application/json" \
            -d '{"text": "集成测试", "voice": "longanran"}' \
            -w "%{http_code}")
        
        HTTP_CODE="${RESPONSE: -3}"
        if [ "$HTTP_CODE" = "200" ]; then
            echo "✅ TTS 合成功能正常"
        else
            echo "⚠️  TTS 合成测试失败 (HTTP $HTTP_CODE) - 可能是 API 密钥或网络问题"
        fi
    else
        echo "⚠️  使用模拟 API 密钥，跳过 TTS 合成测试"
    fi
else
    echo "⚠️  未找到 API 密钥文件，跳过 TTS 合成测试"
fi

echo ""
echo "🎉 TTS Cache 集成验证完成！"
echo ""
echo "📋 服务状态总结："
echo "  - TTS Cache API: http://localhost:22243 ✅"
echo "  - MinIO 控制台: http://localhost:9001 ✅"
echo "  - 主 API 服务: http://localhost:8001 ✅"
echo ""
echo "📁 目录结构："
echo "  - data/minio_data/ (MinIO 数据)"
echo "  - data/htmlcov/ (测试覆盖率)"
echo "  - design_docs/ (设计文档)"
echo "  - scripts/ (启动和管理脚本)"
echo ""
echo "🚀 启动命令："
echo "  - 集成启动: cd scripts && python run_server_with_tts_cache.py"
echo "  - 基础启动: cd scripts && python run_server.py"
echo "  - 带 TTS Cache: cd scripts && python run_server.py --with-tts-cache"
echo ""
echo "✅ TTS Cache 已成功集成到主服务中！"
