#!/bin/bash

# 启动本地 TTS 缓存服务（纯缓存服务，不使用 Docker）
# 注意：此服务不进行 TTS 合成，仅负责音频缓存

# 设置环境变量
export MINIO_ENDPOINT="localhost:9000"  # 需要单独启动 MinIO
export MINIO_ACCESS_KEY="minioadmin"
export MINIO_SECRET_KEY="minioadmin123"
export BUCKET_NAME="tts-cache"
export CACHE_EXPIRY_DAYS=365
export MONTHLY_CHAR_LIMIT=1000000
export CIRCUIT_FAILURE_THRESHOLD=5
export CIRCUIT_RECOVERY_TIMEOUT=60

# 纯缓存服务不需要 API 密钥
# TTS 合成由主应用的 cache_miss_engine 处理

# 创建必要的目录
mkdir -p data/minio_data
mkdir -p services/tts-cache-cosyvoice/logs

echo "启动 TTS 缓存服务..."
echo "服务地址: http://localhost:22243"
echo "API 文档: http://localhost:22243/docs"

# 启动服务
cd services/tts-cache-cosyvoice
python -m uvicorn src.main:app --reload --port 22243 --host 0.0.0.0