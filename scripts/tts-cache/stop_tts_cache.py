#!/usr/bin/env python3
"""
停止 TTS Cache 服务的脚本
"""

import subprocess
import psutil
import sys
import time

def stop_tts_cache_process():
    """停止 TTS Cache 进程"""
    print("🛑 查找并停止 TTS Cache 进程...")
    
    stopped_count = 0
    
    # 查找 TTS Cache 相关进程
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            
            # 匹配 TTS Cache 相关进程
            if any(pattern in cmdline for pattern in [
                'uvicorn src.main:app',
                'tts-cache-cosyvoice',
                'start_tts_cache_local.sh'
            ]):
                print(f"  停止进程 {proc.info['pid']}: {proc.info['name']}")
                proc.terminate()
                stopped_count += 1
                
                # 等待进程结束
                try:
                    proc.wait(timeout=5)
                except psutil.TimeoutExpired:
                    print(f"  强制终止进程 {proc.info['pid']}")
                    proc.kill()
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if stopped_count > 0:
        print(f"✅ 已停止 {stopped_count} 个 TTS Cache 相关进程")
    else:
        print("ℹ️  未找到运行中的 TTS Cache 进程")

def stop_minio_container():
    """停止 MinIO 容器"""
    print("🛑 停止 MinIO 容器...")
    
    try:
        # 检查容器是否存在
        result = subprocess.run(
            ["docker", "ps", "-q", "-f", "name=minio-standalone"],
            capture_output=True, text=True, check=False
        )
        
        if result.stdout.strip():
            # 停止容器
            subprocess.run(["docker", "stop", "minio-standalone"], 
                         capture_output=True, check=False)
            print("  MinIO 容器已停止")
            
            # 删除容器
            subprocess.run(["docker", "rm", "minio-standalone"], 
                         capture_output=True, check=False)
            print("  MinIO 容器已删除")
            print("✅ MinIO 服务已停止")
        else:
            print("ℹ️  MinIO 容器未运行")
            
    except FileNotFoundError:
        print("⚠️  Docker 不可用，跳过 MinIO 容器停止")
    except Exception as e:
        print(f"❌ 停止 MinIO 容器时出错: {e}")

def check_services_stopped():
    """检查服务是否已停止"""
    print("🔍 验证服务停止状态...")
    
    import requests
    
    services = [
        ("TTS Cache", "http://localhost:22243/metrics"),
        ("MinIO", "http://localhost:9000/minio/health/live")
    ]
    
    all_stopped = True
    
    for service_name, url in services:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                print(f"⚠️  {service_name} 仍在运行")
                all_stopped = False
            else:
                print(f"✅ {service_name} 已停止")
        except requests.RequestException:
            print(f"✅ {service_name} 已停止")
    
    return all_stopped

def main():
    """主函数"""
    print("🛑 停止 TTS Cache 服务")
    print("=" * 30)
    
    # 停止 TTS Cache 进程
    stop_tts_cache_process()
    
    # 停止 MinIO 容器
    stop_minio_container()
    
    # 等待一下让服务完全停止
    time.sleep(2)
    
    # 验证服务停止状态
    if check_services_stopped():
        print("\n✅ 所有 TTS Cache 服务已成功停止")
    else:
        print("\n⚠️  部分服务可能仍在运行，请手动检查")
        print("💡 提示:")
        print("  - 检查进程: ps aux | grep tts")
        print("  - 检查容器: docker ps")
        print("  - 强制停止: pkill -f 'uvicorn src.main:app'")

if __name__ == "__main__":
    main()
