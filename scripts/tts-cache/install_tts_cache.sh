#!/bin/bash

# TTS-Cache 快速安装脚本
# 基于成功验证的安装方法

set -e

echo "🚀 开始安装 TTS-Cache 服务..."

# 检查必要的工具
echo "📋 检查系统要求..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v curl &> /dev/null; then
    echo "❌ curl 未安装，请先安装 curl"
    exit 1
fi

# 检查 API 密钥
echo "🔑 检查 API 密钥..."
if [ ! -f "secrets/dashscope_api_key.txt" ]; then
    echo "⚠️  未找到 API 密钥文件"
    echo "请创建 secrets/dashscope_api_key.txt 并添加您的阿里云 DashScope API 密钥"
    echo "或者服务将以模拟模式运行"
    mkdir -p secrets
    echo "mock_key_for_testing" > secrets/dashscope_api_key.txt
    echo "✅ 已创建模拟 API 密钥文件"
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p data/minio_data
chmod 755 data/minio_data
mkdir -p services/tts-cache-cosyvoice/logs
chmod 755 services/tts-cache-cosyvoice/logs
echo "✅ 目录创建完成"

# 停止可能存在的旧容器
echo "🧹 清理旧的 MinIO 容器..."
docker stop minio-standalone 2>/dev/null || true
docker rm minio-standalone 2>/dev/null || true

# 启动 MinIO 服务
echo "🗄️  启动 MinIO 存储服务..."
docker run -d --name minio-standalone \
  -p 9000:9000 -p 9001:9001 \
  -e MINIO_ROOT_USER=minioadmin \
  -e MINIO_ROOT_PASSWORD=minioadmin123 \
  -v $(pwd)/data/minio_data:/data \
  minio/minio:latest server /data --console-address ":9001"

echo "⏳ 等待 MinIO 启动..."
sleep 5

# 检查 MinIO 是否启动成功
if ! curl -s http://localhost:9000/minio/health/live > /dev/null; then
    echo "❌ MinIO 启动失败"
    docker logs minio-standalone
    exit 1
fi
echo "✅ MinIO 启动成功"

# 启动 TTS Cache 服务
echo "🎤 启动 TTS Cache 服务..."
if [ ! -f "start_tts_cache_local.sh" ]; then
    echo "❌ 未找到 start_tts_cache_local.sh 脚本"
    exit 1
fi

chmod +x start_tts_cache_local.sh

# 在后台启动 TTS Cache 服务
echo "🔄 启动 TTS Cache 服务（后台运行）..."
nohup ./start_tts_cache_local.sh > tts_cache.log 2>&1 &
TTS_PID=$!

echo "⏳ 等待 TTS Cache 服务启动..."
sleep 10

# 验证服务
echo "🔍 验证服务安装..."
MAX_RETRIES=30
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if curl -s http://localhost:22243/metrics > /dev/null; then
        echo "✅ TTS Cache 服务启动成功"
        break
    fi
    
    RETRY_COUNT=$((RETRY_COUNT + 1))
    echo "⏳ 等待服务启动... ($RETRY_COUNT/$MAX_RETRIES)"
    sleep 2
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    echo "❌ TTS Cache 服务启动失败"
    echo "查看日志："
    tail -20 tts_cache.log
    exit 1
fi

# 显示服务状态
echo ""
echo "🎉 TTS-Cache 安装成功！"
echo ""
echo "📊 服务状态："
curl -s http://localhost:22243/metrics | python3 -m json.tool 2>/dev/null || curl -s http://localhost:22243/metrics

echo ""
echo "🌐 服务地址："
echo "  - TTS Cache API: http://localhost:22243"
echo "  - API 文档: http://localhost:22243/docs"
echo "  - MinIO 控制台: http://localhost:9001 (minioadmin/minioadmin123)"
echo ""
echo "📝 测试命令："
echo "  curl http://localhost:22243/metrics"
echo "  curl -X POST http://localhost:22243/speech/ -H 'Content-Type: application/json' -d '{\"text\": \"测试\", \"voice\": \"longanran\"}'"
echo ""
echo "📋 管理命令："
echo "  - 查看 TTS 日志: tail -f tts_cache.log"
echo "  - 查看 MinIO 日志: docker logs minio-standalone"
echo "  - 停止服务: kill $TTS_PID && docker stop minio-standalone"
echo ""
echo "✅ 安装完成！"
