#!/usr/bin/env python3
"""
独立启动 TTS Cache 服务的脚本（纯缓存服务）
用于生产环境或需要单独管理 TTS Cache 服务的场景

注意：此服务为纯缓存服务，不进行 TTS 合成
所有合成由主应用的 cache_miss_engine 处理
"""

import subprocess
import time
import signal
import sys
import os
import requests
from pathlib import Path
import atexit

class TTSCacheStandaloneManager:
    """独立管理 TTS Cache 服务"""
    
    def __init__(self):
        self.minio_process = None
        self.tts_cache_process = None
        self.project_root = Path(__file__).parent.parent
        
    def check_docker(self):
        """检查 Docker 是否可用"""
        try:
            subprocess.run(["docker", "--version"], check=True, capture_output=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def check_service_health(self, url, timeout=30):
        """检查服务健康状态"""
        for i in range(timeout):
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    return True
            except requests.RequestException:
                pass
            time.sleep(1)
        return False
    
    def start_minio(self):
        """启动 MinIO 服务"""
        print("🗄️  启动 MinIO 存储服务...")
        
        # 创建数据目录
        minio_data_dir = self.project_root / "data" / "minio_data"
        minio_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查是否已经运行
        try:
            if self.check_service_health("http://localhost:9000/minio/health/live", timeout=3):
                print("✅ MinIO 服务已在运行")
                return True
        except:
            pass
        
        # 停止可能存在的旧容器
        try:
            subprocess.run(["docker", "stop", "minio-standalone"], 
                         capture_output=True, check=False)
            subprocess.run(["docker", "rm", "minio-standalone"], 
                         capture_output=True, check=False)
        except:
            pass
        
        # 启动 MinIO 容器
        cmd = [
            "docker", "run", "-d", "--name", "minio-standalone",
            "-p", "9000:9000", "-p", "9001:9001",
            "-e", "MINIO_ROOT_USER=minioadmin",
            "-e", "MINIO_ROOT_PASSWORD=minioadmin123",
            "-v", f"{minio_data_dir}:/data",
            "minio/minio:latest", "server", "/data", "--console-address", ":9001"
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"✅ MinIO 容器启动成功: {result.stdout.strip()}")
            
            # 等待 MinIO 启动
            print("⏳ 等待 MinIO 服务启动...")
            if self.check_service_health("http://localhost:9000/minio/health/live"):
                print("✅ MinIO 服务启动成功")
                return True
            else:
                print("❌ MinIO 服务启动超时")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ MinIO 启动失败: {e.stderr}")
            return False
    
    def start_tts_cache(self):
        """启动 TTS Cache 服务"""
        print("🎤 启动 TTS Cache 服务...")
        
        # 检查是否已经运行
        try:
            if self.check_service_health("http://localhost:22243/metrics", timeout=3):
                print("✅ TTS Cache 服务已在运行")
                return True
        except:
            pass
        
        # 检查启动脚本
        start_script = self.project_root / "scripts" / "start_tts_cache_local.sh"
        if not start_script.exists():
            print(f"❌ 未找到 TTS Cache 启动脚本: {start_script}")
            return False
        
        # 确保脚本可执行
        os.chmod(start_script, 0o755)
        
        # 创建日志目录
        log_dir = self.project_root / "services" / "tts-cache-cosyvoice" / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 启动 TTS Cache 服务
        try:
            print("⏳ 启动 TTS Cache 服务...")
            # 切换到项目根目录执行脚本
            self.tts_cache_process = subprocess.Popen(
                [str(start_script)],
                cwd=str(self.project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # 等待服务启动
            time.sleep(10)
            
            # 检查服务是否启动成功
            if self.check_service_health("http://localhost:22243/metrics"):
                print("✅ TTS Cache 服务启动成功")
                return True
            else:
                print("❌ TTS Cache 服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ TTS Cache 启动异常: {e}")
            return False
    
    def start_all(self):
        """启动所有 TTS Cache 相关服务"""
        print("🚀 独立启动 TTS Cache 服务组件...")
        print("=" * 50)
        
        # 检查 Docker
        if not self.check_docker():
            print("❌ Docker 不可用，无法启动 MinIO")
            return False
        
        # 启动 MinIO
        if not self.start_minio():
            return False
        
        # 启动 TTS Cache
        if not self.start_tts_cache():
            return False
        
        print("\n🎉 TTS Cache 服务组件启动完成！")
        print("📊 服务地址:")
        print("  - TTS Cache API: http://localhost:22243")
        print("  - TTS Cache 文档: http://localhost:22243/docs")
        print("  - MinIO 控制台: http://localhost:9001")
        print("  - MinIO API: http://localhost:9000")
        print("\n📋 管理命令:")
        print("  - 查看状态: curl http://localhost:22243/metrics")
        print("  - 停止服务: python scripts/stop_tts_cache.py")
        print("  - 查看日志: docker logs minio-standalone")
        print("\n✅ TTS Cache 独立服务启动成功！")
        return True
    
    def stop_all(self):
        """停止所有服务"""
        print("🛑 停止 TTS Cache 服务...")
        
        # 停止 TTS Cache 进程
        if self.tts_cache_process:
            try:
                self.tts_cache_process.terminate()
                self.tts_cache_process.wait(timeout=10)
                print("✅ TTS Cache 服务已停止")
            except:
                try:
                    self.tts_cache_process.kill()
                except:
                    pass
        
        # 停止 MinIO 容器
        try:
            subprocess.run(["docker", "stop", "minio-standalone"], 
                         capture_output=True, check=False)
            subprocess.run(["docker", "rm", "minio-standalone"], 
                         capture_output=True, check=False)
            print("✅ MinIO 服务已停止")
        except:
            pass
    
    def show_status(self):
        """显示服务状态"""
        print("📊 TTS Cache 服务状态检查")
        print("=" * 40)
        
        # 检查 MinIO
        try:
            if self.check_service_health("http://localhost:9000/minio/health/live", timeout=3):
                print("✅ MinIO 服务: 运行中")
            else:
                print("❌ MinIO 服务: 未运行")
        except:
            print("❌ MinIO 服务: 未运行")
        
        # 检查 TTS Cache
        try:
            if self.check_service_health("http://localhost:22243/metrics", timeout=3):
                print("✅ TTS Cache 服务: 运行中")
                # 获取详细状态
                try:
                    response = requests.get("http://localhost:22243/metrics", timeout=2)
                    print("📈 TTS Cache 指标:")
                    print(response.text)
                except:
                    pass
            else:
                print("❌ TTS Cache 服务: 未运行")
        except:
            print("❌ TTS Cache 服务: 未运行")

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n🛑 收到停止信号，正在关闭服务...")
    if hasattr(signal_handler, 'manager'):
        signal_handler.manager.stop_all()
    sys.exit(0)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="TTS Cache 独立服务管理器")
    parser.add_argument("action", choices=["start", "stop", "status", "restart"], 
                       help="操作类型")
    
    args = parser.parse_args()
    
    manager = TTSCacheStandaloneManager()
    signal_handler.manager = manager
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    atexit.register(manager.stop_all)
    
    if args.action == "start":
        if manager.start_all():
            print("\n🔄 服务已启动，按 Ctrl+C 停止服务")
            try:
                # 保持运行状态
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 收到中断信号")
        else:
            print("❌ 服务启动失败")
            sys.exit(1)
    
    elif args.action == "stop":
        manager.stop_all()
        print("✅ 服务已停止")
    
    elif args.action == "status":
        manager.show_status()
    
    elif args.action == "restart":
        print("🔄 重启 TTS Cache 服务...")
        manager.stop_all()
        time.sleep(2)
        if manager.start_all():
            print("✅ 服务重启成功")
        else:
            print("❌ 服务重启失败")
            sys.exit(1)

if __name__ == "__main__":
    main()
