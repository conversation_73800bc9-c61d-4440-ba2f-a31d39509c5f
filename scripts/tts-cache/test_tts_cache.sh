#!/bin/bash

# TTS-Cache 服务测试脚本
# 注意：此为纯缓存服务测试，不测试 TTS 合成功能

set -e

echo "🧪 开始测试 TTS-Cache 服务..."

# 测试服务可用性
echo "1️⃣ 测试服务可用性..."
if curl -s http://localhost:22243/docs > /dev/null; then
    echo "✅ API 文档可访问"
else
    echo "❌ API 文档不可访问"
    exit 1
fi

# 测试指标端点
echo "2️⃣ 测试指标端点..."
METRICS=$(curl -s http://localhost:22243/metrics)
if echo "$METRICS" | grep -q "total_requests"; then
    echo "✅ 指标端点正常"
    echo "📊 当前指标："
    echo "$METRICS" | python3 -m json.tool 2>/dev/null || echo "$METRICS"
else
    echo "❌ 指标端点异常"
    exit 1
fi

# 测试 MinIO 连接
echo "3️⃣ 测试 MinIO 连接..."
if curl -s http://localhost:9000/minio/health/live > /dev/null; then
    echo "✅ MinIO 服务正常"
else
    echo "❌ MinIO 服务异常"
    exit 1
fi

# 测试缓存查询（未命中应返回 404）
echo "4️⃣ 测试缓存查询..."
RESPONSE=$(curl -s -X POST http://localhost:22243/speech/ \
    -H "Content-Type: application/json" \
    -d '{"text": "测试缓存未命中", "voice": "longanran"}' \
    -w "%{http_code}")

HTTP_CODE="${RESPONSE: -3}"
if [ "$HTTP_CODE" = "404" ]; then
    echo "✅ 缓存未命中正确返回 404"
else
    echo "⚠️  预期 404，实际返回 $HTTP_CODE"
fi

# 测试缓存上传功能
echo "5️⃣ 测试缓存上传功能..."
# 创建测试音频文件
echo -n ' ' > /tmp/test_audio.pcm
curl -s -X PUT http://localhost:22243/cache/ \
    -F "text=测试上传" \
    -F "voice=longanran" \
    -F "format=pcm" \
    -F "sample_rate=24000" \
    -F "audio_data=@/tmp/test_audio.pcm" \
    -w "%{http_code}" > /tmp/upload_response

UPLOAD_HTTP_CODE=$(tail -c 3 /tmp/upload_response)
if [ "$UPLOAD_HTTP_CODE" = "200" ]; then
    echo "✅ 缓存上传功能正常"
else
    echo "⚠️  缓存上传功能测试失败 (HTTP $UPLOAD_HTTP_CODE)"
fi
rm -f /tmp/test_audio.pcm /tmp/upload_response

# 显示最终状态
echo ""
echo "📊 最终服务状态："
curl -s http://localhost:22243/metrics | python3 -m json.tool 2>/dev/null || curl -s http://localhost:22243/metrics

echo ""
echo "🎉 TTS-Cache 服务测试完成！"
echo ""
echo "📋 服务信息："
echo "  - TTS Cache API: http://localhost:22243"
echo "  - API 文档: http://localhost:22243/docs"
echo "  - MinIO 控制台: http://localhost:9001"
echo ""
echo "✅ 所有核心功能正常运行"
