#!/bin/bash

# TTS-Cache 监控仪表板
# 提供实时监控和统计信息

clear

while true; do
    echo -e "\033[H\033[2J"  # 清屏
    echo "======================================"
    echo "   TTS-Cache 监控仪表板"
    echo "   $(date '+%Y-%m-%d %H:%M:%S')"
    echo "======================================"
    echo
    
    # 服务状态
    echo "📡 服务状态:"
    if curl -s http://localhost:22243/ > /dev/null; then
        echo "   TTS-Cache: ✅ 运行中"
        
        # 获取指标
        METRICS=$(curl -s http://localhost:22243/metrics)
        
        echo
        echo "📊 性能指标:"
        echo "   缓存命中率: $(echo $METRICS | jq -r '.cache_hit_rate')"
        echo "   总请求数: $(echo $METRICS | jq -r '.total_requests')"
        echo "   缓存命中: $(echo $METRICS | jq -r '.cache_hits')"
        echo "   缓存未中: $(echo $METRICS | jq -r '.cache_misses')"
        echo "   API 调用: $(echo $METRICS | jq -r '.api_calls')"
        echo "   平均响应时间: $(echo $METRICS | jq -r '.avg_response_time_ms')ms"
        echo "   熔断器状态: $(echo $METRICS | jq -r '.circuit_breaker_state')"
        
        echo
        echo "💰 配额使用:"
        echo "   月度字符使用: $(echo $METRICS | jq -r '.monthly_chars_used') / $(echo $METRICS | jq -r '.monthly_chars_limit')"
        echo "   剩余配额: $(echo $METRICS | jq -r '.monthly_chars_remaining')"
    else
        echo "   TTS-Cache: ❌ 离线"
    fi
    
    if curl -s http://localhost:9000/minio/health/live > /dev/null; then
        echo "   MinIO: ✅ 运行中"
    else
        echo "   MinIO: ❌ 离线"
    fi
    
    echo
    echo "📦 容器状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(tts-cache|minio)"
    
    echo
    echo "💾 存储使用:"
    docker exec tts-cache-minio mc admin info local 2>/dev/null | grep -E "(Total|Used)" || echo "   无法获取存储信息"
    
    echo
    echo "按 Ctrl+C 退出"
    
    sleep 5
done