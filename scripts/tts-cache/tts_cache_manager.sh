#!/bin/bash

# TTS Cache 服务管理脚本
# 提供便捷的 TTS Cache 服务管理命令

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo "TTS Cache 服务管理器"
    echo "===================="
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  start          启动 TTS Cache 服务"
    echo "  stop           停止 TTS Cache 服务"
    echo "  restart        重启 TTS Cache 服务"
    echo "  status         查看服务状态"
    echo "  logs           查看服务日志"
    echo "  test           测试服务功能"
    echo "  clean          清理服务数据"
    echo ""
    echo "示例:"
    echo "  $0 start       # 启动服务"
    echo "  $0 status      # 查看状态"
    echo "  $0 logs        # 查看日志"
    echo ""
}

# 启动服务
start_service() {
    print_info "启动 TTS Cache 服务..."
    cd "$PROJECT_ROOT"
    python scripts/start_tts_cache_standalone.py start
}

# 停止服务
stop_service() {
    print_info "停止 TTS Cache 服务..."
    cd "$PROJECT_ROOT"
    python scripts/stop_tts_cache.py
}

# 重启服务
restart_service() {
    print_info "重启 TTS Cache 服务..."
    cd "$PROJECT_ROOT"
    python scripts/start_tts_cache_standalone.py restart
}

# 查看状态
show_status() {
    print_info "查看 TTS Cache 服务状态..."
    cd "$PROJECT_ROOT"
    python scripts/start_tts_cache_standalone.py status
}

# 查看日志
show_logs() {
    print_info "查看服务日志..."
    
    echo ""
    echo "📋 MinIO 日志:"
    echo "=============="
    if docker ps -q -f name=minio-standalone > /dev/null 2>&1; then
        docker logs --tail=50 minio-standalone
    else
        print_warning "MinIO 容器未运行"
    fi
    
    echo ""
    echo "📋 TTS Cache 日志:"
    echo "=================="
    LOG_DIR="$PROJECT_ROOT/services/tts-cache-cosyvoice/logs"
    if [ -d "$LOG_DIR" ]; then
        find "$LOG_DIR" -name "*.log" -type f -exec tail -50 {} \;
    else
        print_warning "未找到 TTS Cache 日志目录"
    fi
}

# 测试服务
test_service() {
    print_info "测试 TTS Cache 服务..."
    cd "$PROJECT_ROOT"
    
    if [ -f "scripts/test_tts_cache.sh" ]; then
        bash scripts/test_tts_cache.sh
    else
        print_error "测试脚本不存在"
        exit 1
    fi
}

# 清理数据
clean_data() {
    print_warning "这将删除所有 TTS Cache 数据，包括缓存的音频文件"
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "清理 TTS Cache 数据..."
        
        # 停止服务
        stop_service
        
        # 清理 MinIO 数据
        if [ -d "$PROJECT_ROOT/data/minio_data" ]; then
            print_info "清理 MinIO 数据..."
            rm -rf "$PROJECT_ROOT/data/minio_data"/*
            print_success "MinIO 数据已清理"
        fi
        
        # 清理日志
        if [ -d "$PROJECT_ROOT/services/tts-cache-cosyvoice/logs" ]; then
            print_info "清理日志文件..."
            rm -f "$PROJECT_ROOT/services/tts-cache-cosyvoice/logs"/*.log
            print_success "日志文件已清理"
        fi
        
        print_success "数据清理完成"
    else
        print_info "取消清理操作"
    fi
}

# 主函数
main() {
    case "${1:-}" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        test)
            test_service
            ;;
        clean)
            clean_data
            ;;
        help|--help|-h)
            show_help
            ;;
        "")
            print_error "请指定命令"
            echo ""
            show_help
            exit 1
            ;;
        *)
            print_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 检查依赖
check_dependencies() {
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        print_warning "Docker 未安装，MinIO 服务将无法启动"
    fi
}

# 运行主函数
check_dependencies
main "$@"
