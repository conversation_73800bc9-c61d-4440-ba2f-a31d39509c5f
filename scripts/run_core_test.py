#!/usr/bin/env python3
"""
核心路径测试脚本 - Python 版本
用于快速验证系统核心功能，防止 AttributeError 等集成错误

替代原有的 Docker 版本，提供更简单、快速的本地测试体验
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path
from typing import Dict, List, Optional


class Colors:
    """ANSI 颜色代码"""
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color


class CoreTestRunner:
    """核心测试运行器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.test_results_dir = self.project_root / "tests" / "results"
        self.max_log_lines = 50
        
    def print_colored(self, message: str, color: str = Colors.NC):
        """打印带颜色的消息"""
        print(f"{color}{message}{Colors.NC}")
        
    def print_header(self, title: str):
        """打印标题头"""
        separator = "=" * 50
        self.print_colored(separator, Colors.BLUE)
        self.print_colored(f"🚀 {title}", Colors.BLUE)
        self.print_colored(separator, Colors.BLUE)
        print()
        
    def check_conda_environment(self) -> bool:
        """检查并确保在 aidev conda 环境中"""
        self.print_colored("🔍 检查 Conda 环境...", Colors.YELLOW)
        
        # 检查是否在 conda 环境中
        conda_prefix = os.environ.get('CONDA_PREFIX', '')
        if not conda_prefix:
            self.print_colored("❌ 未检测到 Conda 环境", Colors.RED)
            self.print_colored("请先激活 aidev 环境: conda activate aidev", Colors.RED)
            return False
            
        # 检查是否是 aidev 环境
        env_name = os.path.basename(conda_prefix)
        if env_name != 'aidev':
            self.print_colored(f"⚠️  当前环境是 '{env_name}'，但期望 'aidev'", Colors.YELLOW)
            self.print_colored("请切换到 aidev 环境: conda activate aidev", Colors.YELLOW)
            return False
            
        self.print_colored(f"✅ 已在 aidev 环境中 ({conda_prefix})", Colors.GREEN)
        return True
        
    def check_python_version(self) -> bool:
        """检查 Python 版本"""
        self.print_colored("🐍 检查 Python 版本...", Colors.YELLOW)
        
        version = sys.version_info
        if version.major != 3 or version.minor < 11:
            self.print_colored(f"❌ Python 版本过低: {version.major}.{version.minor}", Colors.RED)
            self.print_colored("需要 Python 3.11 或更高版本", Colors.RED)
            return False
            
        self.print_colored(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}", Colors.GREEN)
        return True
        
    def setup_environment_variables(self):
        """设置测试环境变量"""
        self.print_colored("⚙️  设置环境变量...", Colors.YELLOW)
        
        test_env_vars = {
            'PYTHONPATH': str(self.project_root / "src"),
            'PYTHONDONTWRITEBYTECODE': '1',
            'PYTHONUNBUFFERED': '1',
            'TEST_MODE': 'true',
            'DISABLE_TTS_CACHE': 'true',
            'DISABLE_ELASTICSEARCH': 'true',
            'DISABLE_REDIS': 'true',
        }
        
        for key, value in test_env_vars.items():
            os.environ[key] = value
            
        self.print_colored(f"✅ 已设置 {len(test_env_vars)} 个环境变量", Colors.GREEN)
        
    def prepare_test_directory(self):
        """准备测试结果目录"""
        self.print_colored("🧹 准备测试目录...", Colors.YELLOW)
        
        # 创建测试结果目录
        self.test_results_dir.mkdir(parents=True, exist_ok=True)
        
        # 清理旧的测试结果文件
        old_files = list(self.test_results_dir.glob("*.log")) + \
                   list(self.test_results_dir.glob("*.xml")) + \
                   list(self.test_results_dir.glob("*.html"))
                   
        for file in old_files:
            try:
                file.unlink()
            except OSError:
                pass  # 忽略删除失败
                
        self.print_colored(f"✅ 测试目录已准备: {self.test_results_dir}", Colors.GREEN)
        
    def check_dependencies(self) -> bool:
        """检查必要的依赖是否已安装"""
        self.print_colored("📦 检查测试依赖...", Colors.YELLOW)
        
        required_packages = ['pytest', 'pytest-html', 'pytest-asyncio']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
                
        if missing_packages:
            self.print_colored("❌ 缺少以下依赖包:", Colors.RED)
            for package in missing_packages:
                self.print_colored(f"  - {package}", Colors.RED)
            self.print_colored("\n请安装缺少的依赖:", Colors.RED)
            self.print_colored("pip install -e \".[dev]\" pytest-html", Colors.YELLOW)
            return False
            
        self.print_colored("✅ 所有依赖已安装", Colors.GREEN)
        return True
        
    def run_tests(self) -> int:
        """运行核心路径测试"""
        print()
        self.print_colored("🧪 运行核心路径测试...", Colors.YELLOW)
        self.print_colored("=" * 32, Colors.YELLOW)
        
        # 构建 pytest 命令
        test_file = self.project_root / "tests" / "test_critical_path_e2e.py"
        junit_file = self.test_results_dir / "junit.xml"
        html_file = self.test_results_dir / "report.html"
        log_file = self.test_results_dir / "test_output.log"
        
        pytest_cmd = [
            sys.executable, "-m", "pytest",
            str(test_file),
            "-vv",
            "--tb=long",
            "--color=yes",
            f"--junit-xml={junit_file}",
            f"--html={html_file}",
            "--self-contained-html"
        ]
        
        # 运行测试并捕获输出
        try:
            with open(log_file, 'w', encoding='utf-8') as log:
                process = subprocess.run(
                    pytest_cmd,
                    cwd=self.project_root,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    encoding='utf-8'
                )
                
                # 同时输出到终端和日志文件
                output = process.stdout
                print(output)
                log.write(output)
                
            return process.returncode
            
        except Exception as e:
            self.print_colored(f"❌ 运行测试时发生错误: {e}", Colors.RED)
            return 1
            
    def show_results(self, exit_code: int):
        """显示测试结果"""
        print()
        self.print_colored("=" * 50, Colors.BLUE)
        
        if exit_code == 0:
            self.print_colored("✅ 核心路径测试通过！", Colors.GREEN)
            self.print_colored("=" * 50, Colors.GREEN)
            
            # 显示测试统计
            junit_file = self.test_results_dir / "junit.xml"
            html_file = self.test_results_dir / "report.html"
            
            print()
            print("📊 测试报告:")
            if junit_file.exists():
                print(f"  - JUnit 报告: {junit_file}")
            if html_file.exists():
                print(f"  - HTML 报告: {html_file}")
                
        else:
            self.print_colored("❌ 核心路径测试失败", Colors.RED)
            self.print_colored("=" * 50, Colors.RED)
            print()
            self.print_colored("📋 错误详情:", Colors.YELLOW)
            self.print_colored("-" * 32, Colors.YELLOW)
            
            # 显示错误日志摘要
            log_file = self.test_results_dir / "test_output.log"
            if log_file.exists():
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        
                    # 查找包含错误的行
                    error_lines = []
                    for i, line in enumerate(lines):
                        if any(keyword in line for keyword in 
                               ['ERROR', 'FAILED', 'AttributeError', 'ImportError', 'TypeError']):
                            # 包含上下文
                            start = max(0, i-2)
                            end = min(len(lines), i+3)
                            error_lines.extend(lines[start:end])
                            
                    if error_lines:
                        for line in error_lines[-self.max_log_lines:]:
                            print(line.rstrip())
                    else:
                        # 如果没找到特定错误，显示最后几行
                        for line in lines[-self.max_log_lines:]:
                            print(line.rstrip())
                            
                except Exception:
                    print("无法读取测试日志文件")
            else:
                print("未找到测试日志文件")
                
            self.print_colored("-" * 32, Colors.YELLOW)
            print()
            print("💡 调试建议:")
            print(f"  1. 查看完整日志: cat {self.test_results_dir}/test_output.log")
            if (self.test_results_dir / "report.html").exists():
                print(f"  2. 查看 HTML 报告: open {self.test_results_dir}/report.html")
            print("  3. 检查代码中的属性访问（.text vs .content）")
            print("  4. 验证数据模型定义是否一致")
            
        self.print_colored("=" * 50, Colors.BLUE)
        
    def run(self) -> int:
        """运行完整的测试流程"""
        start_time = time.time()
        
        self.print_header("AI Live Streamer 核心路径测试")
        
        # 1. 检查环境
        if not self.check_conda_environment():
            return 1
            
        if not self.check_python_version():
            return 1
            
        # 2. 检查依赖
        if not self.check_dependencies():
            return 1
            
        # 3. 设置环境
        self.setup_environment_variables()
        self.prepare_test_directory()
        
        # 4. 运行测试
        exit_code = self.run_tests()
        
        # 5. 显示结果
        self.show_results(exit_code)
        
        # 计算执行时间
        duration = int(time.time() - start_time)
        print()
        print(f"⏱️  总耗时: {duration} 秒")
        
        return exit_code


def main():
    """主入口函数"""
    runner = CoreTestRunner()
    return runner.run()


if __name__ == "__main__":
    sys.exit(main())