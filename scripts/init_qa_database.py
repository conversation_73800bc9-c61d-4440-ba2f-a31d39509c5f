#!/usr/bin/env python3
"""QA系统数据库初始化脚本"""

import sqlite3
import os
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_qa_database():
    """创建QA知识库数据库"""
    db_path = Path("data/qa_knowledge.db")
    db_path.parent.mkdir(exist_ok=True)
    
    logger.info(f"初始化QA数据库: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建表结构
    cursor.executescript("""
        -- QA知识库主表
        CREATE TABLE IF NOT EXISTS qa_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            question TEXT NOT NULL,
            answer TEXT NOT NULL,
            category VARCHAR(50),
            tags TEXT,  -- JSON array
            embedding_id INTEGER,  -- Faiss索引ID
            hit_count INTEGER DEFAULT 0,
            confidence_score REAL DEFAULT 1.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 创建全文搜索索引
        CREATE VIRTUAL TABLE IF NOT EXISTS qa_entries_fts USING fts5(
            question, answer, content=qa_entries
        );
        
        -- 待审核的LLM生成答案
        CREATE TABLE IF NOT EXISTS pending_qa_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            question TEXT NOT NULL,
            generated_answer TEXT NOT NULL,
            context TEXT,  -- JSON
            confidence_score REAL,
            hit_count INTEGER DEFAULT 1,
            status VARCHAR(20) DEFAULT 'pending',  -- pending, approved, rejected
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            reviewed_at TIMESTAMP,
            reviewed_by VARCHAR(100)
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_qa_category ON qa_entries(category);
        CREATE INDEX IF NOT EXISTS idx_qa_confidence ON qa_entries(confidence_score);
        CREATE INDEX IF NOT EXISTS idx_qa_hit_count ON qa_entries(hit_count);
        CREATE INDEX IF NOT EXISTS idx_pending_status ON pending_qa_entries(status);
        CREATE INDEX IF NOT EXISTS idx_qa_created_at ON qa_entries(created_at);
        CREATE INDEX IF NOT EXISTS idx_pending_created_at ON pending_qa_entries(created_at);
    """)
    
    # 插入一些初始测试数据
    test_data = [
        ("产品价格是多少？", "我们的产品目前售价299元，限时优惠中！", "价格咨询"),
        ("有什么优惠活动？", "现在购买享受7折优惠，还有买二送一的活动！", "优惠活动"),
        ("如何下单购买？", "您可以点击直播间的购买链接，或者联系客服下单。", "购买指导"),
        ("产品有什么特点？", "我们的产品采用高品质材料，具有耐用、美观、实用等特点。", "产品介绍"),
        ("配送需要多久？", "我们提供全国包邮，一般3-5个工作日即可送达。", "物流配送"),
    ]
    
    for question, answer, category in test_data:
        cursor.execute("""
            INSERT OR IGNORE INTO qa_entries (question, answer, category, tags)
            VALUES (?, ?, ?, ?)
        """, (question, answer, category, '["热门", "常见问题"]'))
        
        # 同时插入到FTS索引
        entry_id = cursor.lastrowid
        if entry_id:
            cursor.execute("""
                INSERT OR IGNORE INTO qa_entries_fts (rowid, question, answer) 
                VALUES (?, ?, ?)
            """, (entry_id, question, answer))
    
    conn.commit()
    
    # 验证数据
    cursor.execute("SELECT COUNT(*) FROM qa_entries")
    count = cursor.fetchone()[0]
    logger.info(f"QA条目总数: {count}")
    
    cursor.execute("SELECT COUNT(*) FROM qa_entries_fts")
    fts_count = cursor.fetchone()[0] 
    logger.info(f"FTS索引条目数: {fts_count}")
    
    conn.close()
    logger.info(f"数据库初始化完成: {db_path}")


def create_data_directories():
    """创建必要的数据目录"""
    directories = [
        "data",
        "logs", 
        "tests/results",
        "cache"
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        logger.info(f"创建目录: {dir_path}")


if __name__ == "__main__":
    create_data_directories()
    create_qa_database()
    logger.info("QA系统初始化完成！")