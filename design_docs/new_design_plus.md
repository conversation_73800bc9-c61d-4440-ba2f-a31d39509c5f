# AI Live Streamer - 增强版架构设计文档 (v2.1)

#### 1. 核心理念与原则 (保持不变)

我们依然坚守 **“大道至简 (KISS)”** 的核心原则。系统的基础是基于 `asyncio` 的 **简单并发流处理模型**，确保了系统的响应速度和可维护性。在此基础上，我们引入了旧设计中经过验证的、有价值的功能模块，将它们作为可插拔的组件或独立的任务，而非复杂的状态机节点。

#### 2. 增强版架构图

新架构在原有的控制器-执行者模型基础上，增加了对 **情境感知** 和 **离线数据处理** 的支持。

```mermaid
graph TD
    subgraph "离线处理层 (Offline)"
        OpForm[运营表单系统] -->|1. 配置| Prefect[Prefect ETL 工作流]
        Prefect -->|2. 处理| StyleCorpus[Style Corpus 话术库]
        Prefect -->|2. 处理| ES[Elasticsearch<br/>知识库索引]
    end

    subgraph API层
        A[FastAPI Endpoints<br>/prepare, /start, /preview]
    end

    subgraph "控制层 (Controller)"
        B(LiveStreamController<br/>纯协调器)
        C{{asyncio.Queue<br/>question_queue}}
        D{{asyncio.Event<br/>pause_event}}
        PaceTask(Pace Monitor Task)
    end

    subgraph "执行层 (Actors/Workers)"
        E(MainContentPlayer<br/>接收WPM, Pause等参数)
        F(QAManager<br/>内置高级RAG)
        SG(ScriptGenerator<br/>内置AIDA模型, 使用话术库)
    end

    subgraph "外部服务与数据"
        G[TTS Service]
        H[LLM Service]
        ViewerAPI[观众数 API]
    end

    %% 核心流程
    A -- "3. /start (config_id)" --> B
    A -- "4. /submit_question" --> C

    B ==> |"5. create_task()"| E
    B ==> |"5. create_task()"| F_Monitor["monitor_questions()"]
    B ==> |"5. create_task()"| SG_Task["generate_script()"]
    B ==> |"5. create_task()"| PaceTask

    F_Monitor -.-> |"发现问题"| B
    B --> |"中断"| E
    B ==> |"处理"| F
    
    E -- "播放" --> G
    SG_Task -->|"生成句子"| E
    
    F -- "调用" --> H
    F -- "检索" --> ES
    F -- "调用" --> G

    SG -- "调用" --> H
    SG -- "检索" --> StyleCorpus

    PaceTask --> |"调用"| ViewerAPI
    PaceTask --> |"7. set_pace()"| E

```

#### 3. 组件职责定义 (增强版)

##### 3.1. 离线数据处理与运营工具 (新增)

这是在直播开始**之前**运行的独立流程，负责将运营知识转化为系统可用的数据。

*   **运营表单系统 (`OperationForm`)**:
    *   **职责**: 提供一个详尽的前端界面，供运营人员配置直播参数，内容涵盖旧设计中提到的所有方面：人设、SKU信息、核心卖点、合规要求、话术文档等。
    *   **产出**: 一个包含所有配置的JSON对象。

*   **Prefect ETL 工作流**:
    *   **职责**: 接收运营表单的配置，自动化地处理数据。
        *   从上传的文档 (`PDF`, `Markdown`) 中提取知识，进行清洗、分块。
        *   调用嵌入模型，将知识块存入 `Elasticsearch` 知识库。
        *   分析话术文档，构建 `Style Corpus` (话术库)，按开场、卖点、CTA等场景分类。
    *   **触发**: 可手动触发，或通过API调用。

*   **API层扩展**:
    *   `POST /prepare_stream`: 接收运营表单数据，触发ETL流程，并返回一个 `config_id`。
    *   `POST /preview_script`: 接收运营表单数据，调用 `ScriptGenerator` 生成一个简短的脚本预览，用于质量评估。

##### 3.2. `LiveStreamController` (职责扩展)

*   **新增职责**:
    1.  **节奏管理**: 在 `start()` 方法中，额外创建 `pace_monitor_task`。
        ```python
        # In LiveStreamController.start()
        self.pace_monitor_task = asyncio.create_task(
            self.pace_monitor.run(self.player)
        )
        ```
    2.  **动态内容**: 在启动时，根据配置决定是使用静态脚本列表还是启动 `ScriptGenerator`。

##### 3.3. `MainContentPlayer` (功能增强)

*   **新增功能**:
    1.  **动态参数调整**: 提供方法以供外部（如`Pace Monitor`）在运行时调整其播放行为。
        ```python
        class MainContentPlayer:
            # ... existing methods ...
            
            def set_pace(self, words_per_minute: int, sentence_pause_ms: int):
                """在运行时调整语速和句间停顿"""
                self.wpm = words_per_minute
                self.sentence_pause = sentence_pause_ms / 1000.0
                logger.info(f"Pace updated: WPM={self.wpm}, Pause={self.sentence_pause}s")
        ```
    2.  **播放源抽象**: `play` 方法接受异步生成器 (`AsyncGenerator`)，使其能消费由 `ScriptGenerator` 动态生成的句子流。

##### 3.4. `QAManager` (内部实现增强)

*   **接口不变，实现升级**:
    *   其 `handle_qa` 方法的内部实现将采用旧设计中先进的 **RAG 策略**。
    *   **检索流程**:
        1.  并行执行 `Elasticsearch` 的 BM25 搜索和向量搜索。
        2.  使用 Reciprocal Rank Fusion (RRF) 算法对两路结果进行融合。
        3.  （可选）将融合后的结果送入一个轻量级的重排模型 (Re-ranker)，得到最相关的上下文。
        4.  将最终上下文和问题一起提交给 LLM 生成答案。

##### 3.5. `ScriptGenerator` (新核心组件)

*   **职责**: 作为动态内容的生产者，实现无限流直播。
*   **功能**:
    1.  **AIDA 模型驱动**: 内部逻辑基于 AIDA 模型 (`Attention`, `Interest`, `Desire`, `Action`) 来组织内容，生成一个个自洽的“微循环”卖点。
    2.  **话术库支持**: 在生成文本时，会从 `Style Corpus` 中检索符合当前人设和场景（如开场、CTA）的话术片段，并将其融入生成内容，确保风格一致性和运营要求。
    3.  **情境感知**: 调用一个简单的 `ContextEnricher` 工具，获取当前的时段（早/中/晚），并将相应的风格提示（如“现在是晚上，语气要更温暖”）加入到 Prompt 中。

#### 4. 新增的独立模块与任务

##### 4.1. `PaceMonitor` (节奏监视器)

*   **角色定位**: 一个独立的、由 `LiveStreamController` 管理的后台 `asyncio.Task`。
*   **职责**:
    1.  定期（例如每30秒）异步请求**观众数API**。
    2.  根据返回的观众人数，查询预设的**节奏映射表** (Pace Mapping Table)。
    3.  调用 `MainContentPlayer.set_pace()` 方法，将新的语速（WPM）和句间停顿参数传递给播放器。
    4.  如果API调用失败，则使用默认节奏。

    ```python
    # Pace Mapping Table (from old design)
    PACE_MAPPING = {
        "0-10": {"wpm": 160, "pause_ms": 400},
        "11-50": {"wpm": 170, "pause_ms": 300},
        "51-200": {"wpm": 180, "pause_ms": 200},
    }

    class PaceMonitor:
        async def run(self, player: MainContentPlayer):
            while True:
                try:
                    viewer_count = await self.get_viewer_count()
                    pace_params = self.get_pace_params(viewer_count)
                    player.set_pace(pace_params['wpm'], pace_params['pause_ms'])
                except Exception as e:
                    logger.warning(f"Pace monitoring failed: {e}")
                
                await asyncio.sleep(30) # 30秒检查一次
    ```

##### 4.2. `InterruptManager` (通用中断管理器)

新设计中的 `InterruptManager` 可以被用来实现旧设计中一些优秀的事件驱动功能。

*   **欢迎新用户 (`Welcome Bot`)**:
    *   可以实现一个 `WelcomeInterruptHandler`。
    *   当系统通过外部接口（如 webhook）收到“新用户加入”事件时，可以调用 `interrupt_manager.request_interrupt`，类型为 `welcome_user`，优先级设为低。
    *   处理器会播放一句简短的欢迎语，不影响主流程。

*   **情景回顾 (`Contextual Recap`)**:
    *   可以实现一个 `RecapInterruptHandler`。
    *   `PaceMonitor` 在检测到观众数有显著增长时（例如，在3分钟内增长超过20%），可以触发一个 `recap_needed` 的中断请求。
    *   处理器会生成一小段回顾（如“欢迎新来的朋友，我们刚才聊到...接下来我们会讲...”），帮助新观众快速融入。

---

### **5. QA 模块知识库集成深度设计**

本章节详细阐述 `QAManager` 与知识库的集成方案，以确保其具备准确、高效的问答能力。

#### **5.1. QAManager 内部架构**

`QAManager` 作为一个独立的 Actor，其内部由几个协作的子组件构成，形成一个完整的 RAG (Retrieval-Augmented Generation) 管道。

```mermaid
graph TD
    subgraph QAManager
        direction LR
        A[handle_qa(question)] --> B{QueryBuilder};
        B --> |es_query| C(KnowledgeRetriever);
        C --> |docs| D{ContextProcessor};
        D --> |prompt| E(LLMAdapter);
        E --> |answer| F[ResponseSynthesizer];
        F --> G[TTSAdapter];
        C --> |检索| H[Elasticsearch];
    end

    style F fill:#f9f,stroke:#333,stroke-width:2px
```

*   **QueryBuilder**: 负责将用户的原始问题，结合当前直播上下文（如 SKU ID），构造成一个发送给 `Elasticsearch` 的结构化查询。
*   **KnowledgeRetriever**: 核心的检索器。它与 `Elasticsearch` 交互，执行混合搜索，并返回原始的文档块。
*   **ContextProcessor**: 对检索到的文档块进行处理，例如重排（re-ranking）、拼接或压缩，形成最终注入到 LLM 提示词中的上下文。
*   **LLMAdapter**: 将处理后的上下文和原始问题格式化为完整的提示词，并调用 LLM 服务获取答案。
*   **ResponseSynthesizer**: （可选）对 LLM 返回的答案进行后处理，例如添加引用来源或进行格式化。
*   **TTSAdapter**: 调用 TTS 服务将最终答案文本转换为音频。

#### **5.2. 知识库查询机制与数据流**

**数据流**:

1.  `LiveStreamController` 调用 `qa_manager.handle_qa(question, context)`. `context` 对象至少包含 `sku_id` 和 `session_id`。
2.  **`QueryBuilder`** 接收到问题和上下文后，执行以下操作：
    *   生成用于向量搜索的查询嵌入。
    *   构建一个包含 `bool` 查询（用于过滤）和 `hybrid` 查询（用于搜索）的 `Elasticsearch` DSL 查询。
3.  **`KnowledgeRetriever`** 将此 DSL 查询发送到 `Elasticsearch`。
    *   `Elasticsearch` 并行执行 BM25（稀疏向量）和 kNN（密集向量）搜索。
    *   `Elasticsearch` 内部使用 RRF 进行结果融合和排序。
4.  `KnowledgeRetriever` 接收到排序后的文档列表。每个文档都符合下面定义的**数据模型**。
5.  **`ContextProcessor`** 接收文档列表：
    *   **重排（可选）**: 如果启用了重排器，它会将问题和每个文档的文本传递给一个轻量级的交叉编码器模型（Cross-Encoder），该模型会为每个文档给出一个更精确的相关性分数。然后根据此分数对文档重新排序。
    *   **上下文构建**: 选择 top-K (例如 K=5) 的文档，并将它们的 `chunk_text` 字段拼接成一个长字符串，作为最终的上下文。
6.  **`LLMAdapter`** 构建最终的提示词，并调用 LLM。
7.  后续流程照常进行。

#### **5.3. 接口与数据模型**

##### **5.3.1. Elasticsearch 索引数据模型**

在 `Prefect ETL` 流程中，所有知识文档都应被处理成以下结构并存入 `Elasticsearch`：

```json
{
  "doc_id": "sku_123_manual.pdf#chunk_7",  // 唯一文档块ID
  "sku_id": "123",                        // 关联的产品SKU
  "brand": "BrandX",                      // 品牌
  "category": "Electronics",              // 分类
  "source_doc": "product_manual.pdf",     // 来源文档名
  "doc_type": "faq|spec|policy|story",    // 文档类型
  "chunk_text": "本产品采用IPX7级防水设计，可在一米深的水中浸泡30分钟...", // 文档块原文
  "embedding": [0.12, -0.45, ...],        // 文本块的向量嵌入
  "created_at": "2024-01-01T12:00:00Z"
}
```
*   `embedding` 字段应被映射为 `dense_vector` 类型。
*   `sku_id`, `brand`, `category`, `doc_type` 等字段应用作关键词过滤（`keyword` type）。

##### **5.3.2. Elasticsearch 查询 DSL 示例**

`QueryBuilder` 生成的查询将类似于：

```json
{
  "query": {
    "hybrid": {
      "queries": [
        {
          "match": {
            "chunk_text": {
              "query": "这个产品防水吗？" 
            }
          }
        },
        {
          "knn": {
            "embedding": {
              "vector": [0.34, -0.56, ...], // "这个产品防水吗？" 的查询向量
              "k": 10
            }
          }
        }
      ]
    }
  },
  "filter": {
    "term": {
      "sku_id": "123" // 根据当前直播的SKU进行过滤
    }
  },
  "_source": ["doc_id", "chunk_text", "source_doc"], // 只返回需要的字段
  "size": 10 // 指定返回的文档数量
}
```

##### **5.3.3. QAManager 构造与初始化**

`QAManager` 的创建应通过依赖注入完成，并从应用配置中读取 `Elasticsearch` 的连接信息。

```python
class QAManager:
    def __init__(self, es_client: AsyncElasticsearch, llm_adapter: LLMAdapter, tts_adapter: TTSAdapter, reranker: Optional[CrossEncoder] = None):
        self.es_client = es_client
        self.llm_adapter = llm_adapter
        self.tts_adapter = tts_adapter
        self.reranker = reranker
        
        # 内部组件初始化
        self.query_builder = QueryBuilder()
        self.retriever = KnowledgeRetriever(self.es_client)
        self.context_processor = ContextProcessor(self.reranker)

    async def handle_qa(self, question: str, context: Dict[str, Any]) -> None:
        # 1. 构建查询
        es_query = self.query_builder.build(question, context)
        
        # 2. 检索
        documents = await self.retriever.search(es_query)
        
        # 3. 处理上下文
        final_context = self.context_processor.process(documents, question)
        
        # 4. 生成答案
        answer_text = await self.llm_adapter.generate(question, final_context)
        
        # 5. 合成语音
        await self.tts_adapter.synthesize(answer_text)

# 在服务启动时进行依赖注入配置
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 创建 Elasticsearch 客户端
    es_client = AsyncElasticsearch(hosts=settings.ES_HOSTS)
    
    # 创建其他服务...
    llm_adapter = LiteLLMAdapter()
    tts_adapter = CosyVoiceTTSAdapter()
    
    # 创建 QAManager 单例
    qa_manager = QAManager(es_client, llm_adapter, tts_adapter)
    
    # 存入 app.state 以供依赖注入使用
    app.state.qa_manager = qa_manager
    
    yield
    
    # 关闭连接
    await es_client.close()
```

#### **5.4. 集成点总结**

-   **离线**: `Prefect ETL` 流程负责填充 `Elasticsearch` 索引。
-   **运行时**: `QAManager` 在初始化时创建 `Elasticsearch` 客户端。
-   **问答流程**: `handle_qa` 方法内部通过 RAG 管道调用 `KnowledgeRetriever` 来执行查询。
-   **上下文注入**: 直播会话的上下文（特别是 `sku_id`）必须在调用 `handle_qa` 时传递进来，用于构建精确的过滤查询。

通过以上详细设计，`QAManager` 的知识库集成方案变得清晰、完整且可执行，确保了 QA 模块能够基于准确的知识来回答用户问题。

### **6. 架构升级：从文本流到结构化指令流**

为了从根本上解决“特殊标记（如 `[价格公布]`）被当成普通文本播报”的问题，并为系统未来的功能扩展（如控制多媒体、触发前端动画等）奠定坚实基础，我们提出一次核心的架构升级：**将直播脚本从单一的“纯文本流”演进为可编排的“结构化指令流”**。

这标志着系统从一个“内容播放器”向一个真正的“直播自动化引擎”的转变。

#### **6.1. 核心思想：行为与内容分离**

我们将不再把直播脚本看作一个长字符串，而是看作一个由不同“动作”组成的有序序列。每个动作要么是需要播报的**内容（Content）**，要么是需要执行的**行为（Behavior）**。

#### **6.2. 新增核心模型**

为了在代码中体现这一思想，我们定义以下数据结构：

```python
# 位于: src/ai_live_streamer/core/models/actions.py (新文件)
from dataclasses import dataclass, field
from typing import Dict, Any, Union, List

@dataclass
class SpeechAction:
    """播报台词的动作"""
    text: str

@dataclass
class EventAction:
    """触发特定行为事件的动作"""
    name: str  # 例如: "price_reveal", "show_image", "play_sound_effect"
    params: Dict[str, Any] = field(default_factory=dict) # 事件参数

# 一个完整的脚本就是这些动作的序列
Script = List[Union[SpeechAction, EventAction]]
```

#### **6.3. 新增组件与职责变更**

##### **6.3.1. 新增：`ScriptParser` (脚本解析器)**

*   **定位**: 核心服务组件，位于 `src/ai_live_streamer/core/script_parser.py`。
*   **职责**: 其唯一职责是将原始的、包含特殊标记的文本脚本，解析成上述定义的 `Script` 对象列表（即 `List[Union[SpeechAction, EventAction]]`）。

**解析逻辑示例**:

*   **输入**: `"来吧，[价格公布] 3元一罐！[播放音效: cheer.mp3]"`
*   **输出**:
    ```python
    [
        SpeechAction(text="来吧，"),
        EventAction(name="price_reveal", params={}),
        SpeechAction(text=" 3元一罐！"),
        EventAction(name="play_sound_effect", params={"file": "cheer.mp3"})
    ]
    ```

##### **6.3.2. 新增：`EventHandler` (事件处理器)**

*   **定位**: 核心服务组件，位于 `src/ai_live_streamer/services/event_handler.py`。
*   **职责**: 接收 `EventAction` 对象，并根据事件名称执行相应的逻辑。这是一个高度可扩展的模块。

**处理逻辑示例**:
*   `price_reveal` 事件: 通过 WebSocket 向前端发送一个JSON消息，通知UI展示价格。
*   `show_image` 事件: 通知前端展示指定图片。
*   `play_sound_effect` 事件: 在音频流中混入指定的音效文件。

##### **6.3.3. 演进：`MainContentPlayer` → `StreamOrchestrator` (流编排器)**

`MainContentPlayer` 的角色发生了根本性变化，建议重命名为 `StreamOrchestrator` 以准确反映其新职责。

*   **旧职责**: 接收文本，切分句子，然后播放。
*   **新职责**:
    1.  接收原始脚本字符串。
    2.  调用 `ScriptParser` 将其解析为 `Action` 序列。
    3.  遍历 `Action` 序列：
        *   如果遇到 `SpeechAction`，则将其文本内容交由 `SemanticSentenceSplitter` 进行断句，并依次送入 `TTS Engine` 播报。
        *   如果遇到 `EventAction`，则将其交由 `EventHandler` 处理。

#### **6.4. 新架构数据流**

```mermaid
graph TD
    A[原始脚本<br/>"来吧, [价格公布] 3元一罐!"] --> B{Script Parser};
    B --> C[动作序列<br/>1. SpeechAction("来吧,")<br/>2. EventAction("price_reveal")<br/>3. SpeechAction("3元一罐!")];
    C --> D{Stream Orchestrator};
    
    subgraph "Orchestrator's Flow"
        D -- SpeechAction --> E[Semantic Splitter];
        E --> F[TTS Engine];
        F --> G((音频流));
        D -- EventAction --> H{Event Handler};
        H --> I[WebSocket/API<br/>(通知前端/其他服务)];
    end
```

#### **6.5. 优势总结**

1.  **高度可扩展**: 新增功能（如 `[发起投票]`）只需增加新的 `EventAction` 类型并在 `EventHandler` 中实现即可，不影响核心编排逻辑。
2.  **职责清晰**: `Parser` 只管解析，`Orchestrator` 只管调度，`EventHandler` 只管执行具体行为。代码更清晰、更易于维护和测试。
3.  **强大的表现力**: 脚本编写者可以通过简单的标签，精确控制直播过程中的多媒体和交互事件，极大地丰富了直播的自动化能力和表现力。
4.  **问题根除**: 从设计上彻底杜绝了将“指令”当作“内容”来播报的问题。

---

### **7. 健壮性增强：实现播放状态的闭环确认 (ACK机制)**

“结构化指令流”极大地提升了系统的表现力，但要确保指令（特别是需要与语音同步的事件）的精确执行，我们必须解决一个关键问题：**后端如何确切地知道前端音频已经播放完毕？**

当前“即发即忘”的模式存在风险：网络延迟、客户端缓冲、浏览器暂停等因素都可能导致后端对播放状态的判断与实际情况不符。为了实现广播级的可靠性，我们引入客户端ACK（Acknowledge）确认机制，构建一个从发送到播放完成的完整闭环。

#### **7.1. 核心思想：从“发射后不管”到“双向确认”**

我们将引入一个轻量级的双向信令协议，允许 `StreamOrchestrator` 在执行关键动作前后，等待来自客户端的“完成回执”。

#### **7.2. 协议与数据模型**

##### **7.2.1. 后端 -> 前端：音频块标记**

后端在通过 WebSocket 发送每一段有意义的音频数据块（例如，一个完整的句子或一个短语）时，需要附加一个唯一的ID。

```json
// WebSocket message from backend
{
  "type": "audio_chunk",
  "chunk_id": "speech_action_1_sentence_2", // 唯一ID
  "data": "base64-encoded-audio-data"
}
```

##### **7.2.2. 前端 -> 后端：播放完成确认**

前端的音频播放器（如 `web_audio_player.js`）维护一个待播放队列。当它 **完全播放完** 一个带有 `chunk_id` 的音频块后，立刻通过 WebSocket 发回一个确认消息。

```json
// WebSocket message from frontend
{
  "type": "playback_finished",
  "chunk_id": "speech_action_1_sentence_2" 
}
```

#### **7.3. 组件职责变更**

##### **7.3.1. `StreamOrchestrator` (职责增强)**

`StreamOrchestrator` 的执行逻辑需要升级，以管理和等待ACK。

```python
# In StreamOrchestrator

class StreamOrchestrator:
    def __init__(self, ..., audio_websocket_manager):
        # ...
        self.pending_acks = {} # 用于存储待确认的 chunk_id 及其 asyncio.Event

    async def _process_speech_action(self, action: SpeechAction):
        sentences = self.sentence_splitter.split(action.text)
        for i, sentence in enumerate(sentences):
            chunk_id = f"{action.id}_sentence_{i}" # 为每句话生成唯一ID
            
            # 1. 创建一个 Event 用于等待 ACK
            ack_event = asyncio.Event()
            self.pending_acks[chunk_id] = ack_event
            
            # 2. 发送带ID的音频块到前端
            await self.tts_engine.synthesize_and_send(sentence, chunk_id)
            
            # 3. 异步等待ACK，并设置超时
            try:
                await asyncio.wait_for(ack_event.wait(), timeout=15.0) # 设置15秒超时
                logger.info(f"ACK received for {chunk_id}.")
            except asyncio.TimeoutError:
                logger.warning(f"Timeout waiting for ACK on {chunk_id}. Moving on.")
            finally:
                del self.pending_acks[chunk_id]

    async def _process_event_action(self, action: EventAction):
        # 对于需要严格同步的事件，现在可以确保它在前一句语音后执行
        await self.event_handler.handle(action)

    def handle_ack(self, chunk_id: str):
        """由 WebSocket 管理器调用，用于接收ACK并设置事件"""
        if chunk_id in self.pending_acks:
            self.pending_acks[chunk_id].set()

```

##### **7.3.2. 前端 `WebAudioPlayer` (职责增强)**

前端播放器需要实现：
1.  接收 `audio_chunk` 类型的消息。
2.  将 `chunk_id` 和音频数据一同放入播放队列。
3.  在一个音频块播放结束后，立即取出其 `chunk_id` 并通过 WebSocket 发送 `playback_finished` 消息。

#### **7.4. 新架构数据流 (含ACK)**

```mermaid
sequenceDiagram
    participant SO as StreamOrchestrator (Backend)
    participant EP as Audio WebSocket (Backend)
    participant WP as Web Player (Frontend)

    SO->>+EP: send(audio_chunk, chunk_id="s1")
    EP-->>WP: WebSocket push: {type: "audio_chunk", chunk_id: "s1", data: ...}
    SO->>SO: await self.pending_acks["s1"].wait()
    
    Note right of WP: Player starts playing audio for "s1"
    
    ... a few seconds later ...
    
    Note right of WP: Playback for "s1" finishes
    WP-->>-EP: WebSocket send: {type: "playback_finished", chunk_id: "s1"}
    EP->>SO: handle_ack("s1")
    
    Note left of SO: ack_event for "s1" is set, await completes
    
    SO->>SO: Proceed to next action (e.g., EventAction or next SpeechAction)
```

#### **7.5. 优势总结**

1.  **精确同步**: 完美解决了音画/音事同步问题，确保 `EventAction` 在正确的时机触发。
2.  **极高可靠性**: 系统流程不再依赖于猜测和 `asyncio.sleep`，而是基于确切的客户端状态。
3.  **实时错误检测**: 超时机制能迅速发现客户端的潜在问题（断连、卡死），为后续的重试或报警策略提供可能。
4.  **数据洞察**: 为分析每个语音片段的实际送达率和播放完成情况提供了坚实的数据基础。

