## 背景与问题

当前系统中“卖点/买点”配置散落在运营配置（`operational_forms`）中，作为“卖点结构（`SellingPointsStructure`）”的一部分进行收集与保存。这带来几类问题：

- **数据归属不清**：卖点是商品的固有属性，但被放在“配置管理”里，导致认知错位与复用困难。
- **配置成本高**：每次创建直播配置都要重复输入/粘贴卖点，容易遗漏或不一致。
- **难以沉淀与分析**：卖点无法在商品维度沉淀，后续做统计、A/B、命中率分析成本高。

代码与文档迹象：

- 前端运营表单模板含“卖点结构”配置（`src/ai_live_streamer/api/templates/operational_form.html`）。
- 脚本生成/预览逻辑基于表单中的 `SellingPointsStructure`（`services/script_previewer.py`、`services/llm_script_generator.py`）。
- 数据库当前无独立“商品卖点”表（`scripts/init_product_database.py`、`services/persistence/database_manager.py`）。


## 目标与原则

- **目标**：将“卖点/买点”作为“商品”的一等子资源进行管理；直播配置仅选择商品并（可选）勾选/排序该商品的已有卖点。
- **原则**：
  - 数据单一事实来源（SSOT）在“商品侧”。
  - 对现有运营表单兼容（保留覆盖能力，但默认走商品卖点）。
  - 渐进迁移、可回滚。


## 范围

- 数据库：新增商品卖点表；给直播配置增加“已选卖点ID列表（可选）”。
- 后端：商品服务新增 CRUD；脚本生成读取路径调整；运营表单存储与读取逻辑调整。
- 前端：商品管理页支持卖点维护；运营表单“卖点结构”切换为“引用商品卖点 + 增量覆盖”。


## 数据模型与表结构设计

### 新增表：`product_selling_points`

```sql
CREATE TABLE IF NOT EXISTS product_selling_points (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  product_id INTEGER NOT NULL,
  title TEXT NOT NULL,              -- 卖点标题（短语）
  description TEXT,                 -- 卖点详细说明
  supporting_facts TEXT,            -- JSON 数组：支撑事实/证据
  priority INTEGER DEFAULT 3,       -- 1-高, 2-中, 3-低（可扩展为枚举表）
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_psp_product ON product_selling_points(product_id);
CREATE INDEX IF NOT EXISTS idx_psp_active ON product_selling_points(product_id, is_active);
```

字段说明：

- **supporting_facts**：使用 JSON 数组，便于前端/脚本侧直接消费。
- **priority**：与现有 `PriorityLevel` 对齐，便于脚本时长/详尽度分配。

### 直播配置表增量（可选）

在 `streaming_configs` 中新增可选字段，记录“本次配置选择的卖点及顺序”。

```sql
ALTER TABLE streaming_configs
ADD COLUMN selected_selling_point_ids TEXT;  -- JSON 数组，存放 psp.id 列表与顺序
```

说明：

- 若为空，运行时从 `product_selling_points` 读取默认“启用的卖点”，并按 `priority` 与 `updated_at` 排序。
- 若不为空，则严格按配置指定的 ID 顺序使用，可用于场次级编排。


## 后端服务与接口变更

### 新增：商品卖点 CRUD 接口（示例）

- `GET /api/products/{product_id}/selling-points`
- `POST /api/products/{product_id}/selling-points`
- `PUT /api/products/{product_id}/selling-points/{id}`
- `DELETE /api/products/{product_id}/selling-points/{id}`

数据模型（简化 Pydantic 示意）：

```python
class ProductSellingPoint(BaseModel):
    id: Optional[int]
    product_id: int
    title: str
    description: Optional[str]
    supporting_facts: List[str] = []
    priority: int = 3
    is_active: bool = True
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
```

服务层：

- 在 `ProductService` 或 `SimpleProductService` 增加对应方法：
  - `list_selling_points(product_id)`
  - `create_selling_point(product_id, data)`
  - `update_selling_point(id, data)`
  - `delete_selling_point(id)`

### 读取路径调整

- `services/llm_script_generator.py` 与 `services/script_previewer.py`：
  - 默认从商品卖点表读取（基于配置的 `product_id`）。
  - 若 `streaming_configs.selected_selling_point_ids` 有值，则按其顺序挑选对应卖点。
  - 若运营表单提供“临时覆盖的卖点”（见下文兼容策略），则合并并优先使用覆盖项。


## 前端交互与页面改造

- 商品管理（`admin_console`）：
  - 在商品详情页新增“卖点”标签页，支持增删改、拖动排序、启用/禁用。
  - 支持批量导入（CSV/JSON）与一键复制到剪贴板。

- 运营表单（`operational_form.html`）：
  - “卖点结构”改为：
    1) 默认展示“当前已选商品”的卖点清单（可勾选/排序）。
    2) 提供“临时新增卖点”入口（仅对本场次有效，不回写商品）。
  - 表单提交时，仅保存“所选卖点ID顺序”和“临时卖点（JSON）”。


## 兼容与迁移策略

- 存量表单数据：
  - 扫描 `operational_forms.section3`（卖点结构）。若绑定了商品且商品侧无相同卖点，则将其迁移为 `product_selling_points`（去重、归一化）。
  - 迁移后，原表单保留“临时卖点”仅作为覆盖；默认使用商品侧卖点。

- 在线灰度：
  - 双写期：商品卖点与表单卖点同时可用；生成逻辑以“配置的已选ID + 临时卖点”优先。
  - 灰度完成后，逐步隐藏旧表单的“完整卖点编辑”，仅保留“临时新增/覆盖”。

- 回滚：
  - 保留原读取路径的开关（环境变量或配置项），可在紧急情况下切回“完全使用表单卖点”。


## 风险与缓解

- 数据不一致风险：新增唯一性校验（同商品下 `title` + 归一描述哈希去重）。
- 顺序与优先级冲突：顺序以配置场次为准，缺失则按 `priority` 及 `updated_at`。
- 前后端版本错配：接口增加版本号或能力检测；前端按接口返回能力渲染。


## 验收标准（DoD）

- 商品详情页可完整维护卖点，支持排序与启停。
- 新建/编辑直播配置时，商品卖点自动加载，可勾选/排序；不编辑也能跑通。
- 脚本预览页统计“卖点展示数”与链路正确，生成逻辑使用所选卖点。
- 迁移脚本对存量数据迁移正确，去重有效，有迁移日志与回滚方案。
- E2E 覆盖：
  - 选择商品 -> 自动带出卖点 -> 生成脚本 OK。
  - 配置自定义顺序与子集 -> 生效。
  - 添加临时卖点 -> 本场次可见，商品侧不污染。


## 任务拆分与工期估算（参考）

- 数据层与迁移脚本：1d
- 服务层 CRUD 与缓存：1d
- 脚本生成/预览读取路径改造：1d
- 前端商品页卖点编辑与表单改造：2d
- E2E 与灰度、观测：1-2d


## 附录：迁移脚本要点（伪代码）

```sql
-- 1) 创建新表
-- 见上文 product_selling_points DDL

-- 2) streaming_configs 增量字段（若不存在）
ALTER TABLE streaming_configs
ADD COLUMN selected_selling_point_ids TEXT;
```

```python
# 3) 从 operational_forms 迁移：
for form in forms:
    product_id = form.product_id
    if not product_id: 
        continue
    for sp in form.selling_points_structure.selling_points:
        if not exists_same_sp(product_id, sp):
            insert into product_selling_points(...)
```

```json
// 4) streaming_configs.selected_selling_point_ids 示例
[12, 5, 7, 9]
```


## 配置项与开关

- `FEATURE_USE_PRODUCT_SELLING_POINTS=true`：启用新路径。
- `FEATURE_ALLOW_FORM_SP_OVERRIDE=true`：允许表单临时卖点覆盖。


## 变更影响清单（代码热区）

- 模型/存储：`src/ai_live_streamer/models`、`services/persistence/*`、`scripts/init_product_database.py`、`scripts/migrations/*`
- 服务：`src/ai_live_streamer/services/product_service.py`、`services/simple_product_service.py`
- 生成链路：`services/llm_script_generator.py`、`services/script_previewer.py`
- 前端：`api/templates/admin_console.html`、`api/templates/operational_form.html`、`api/templates/script_preview.html`


## 结论

将“卖点/买点”回归商品域是一次模型正本清源的调整。它能显著降低运营配置成本、提升数据复用与分析能力，并为后续的素材管理、A/B 测试与效果归因铺平道路。


