# 核心路径测试框架使用指南

## 概述

本文档描述了 AI Live Streamer 项目的核心路径测试框架，该框架专门设计用于防止 AttributeError 等集成错误，确保代码质量和系统稳定性。

### 设计理念

- **确定性环境**：通过 Docker 隔离确保测试结果可复现
- **快速反馈**：使用 FastAPI TestClient 进行内存测试，速度快
- **信息最大化**：详细的错误输出便于快速定位问题
- **自动化友好**：与 pre-commit 集成，在提交前自动运行

## 快速开始

### 1. 安装依赖

```bash
# 安装 Python 开发依赖
pip install -e ".[dev]"

# 安装 pre-commit
pip install pre-commit

# 初始化 pre-commit 钩子
pre-commit install
```

### 2. 运行测试

#### 方式一：本地快速验证（无需 Docker）

```bash
# 运行快速验证脚本
python tests/quick_verify_fix.py

# 或使用 pytest 运行核心测试
python -m pytest tests/test_critical_path_e2e.py -v
```

#### 方式二：Docker 隔离环境测试（推荐）

```bash
# 运行核心测试脚本
bash scripts/run_core_test.sh
```

#### 方式三：手动运行 Docker 测试

```bash
# 构建并运行测试
docker-compose -f docker-compose.test.yml up --build

# 清理环境
docker-compose -f docker-compose.test.yml down -v
```

## 测试覆盖范围

### 核心测试文件

- `tests/test_critical_path_e2e.py` - 核心路径集成测试
- `tests/quick_verify_fix.py` - 快速验证脚本

### 测试内容

1. **PlaylistItem 属性验证**
   - 验证 `content` 属性存在且可访问
   - 确保没有错误的 `text` 属性
   - 防止 AttributeError: 'PlaylistItem' object has no attribute 'text'

2. **StreamingContentProvider 集成测试**
   - 测试内容请求处理流程
   - 验证属性访问的正确性
   - 确保日志代码不会触发 AttributeError

3. **API 端点测试**
   - 测试流启动 API
   - 验证请求响应格式

4. **WebSocket 协议验证**
   - 测试消息序列化
   - 验证协议兼容性

5. **模块导入测试**
   - 确保所有关键模块可以正确导入
   - 检测循环依赖问题

## 文件结构

```
ai-live-streamer/
├── tests/
│   ├── test_critical_path_e2e.py    # 核心路径测试
│   ├── quick_verify_fix.py          # 快速验证脚本
│   └── results/                      # 测试结果输出目录
│       ├── test_output.log          # 测试日志
│       ├── junit.xml                # JUnit 格式报告
│       └── report.html              # HTML 测试报告
├── scripts/
│   └── run_core_test.sh            # Docker 测试脚本
├── docker-compose.test.yml         # Docker 测试配置
├── Dockerfile                       # 多阶段构建文件
└── .pre-commit-config.yaml         # Pre-commit 配置
```

## Docker 配置说明

### Dockerfile（多阶段构建）

- **base**: 基础镜像，包含生产依赖
- **test**: 测试镜像，包含开发和测试依赖
- **development**: 开发环境
- **production**: 生产环境

### docker-compose.test.yml

- 使用 `test` 阶段构建镜像
- 挂载源代码为只读，防止意外修改
- 自动运行测试并生成报告
- `restart: "no"` 确保失败不会被掩盖

## Pre-commit 集成

### 配置说明

`.pre-commit-config.yaml` 包含以下钩子：

1. **代码格式化**（Black）
2. **代码检查**（Ruff）
3. **核心路径测试**（Docker 隔离）
4. **语法检查**（Python 编译）

### 触发条件

核心路径测试仅在修改以下目录的文件时触发：
- `src/ai_live_streamer/services/`
- `src/ai_live_streamer/models/`
- `src/ai_live_streamer/api/`
- `src/ai_live_streamer/core/`

### 跳过检查（紧急情况）

```bash
# 不推荐：跳过所有 pre-commit 检查
git commit --no-verify -m "紧急修复"
```

**注意**：作为 AI 执行者或负责任的开发者，应避免使用 `--no-verify`。

## 测试脚本详解

### scripts/run_core_test.sh

该脚本执行以下步骤：

1. **检查 Docker** - 确保 Docker 正在运行
2. **清理旧容器** - 移除之前的测试容器
3. **构建镜像** - 使用 Dockerfile 的 test 阶段
4. **运行测试** - 执行 pytest 并生成报告
5. **清理环境** - 删除测试容器和卷
6. **显示结果** - 自动输出错误日志（如果失败）

### 关键特性

- **彩色输出** - 使用 ANSI 颜色代码提高可读性
- **自动日志输出** - 失败时自动显示最后 50 行错误
- **执行时间统计** - 显示测试总耗时
- **详细报告** - 生成 JUnit XML 和 HTML 报告

## 故障排查

### 常见问题

#### 1. Docker 未运行

```bash
❌ Docker 未运行，请先启动 Docker
```

**解决方案**：启动 Docker Desktop 或 Docker 服务

#### 2. 端口冲突

如果 8000 端口被占用，修改 `docker-compose.test.yml` 中的端口映射。

#### 3. 权限问题

```bash
# 添加执行权限
chmod +x scripts/run_core_test.sh
```

#### 4. 依赖问题

```bash
# 重新安装依赖
pip install -e ".[dev]" --force-reinstall
```

### 调试建议

1. **查看完整日志**
   ```bash
   cat tests/results/test_output.log
   ```

2. **查看 HTML 报告**
   ```bash
   open tests/results/report.html
   ```

3. **运行单个测试**
   ```bash
   pytest tests/test_critical_path_e2e.py::TestCriticalPath::test_playlist_item_attributes -vv
   ```

4. **进入测试容器**
   ```bash
   docker-compose -f docker-compose.test.yml run test-runner /bin/bash
   ```

## AI 执行指令

作为 AI 执行者，请遵循以下工作流程：

### 提交前工作流

```python
def commit_workflow():
    # 1. 运行测试
    result = run("bash scripts/run_core_test.sh")
    
    # 2. 如果失败，分析并修复
    while result.exit_code != 0:
        # 错误已在 stdout 中
        analyze_error(result.stdout)
        fix_code()
        result = run("bash scripts/run_core_test.sh")
    
    # 3. 测试通过后提交
    run("git add -A")
    run("git commit -m 'message'")  # 永不使用 --no-verify
```

### 核心原则

1. **永不跳过测试** - 不使用 `--no-verify`
2. **测试失败立即修复** - 不提交失败的代码
3. **保持环境纯净** - 使用 Docker 隔离
4. **详细记录** - 保存所有测试日志

## 性能指标

### 测试执行时间

- **本地快速验证**：< 2 秒
- **Docker 测试（首次）**：30-60 秒（包含镜像构建）
- **Docker 测试（后续）**：5-10 秒

### 优化建议

1. **使用缓存** - Docker 会缓存镜像层
2. **并行执行** - pytest 支持 `-n` 参数并行运行
3. **选择性测试** - 仅运行相关测试

## 扩展和维护

### 添加新测试

1. 在 `test_critical_path_e2e.py` 中添加新的测试方法
2. 遵循现有的命名规范：`test_<功能描述>`
3. 添加清晰的文档字符串

### 更新 Docker 配置

1. 修改 `Dockerfile` 添加新的依赖
2. 更新 `docker-compose.test.yml` 调整测试参数
3. 重新构建镜像：`docker-compose -f docker-compose.test.yml build`

### 维护 Pre-commit

```bash
# 更新 pre-commit 钩子
pre-commit autoupdate

# 手动运行所有钩子
pre-commit run --all-files

# 清理缓存
pre-commit clean
```

## 总结

这个测试框架提供了一个可靠、快速、自动化的方式来防止代码回归错误，特别是 AttributeError 类型的问题。通过 Docker 隔离和 pre-commit 集成，确保每次提交的代码都经过严格验证。

### 关键优势

- ✅ **防止 AttributeError** - 核心测试专门检查属性访问
- ✅ **环境隔离** - Docker 确保测试结果可复现
- ✅ **快速反馈** - 内存测试速度快
- ✅ **自动化** - 与 git 工作流无缝集成
- ✅ **详细报告** - 多种格式的测试报告

### 最佳实践

1. 每次修改核心代码后运行测试
2. 提交前确保所有测试通过
3. 定期更新测试用例
4. 保持测试简单快速
5. 使用 Docker 确保环境一致性

---

*文档创建日期：2025-08-10*
*作者：Claude Code (AI Assistant)*
*版本：1.0.0*