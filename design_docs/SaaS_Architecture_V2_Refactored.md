# AI Live Streamer: 可商业化SaaS平台架构设计

**版本**: 2.0 (重构版)
**日期**: 2025-08-13
**作者**: AI架构师

---

## 1. 业务愿景与架构目标

### 1.1. 业务目标
- **多租户支持**: 将系统从单用户模式演进为支持多个独立客户（租户）的SaaS平台。
- **商业化基础**: 建立灵活、可靠的计费与配额管理体系，支持商业化运营。
- **高可用与可扩展**: 确保系统在高并发下稳定运行，并能随业务增长平滑扩展。

### 1.2. 架构设计原则
- **控制/数据平面分离**: 集中管理租户、计费、认证；独立处理业务数据，确保扩展性与安全。
- **零信任安全**: 所有访问都需经过强认证，不信任任何网络环境。
- **金融级计量**: 计费数据准确、不可抵赖、可审计。
- **最小化改造**: 在现有代码基础上渐进式演进，降低风险，快速迭代。

## 2. 总体架构：混合存储模型

本架构采用 **控制平面** 与 **数据平面** 分离的设计，是现代SaaS平台的基石。

- **控制平面 (MySQL)**: 系统的“大脑”和“账房”，集中管理所有租户的元数据、认证、授权和计费。
  - **组件**: 高可用MySQL集群 (推荐使用RDS)。
  - **职责**: 租户管理、套餐与价格、API密钥、用量台账、配额余额。

- **数据平面 (SQLite)**: 系统的“肌肉”，专注于高效处理每个租户的隔离业务数据。
  - **组件**: 每租户一个独立的SQLite数据库文件。
  - **职责**: 商品信息、问答对、直播脚本、表单数据等。

```mermaid
graph TD
    subgraph "用户/API请求"
        A[Web/Mobile Client] --> B{API Gateway / LB};
        C[API/SDK Client] --> B;
    end

    subgraph "控制平面 (Central MySQL)"
        B -- "认证/授权" --> D[Auth Service (API Key)];
        D -- "Tenant/Plan Info" --> F[Tenant & Plan DB];
        B -- "业务请求" --> E[App Service];
        E -- "读写配置/计费" --> F;
        E -- "检查/更新配额" --> G[Quota & Billing DB];
        H[Metering Service] -- "记录用量" --> G;
    end
    
    subgraph "数据平面 (Per-Tenant SQLite)"
        E -- "读写业务数据 (tenant_id)" --> I{SQLite DB Manager};
        I --> J1["SQLite DB (Tenant A)"];
        I --> J2["SQLite DB (Tenant B)"];
        I --> J3["..."];
    end

    E -- "异步发送计量事件" --> K((Message Queue));
    K --> H;

    style F fill:#cde4ff,stroke:#36c,stroke-width:2px
    style G fill:#cde4ff,stroke:#36c,stroke-width:2px
    style J1 fill:#d5f5e3,stroke:#27ae60,stroke-width:2px
    style J2 fill:#d5f5e3,stroke:#27ae60,stroke-width:2px
    style J3 fill:#d5f5e3,stroke:#27ae60,stroke-width:2px
```

## 3. 认证与授权 (AAA)

采用基于API Key的零信任认证模型。

### 3.1. 认证流程
1.  **密钥颁发**: 系统为每个租户生成一个或多个API Key，包含`前缀`和`密钥主体`。
2.  **密钥存储**: 数据库中仅存储`前缀`和`密钥主体的哈希值 (bcrypt/argon2)`。
3.  **请求认证**: 客户端在HTTP Header中携带密钥: `Authorization: Bearer <API_KEY>`。
4.  **后端验证**: 
    - 服务端通过`前缀`快速查找租户。
    - 验证`密钥主体`与存储的哈希值是否匹配。
    - 验证通过后，将`tenant_id`、`plan_id`等信息注入请求上下文。

### 3.2. Web端集成
- **登录**: 提供登录页面，租户输入API Key完成认证。认证成功后，API Key存储在浏览器的`localStorage`中，实现会话持久化。
- **短期令牌**: 对于WebSocket等场景，后端基于API Key绑定的租户信息，生成一个有时效性（如60秒）的`session_token`，避免在URL中暴露长效密钥。
- **安全**: 全程使用HTTPS，API Key不应在URL参数中传递。

## 4. 计费与配额管理

### 4.1. 计费模型：预付费时长包
- **核心逻辑**: 租户预先购买直播时长包（如“1000分钟标准包”），直播时实时消耗已购时长。
- **灵活性**: 支持创建多种价格、不同有效期的时长包。
- **状态管理**: 租户余额不足时，禁止启动新的直播。

### 4.2. 实时配额检查 (Gating)
为防止超额使用并保证系统性能，采用 **Redis + MySQL** 混合策略进行实时检查。
1.  **高速预检 (Redis)**: 
    - 直播开始前，在Redis中对租户的月度用量计数器执行原子增操作 (`INCRBY`)。
    - 99%的情况下，Redis的检查结果能快速决定是否允许操作。
2.  **权威校准 (MySQL)**: 
    - 后台任务每分钟从MySQL的`UsageAggregates`表中获取精确用量。
    - 用此精确值重置（`SET`）Redis中的计数器，确保数据最终一致性。

### 4.3. 金融级用量计量
- **事件溯源**: 所有计费相关的操作（如直播开始/结束）都生成不可变的`计量事件`。
- **数据完整性**: 
  - **哈希链**: 每个事件都包含前一个事件的哈希值 (`prev_event_hash`)，形成防篡改链条。
  - **数字签名**: 服务端使用HMAC密钥对事件签名，确保事件不可伪造。
- **异步处理**: 计量事件通过消息队列（如Redis Streams）异步写入数据库，不阻塞主业务流程。

## 5. 数据库设计 (MySQL控制平面)

### 5.1. 核心表结构清单
- `tenants`: 租户主表。
- `plans`: 商业套餐表。
- `api_keys`: API密钥表。
- `pricing_packages`: 可购买的时长包定义。
- `quota_balances`: 租户配额余额表。
- `quota_purchases`: 购买历史记录。
- `usage_ledger`: 用量台账 (事件溯源)。
- `usage_aggregates`: 聚合用量统计。

*(详细的CREATE TABLE SQL语句请参考附录A)*

## 6. 跨领域架构关注点

### 6.1. 安全设计
- **数据隔离**: 控制平面通过`tenant_id`实现行级逻辑隔离；数据平面通过独立SQLite文件实现物理隔离。
- **纵深防御**: API Key认证 + Session Token双重验证。
- **防篡改**: 计量事件的哈希链与数字签名。
- **防范常见攻击**: 遵循OWASP Top 10，防范SQL注入、CSRF、XSS等。

### 6.2. 配置管理
- **单一事实源**: 所有与租户相关的、可变的配置（如功能开关、配额、TTS音色）全部迁移到控制平面的`plans`和`tenants`表的JSON字段中。
- **静态配置**: `config.yml`仅保留数据库连接信息、日志级别等系统级静态配置。

### 6.3. 可观测性 (日志、指标、告警)
- **日志**: 所有日志必须包含`tenant_id`和`session_id`等结构化信息。
- **指标 (Metrics)**: 按租户维度暴露关键业务指标（如QPS、延迟、错误率、直播时长）。
- **告警**: 
  - **业务告警**: 租户配额低于阈值、余额耗尽。
  - **系统告警**: 计量哈希链断裂、数据库连接池耗尽、消息队列积压。

### 6.4. 异常与边界处理
- **幽灵播放**: 当一个会话的所有客户端都断开连接后，进入一个短暂的“等待重连”宽限期（如30-60秒），期间不计费。若客户端重连，则会话恢复；若超时，则正式结束会话，结算最终用量。
- **服务重启**: 重启后，通过查询`BillingSessions`表恢复未正常结束的会话状态，进行用量校对。

## 7. 实施与迁移路线图

采用分阶段、低风险的策略进行迁移和上线。

### 7.1. Phase 1: 控制平面搭建与认证升级
- **动作**: 部署MySQL/Redis；创建DB schema；实现API Key认证服务。
- **策略**: 采用影子认证模式，同时接受旧`X-Tenant-ID`和新`Authorization`头，为平滑过渡做准备。

### 7.2. Phase 2: 计量系统插桩与数据采集
- **动作**: 在业务代码中植入计量事件的生成逻辑。
- **策略**: 仅通过消息队列异步采集数据到`usage_ledger`，不执行任何计费或配额限制。

### 7.3. Phase 3: 配额系统上线 (观测模式)
- **动作**: 部署配额检查逻辑。
- **策略**: 只读模式，仅记录检查结果，不实际拦截请求。运行2-4周，观测准确性与性能。

### 7.4. Phase 4: 预付费功能上线与灰度启用
- **动作**: 开发套餐购买、支付集成和余额管理界面。
- **策略**: 对新注册或自愿参与的租户，正式启用配额限制（硬拒绝）。

### 7.5. Phase 5: 全面切换
- **动作**: 引导所有老租户迁移至新模式。
- **策略**: 在明确的截止日期后，停止对旧认证方式的支持。

---
## 附录A: 核心表结构SQL

```sql
-- 租户主表
CREATE TABLE tenants (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id VARCHAR(64) NOT NULL UNIQUE,
  name VARCHAR(128) NOT NULL,
  status ENUM('active','suspended','cancelled') NOT NULL DEFAULT 'active',
  plan_id BIGINT NOT NULL,
  config_overrides JSON COMMENT '租户级功能配置覆盖',
  created_at DATETIME(6) NOT NULL,
  updated_at DATETIME(6) NOT NULL
) ENGINE=InnoDB;

-- 商业套餐表
CREATE TABLE plans (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(64) NOT NULL UNIQUE,
  feature_config JSON COMMENT '套餐默认功能配置',
  created_at DATETIME(6) NOT NULL,
  updated_at DATETIME(6) NOT NULL
) ENGINE=InnoDB;

-- API密钥表
CREATE TABLE api_keys (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  key_prefix VARCHAR(16) NOT NULL UNIQUE,
  hashed_key VARBINARY(128) NOT NULL COMMENT 'bcrypt/argon2 hash',
  status ENUM('active','revoked') NOT NULL DEFAULT 'active',
  last_used_at DATETIME(6) NULL,
  created_at DATETIME(6) NOT NULL,
  updated_at DATETIME(6) NOT NULL
) ENGINE=InnoDB;

-- 价格套餐表 (时长包)
CREATE TABLE pricing_packages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '"标准包1000分钟"',
    minutes_included BIGINT NOT NULL,
    price_cents BIGINT NOT NULL,
    valid_days INT NOT NULL DEFAULT 365,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME(6) NOT NULL
);

-- 租户配额余额表
CREATE TABLE quota_balances (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  quota_type ENUM('live_minutes') NOT NULL DEFAULT 'live_minutes',
  total_purchased BIGINT NOT NULL DEFAULT 0,
  total_consumed BIGINT NOT NULL DEFAULT 0,
  available_balance BIGINT NOT NULL DEFAULT 0,
  last_updated DATETIME(6) NOT NULL,
  version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本',
  UNIQUE KEY uk_tenant_quota(tenant_id, quota_type)
) ENGINE=InnoDB;

-- 购买历史记录表
CREATE TABLE quota_purchases (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  package_id BIGINT NULL,
  minutes_purchased BIGINT NOT NULL,
  price_paid_cents BIGINT NOT NULL,
  expires_at DATETIME(6) NOT NULL,
  status ENUM('active','expired','consumed') DEFAULT 'active',
  purchase_order_id VARCHAR(64) NULL,
  created_at DATETIME(6) NOT NULL
) ENGINE=InnoDB;

-- 用量台账 (事件溯源)
CREATE TABLE usage_ledger (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  event_id VARCHAR(64) NOT NULL UNIQUE COMMENT '幂等键',
  tenant_id BIGINT NOT NULL,
  session_id VARCHAR(64) NOT NULL,
  metric_type ENUM('live_seconds') NOT NULL,
  amount BIGINT NOT NULL COMMENT '单位:秒',
  occurred_at DATETIME(6) NOT NULL,
  prev_event_hash VARBINARY(64) NULL,
  event_hash VARBINARY(64) NOT NULL,
  signature VARBINARY(128) NOT NULL,
  key_version INT NOT NULL DEFAULT 1
) ENGINE=InnoDB;

-- 聚合用量统计
CREATE TABLE usage_aggregates (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL,
  period DATE NOT NULL COMMENT '聚合周期，如YYYY-MM-01',
  metric_type ENUM('live_seconds') NOT NULL,
  value BIGINT NOT NULL,
  version INT NOT NULL DEFAULT 1,
  computed_at DATETIME(6) NOT NULL,
  UNIQUE KEY uk_tenant_period_metric(tenant_id, period, metric_type)
) ENGINE=InnoDB;

```
