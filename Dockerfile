# 多阶段构建 Dockerfile
# 支持基础、测试和生产环境

# ========== 基础阶段 ==========
FROM python:3.11-slim as base

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app/src \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY pyproject.toml .
COPY . .

# 安装基础 Python 依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -e .

# ========== 测试阶段 ==========
FROM base as test

# 安装测试依赖
RUN pip install --no-cache-dir -e ".[dev]" && \
    pip install --no-cache-dir pytest-html

# 创建测试结果目录
RUN mkdir -p /app/tests/results

# 默认命令：运行核心测试
CMD ["pytest", "tests/test_critical_path_e2e.py", "-vv"]

# ========== 开发阶段 ==========
FROM base as development

# 安装开发依赖
RUN pip install --no-cache-dir -e ".[dev]"

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 8000

# 开发服务器命令
CMD ["uvicorn", "ai_live_streamer.app_v2:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# ========== 生产阶段 ==========
FROM base as production

# 只复制必要的源代码（不包括测试和开发文件）
COPY src/ /app/src/
COPY config/ /app/config/

# 创建非root用户
RUN useradd -m -u 1000 appuser && \
    chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8000

# 生产服务器命令
CMD ["uvicorn", "ai_live_streamer.app_v2:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]

# ========== 健康检查（所有环境通用） ==========
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1